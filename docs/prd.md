# Ziarah Product Requirements Document (PRD)

## Goals and Background Context

### Goals

- Deliver comprehensive pilgrimage assistance through GPS-guided ritual counting for Tawaf and Sa'i, enabling spiritual focus without manual counting distractions
- Ensure family safety in massive pilgrimage crowds through multi-technology Family Finder with Bluetooth, WiFi Direct, and Google Maps integration
- Provide authentic spiritual content via direct access to Friday sermons from holy mosques in 6 languages with synchronized captions
- Create unified pilgrimage platform consolidating prayer times, historical places, crowd insights, and family coordination in single offline-capable application
- Achieve market leadership with 100,000 downloads and 15% premium conversion rate at accessible $10/year subscription pricing
- Establish Ziarah as the #1 comprehensive Islamic pilgrimage companion serving global Muslim community of 1.8+ billion people

### Background Context

The Ziarah project addresses critical gaps in Islamic pilgrimage technology by recognizing that current solutions treat pilgrimage as regular travel rather than understanding the unique spiritual, safety, and logistical requirements of Islamic worship in crowded sacred spaces. With 2+ million annual Hajj pilgrims and 7+ million Umrah pilgrims struggling with fragmented tools, manual ritual counting errors, family separation concerns, and lack of authentic multilingual spiritual content, there exists a clear market opportunity for purpose-built pilgrimage technology.

Unlike existing Islamic apps that add pilgrimage features as afterthoughts, Ziarah's offline-first architecture, GPS-guided ritual assistance, advanced family tracking capabilities, and integration with authentic holy mosque sermons create a comprehensive solution designed specifically for the intersection of technology and Islamic worship in sacred spaces.

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-23 | 1.0 | Initial PRD creation based on Project Brief | John (PM) |

## Requirements

### Functional Requirements

**FR1:** The app shall provide GPS-guided Tawaf counter in MyIbadah tools that automatically tracks pilgrim progress around the Kaaba using Google Maps API with manual override capabilities when GPS accuracy is compromised

**FR2:** The app shall provide GPS-guided Sa'i counter in MyIbadah tools that automatically tracks pilgrim progress between Safa and Marwah using Google Maps API with manual override capabilities

**FR3:** The app shall implement Family Finder with QR code group creation (with preset expiry dates), Bluetooth/WiFi Direct detection, Google Maps API integration, preset messaging system ("Come to me", "Go to hotel"), and directional guidance to family members

**FR4:** The app shall integrate with @tubesermon YouTube channel using YouTube Data API to provide Live Friday Sermon and Last Friday Sermon from Mecca and Medina with specific search algorithms using Hijri date conversion, language keywords, and mosque-specific filters for 6 languages (English, Persian, Turkish, Urdu, Malay, Indonesian)

**FR5:** The app shall provide local daily prayer times using Al Adhan API (https://aladhan.com/prayer-times-api) with azan notifications and Gregorian to Hijri date conversion using Islamic Calendar API (https://aladhan.com/islamic-calendar-api)

**FR6:** The app shall display News section with Hajj/Umrah news titles that direct users to external browser when clicked

**FR7:** The app shall display weather forecast specifically for holy cities (Mecca and Medina) in the Home tab top section

**FR8:** The app shall provide Quick Map functionality allowing users to add and save custom locations (Hotel, Gate, etc.) with arrow pointing to selected location in Home tab middle section

**FR9:** The app shall provide Crowd Insights using Kaggle dataset (user ziya07, CC0 Public Domain) showing data-driven crowd predictions for holy sites in Mecca, Medina, and surrounding pilgrimage locations

**FR10:** The app shall provide MyItineraries feature connected to Historical Places tab, allowing users to view and edit custom pilgrimage itineraries

**FR11:** The app shall provide comprehensive Ibadah Guide for Hajj with progress tracker in Knowledge Hub tab

**FR12:** The app shall provide comprehensive Ibadah Guide for Umrah with progress tracker in Knowledge Hub tab

**FR13:** The app shall display Historical Places with detailed information including name, historical period, category, description, GPS locations, access information with disclaimers, Hadith/Quran references, "Add to MyItinerary" button, and "Get directions" button

**FR14:** The app shall implement freemium subscription model with $10/year premium tier and secure payment processing through Google Play Billing

**FR15:** The app shall provide offline functionality for critical features including prayer times, manual ritual counters, and cached historical information

### Non-Functional Requirements

**NFR1:** The app shall maintain 99.5% uptime for critical features (prayer times, GPS tracking) with <2 second response times for location-based queries

**NFR2:** The app shall launch in <3 seconds and acquire GPS location within <2 seconds on target Android devices (API level 21+)

**NFR3:** The app shall function reliably in offline mode for core features when network connectivity is poor or unavailable

**NFR4:** The app shall implement end-to-end encryption for family location data and personal information to ensure privacy compliance

**NFR5:** The app shall optimize API usage through caching strategies to maintain profitability at $10/year subscription price point

**NFR6:** The app shall support devices with GPS, Bluetooth 4.0+, and WiFi capabilities while maintaining maximum 500MB storage footprint

**NFR7:** The app shall ensure Islamic data sensitivity by preventing tracking during prayer times and private worship periods

**NFR8:** The app shall maintain GDPR compliance for European users with clear data retention policies and user consent management

**NFR9:** The app shall optimize battery usage to support 12+ hour pilgrimage days through efficient location services, background processing management, and adaptive refresh rates

**NFR10:** The Friday Sermon search algorithm shall find exactly 1 matching video per search query using the specified multi-step filtering process (date → language → mosque → validation)

## User Interface Design Goals

### Overall UX Vision
**Clean, distraction-free interface prioritizing spiritual focus during religious activities. The app should feel respectful and appropriate for sacred spaces, with intuitive navigation that works seamlessly for users ranging from tech-savvy millennials to elderly pilgrims. Emphasis on large, clear controls that function reliably with gloved hands or during crowded conditions.**

### Key Interaction Paradigms
**Touch-optimized with minimal swipe gestures to prevent accidental actions during ritual movements. Primary navigation through bottom tab bar (5 tabs: Home, Prayer Times, Knowledge Hub, Friday Sermon, Historical Places). Critical actions like GPS ritual activation use prominent, hard-to-miss buttons with confirmation dialogs. Emergency family finder features accessible via persistent floating action button.**

### Core Screens and Views

**Home Tab:**
- Top Section: Hajj/Umrah news headlines linking to external browser
- Middle Section: Arrow pointing to selected saved location in Quick Map
- Bottom Section: 2x2 tiled icons for Quick Map, Family Finder, MyItineraries, and Crowd Insights

**Prayer Times Tab:**
- Local daily prayer times display with azan/notification settings
- Today's Gregorian to Hijri date conversion

**Knowledge Hub Tab:**
- Ibadah Guide for Hajj with progress tracker
- Ibadah Guide for Umrah with progress tracker
- MyIbadah tools including GPS-guided Tawaf and Sa'i counters

**Friday Sermon Tab:**
- Live and Last Friday sermon selection
- Location choice (Mecca/Medina), language selection (6 languages)
- Sermon audio and transcript display with search functionality

**Historical Places Tab:**
- Categorized list of Islamic historical sites in Mecca, Medina and surrounding areas
- Place details with GPS coordinates, historical period, descriptions
- Hadith/Quran references and "Add to MyItinerary" buttons

### Accessibility: WCAG AA
**Implementing WCAG AA compliance to support elderly pilgrims and users with visual impairments. High contrast color schemes, scalable text (up to 200%), screen reader compatibility, and voice feedback for critical GPS counting functions.**

### Branding
**Islamic-inspired design with reverent color palette (deep blues, golds, and whites reminiscent of holy mosque architecture). Typography should be readable in multiple scripts (Arabic, Latin, Southeast Asian). Subtle geometric patterns inspired by Islamic art, but never distracting from functional content. No imagery of people or animals per Islamic guidelines.**

### Target Device and Platforms: Web Responsive
**Primary focus on Android smartphones (API 21+) with responsive design principles. Interface must adapt to various screen sizes from compact phones (5") to large devices (6.7"+). Touch targets minimum 44dp for accessibility, with increased spacing for elderly users.**

## Technical Assumptions

### Repository Structure: Monorepo
**Single repository containing Flutter mobile app, Firebase backend configuration, and shared documentation. This approach simplifies dependency management, enables atomic commits across frontend/backend changes, and reduces complexity for solo/small team development while supporting future cross-platform expansion.**

### Service Architecture
**Hybrid approach combining monolithic Flutter app with serverless backend services. Core app functionality runs as single deployable unit while leveraging Firebase Functions for real-time features (family tracking, notifications) and external API orchestration. This balances development simplicity with scalability for real-time location services.**

### Testing Requirements
**Unit + Integration testing focused on critical path validation. Priority testing areas: GPS accuracy simulation, API integration reliability, offline functionality, and subscription flow validation. Manual testing required for physical location features and family finder scenarios in crowded environments.**

### Additional Technical Assumptions and Requests

**Frontend Framework & Platform:**
- **Flutter 3.x** for Android development with future iOS expansion capability
- **Target Android API Level 21+** (Android 5.0+) for maximum device compatibility
- **Material Design 3** with Islamic-appropriate customizations for consistent UX

**Backend & Infrastructure:**
- **Firebase suite** for authentication, real-time database, cloud messaging, and analytics
- **Google Cloud Platform** hosting for scalability and API integration ecosystem
- **SQLite** for local caching and offline-first data persistence

**External API Integration:**
- **Al Adhan API** for prayer times and Islamic calendar conversion
- **YouTube Data API v3** for @tubesermon channel integration with strict rate limiting
- **Google Maps API** for GPS-guided ritual assistance and family location services
- **Kaggle CC0 dataset integration** for crowd insights (user ziya07 dataset)

**Performance & Optimization:**
- **Offline-first architecture** with background sync for prayer times and historical content
- **API caching strategies** with 24-hour prayer time cache and intelligent refresh policies
- **Battery optimization** through adaptive GPS polling and background service management
- **Progressive loading** for historical places content and sermon transcripts

**Security & Compliance:**
- **End-to-end encryption** for family location data using Firebase security rules
- **Islamic data sensitivity** with prayer time detection to pause location tracking
- **GDPR compliance** implementation with clear consent management for EU users
- **Google Play Billing integration** for secure subscription processing

**Development & Deployment:**
- **CI/CD pipeline** using GitHub Actions for automated testing and Play Store deployment
- **Version control strategy** with feature branches and semantic versioning
- **Error tracking** via Firebase Crashlytics for production issue monitoring
- **Analytics implementation** using Firebase Analytics with privacy-compliant user behavior tracking

## Monetization Strategy

### Freemium Business Model
Ziarah operates on a freemium subscription model with $10/year premium tier designed to balance global accessibility with sustainable development funding. The strategy provides essential Islamic functionality for free while gating advanced pilgrimage features behind premium subscription.

### Premium Conversion Strategy
- **Target Conversion Rate:** 15% of active users convert to premium within 12 months
- **Value Proposition:** GPS-guided ritual assistance and family safety features justify premium cost for serious pilgrims
- **Pricing Rationale:** $10/year price point ensures global accessibility while covering API costs and development
- **Conversion Triggers:** Trial GPS features during app onboarding, family safety scenarios during group setup

### Freemium vs Premium Feature Comparison

| Feature Category         | Free Tier                                     | Premium Tier ($10/year)                                         |
|--------------------------|-----------------------------------------------|-----------------------------------------------------------------|
| 🏠 **HOME TAB**          |                                               |                                                                 |
| News & Weather           | ✅ Full access                                | ✅ Full access                                                   |
| Quick Map                | ✅ Full access                                | ✅ Full access                                                   |
| Crowd Insights           | ✅ Full access                                | ✅ Full access                                                   |
| Family Finder            | Basic tracking only (Bluetooth + WiFi Direct) | Full features: Google Maps API + preset messaging + last location |
| MyItineraries            | ❌ Not available                              | ✅ Create, edit, manage itineraries                              |
| 🕐 **PRAYER TIMES**      |                                               |                                                                 |
| All Features             | ✅ Full access                                | ✅ Full access                                                   |
| 📚 **KNOWLEDGE HUB**     |                                               |                                                                 |
| Guides & Manual Counters | ✅ Full access                                | ✅ Full access                                                   |
| GPS-guided Tawaf/Sa'i    | ❌ Not available                              | ✅ GPS-guided with Google Maps API                               |
| 🕌 **FRIDAY SERMON**     |                                               |                                                                 |
| All Features             | ❌ Not available                              | ✅ Full access (Google YouTube API)                             |
| 🏛️ **HISTORICAL PLACES** |                                               |                                                                 |
| Basic Information        | ✅ Full access                                | ✅ Full access                                                   |
| Advanced Features        | ❌ Limited                                    | ✅ Hadith/Quran references + itinerary integration               |

### Revenue Projections
- **Year 1 Target:** 25,000 downloads, 15% conversion = 3,750 premium users = $37,500 revenue
- **Year 2 Target:** 100,000 downloads, 15% conversion = 15,000 premium users = $150,000 revenue
- **Break-even Analysis:** Estimated $50,000 annual operating costs (APIs, hosting, development) achieved at 5,000 premium subscribers

## Epic List

### Epic 1: Foundation & Prayer Times Infrastructure
**Goal:** Establish project foundation with authentication system and complete Prayer Times tab functionality, delivering immediate value through accurate global prayer times with Al Adhan API integration and Hijri calendar conversion while setting up infrastructure for advanced features.

### Epic 2: Knowledge Hub & GPS-Guided Ritual Assistance
**Goal:** Implement complete Knowledge Hub tab with Ibadah Guides for Hajj and Umrah (with progress trackers) and MyIbadah GPS-guided Tawaf/Sa'i counters, providing the core pilgrimage assistance value that differentiates Ziarah from basic Islamic apps.

### Epic 3: Friday Sermon Integration & Content Platform
**Goal:** Deploy complete Friday Sermon tab with sophisticated YouTube API integration using @tubesermon channel, implementing the multi-step search algorithm (date/language/mosque filtering) for Live and Last Friday sermons in 6 languages from Mecca and Medina.

### Epic 4: Historical Places & MyItineraries System
**Goal:** Implement complete Historical Places tab with comprehensive Islamic site database (name, period, category, description, GPS, Hadith/Quran references) and integrated MyItineraries functionality, enabling custom pilgrimage planning and itinerary management.

### Epic 5: Family Safety System
**Goal:** Deploy Family Finder with QR group creation, multi-technology tracking (Bluetooth/WiFi Direct/Google Maps), preset messaging system ("Come to me", "Go to hotel"), and directional guidance to family members, ensuring family safety during crowded pilgrimage conditions.

### Epic 6: Home Dashboard Complete Integration
**Goal:** Implement complete Home tab with news integration (external browser links), weather forecast for holy cities, arrow pointing to selected saved location in Quick Map middle section, and 2x2 tiled icons (Quick Map, Family Finder, MyItineraries, Crowd Insights) with all dependencies resolved and tiles fully functional.

## Epic Details

### Epic 1: Foundation & Prayer Times Infrastructure

**Epic Goal:** Establish comprehensive project foundation including Flutter app setup, authentication system, subscription framework, and complete Prayer Times tab functionality. This epic delivers immediate user value through accurate global prayer times while creating the technical infrastructure required for all subsequent features. Users will have a fully functional Islamic prayer app with premium subscription capabilities.

#### Story 1.1: Project Setup & Basic App Structure
**As a developer,**
**I want to establish the foundational Flutter project with proper architecture,**
**so that the team has a scalable codebase for building all Ziarah features.**

**Acceptance Criteria:**
1. Flutter 3.x project created with Android target (API Level 21+)
2. Material Design 3 theme configured with Islamic-appropriate color palette
3. Bottom navigation bar implemented with 5 tabs (Home, Prayer Times, Knowledge Hub, Friday Sermon, Historical Places)
4. Basic routing structure established for all main screens
5. Firebase project configured with Android app registration
6. SQLite database schema designed for offline data storage
7. Repository pattern implemented for data access abstraction

#### Story 1.2: Authentication System Implementation
**As a user,**
**I want to create an account and sign in securely,**
**so that I can access premium features and sync my preferences across devices.**

**Acceptance Criteria:**
1. Firebase Authentication integrated with email/password and Google Sign-In options
2. User registration flow with email verification implemented
3. Secure sign-in flow with password reset functionality
4. User profile management screen with basic account information
5. Authentication state management throughout app lifecycle
6. Proper error handling for authentication failures
7. Guest mode option for users who want to try basic features before registering

#### Story 1.3: Subscription System & Payment Integration
**As a user,**
**I want to subscribe to premium features for $10/year,**
**so that I can access advanced pilgrimage assistance tools and content.**

**Acceptance Criteria:**
1. Google Play Billing integration for in-app purchases
2. Subscription product configured for $10/year premium tier
3. Subscription status verification and management system
4. Free vs. premium feature access control throughout app
5. Subscription renewal and cancellation handling
6. Clear premium feature comparison screen
7. Receipt validation and secure subscription state storage

#### Story 1.4: Prayer Times Core Implementation
**As a Muslim user,**
**I want to see accurate daily prayer times for my location,**
**so that I can fulfill my religious obligations regardless of where I am in the world.**

**Acceptance Criteria:**
1. Al Adhan API integration for prayer times (https://aladhan.com/prayer-times-api)
2. Automatic location detection with manual location override option
3. Five daily prayer times displayed with countdown to next prayer
4. Prayer time calculation method selection (different schools of thought)
5. 24-hour caching strategy for prayer times to support offline usage
6. Location-based automatic timezone and DST handling
7. Error handling for API failures with cached fallback data

#### Story 1.5: Hijri Calendar Integration
**As a Muslim user,**
**I want to see today's date in both Gregorian and Hijri calendars,**
**so that I can stay connected to the Islamic calendar system.**

**Acceptance Criteria:**
1. Al Adhan Islamic Calendar API integration (https://aladhan.com/islamic-calendar-api)
2. Automatic Gregorian to Hijri date conversion for current date
3. Display of both calendar systems in Prayer Times tab
4. Hijri date adjustment options for different calculation methods
5. Historical date conversion capability for sermon search functionality (Epic 3 dependency)
6. Offline fallback using calculated Hijri dates when API unavailable
7. Clear visual distinction between Gregorian and Hijri date displays

#### Story 1.6: Azan Notifications & Audio System
**As a practicing Muslim,**
**I want to receive prayer time notifications with authentic azan audio,**
**so that I never miss prayer times even when busy with daily activities.**

**Acceptance Criteria:**
1. Local notification system for all five daily prayers
2. Customizable notification timing (at prayer time, 5/10/15 minutes before)
3. Multiple azan audio options with high-quality recordings
4. Volume control and silent mode options
5. Do Not Disturb integration respecting system settings
6. Notification persistence across app restarts and device reboots
7. User preferences for enabling/disabling individual prayer notifications

### Epic 2: Knowledge Hub & GPS-Guided Ritual Assistance

**Epic Goal:** Implement complete Knowledge Hub tab with comprehensive Ibadah Guides for Hajj and Umrah (including progress trackers) and MyIbadah GPS-guided Tawaf/Sa'i counters. This epic delivers the core pilgrimage assistance value that differentiates Ziarah from basic Islamic apps, providing both educational guidance and precise ritual assistance for pilgrims.

#### Story 2.1: Ibadah Guide for Hajj Implementation
**As a pilgrim preparing for Hajj,**
**I want a comprehensive step-by-step guide with progress tracking,**
**so that I can understand and complete all Hajj rituals correctly without missing any steps.**

**Acceptance Criteria:**
1. Complete Hajj ritual sequence guide with detailed step-by-step instructions
2. Progress tracker showing current step and completion status throughout Hajj journey
3. Interactive checklist for each major ritual (Ihram, Tawaf, Sa'i, Arafat, Muzdalifah, Mina, Jamarat)
4. Clear visual indicators for completed, current, and upcoming ritual steps
5. Offline functionality for all guide content and progress tracking
6. Reset functionality to restart guide for multiple Hajj journeys
7. Educational content explaining the spiritual significance of each ritual

#### Story 2.2: Ibadah Guide for Umrah Implementation
**As a pilgrim performing Umrah,**
**I want a focused guide specifically for Umrah rituals with progress tracking,**
**so that I can complete the shorter pilgrimage correctly and efficiently.**

**Acceptance Criteria:**
1. Complete Umrah ritual sequence guide (Ihram, Tawaf, Sa'i, Tahallul)
2. Progress tracker optimized for shorter Umrah timeline
3. Interactive checklist for each Umrah step with clear completion indicators
4. Distinction between essential (Fard) and recommended (Sunnah) actions
5. Multiple Umrah support for pilgrims performing multiple Umrahs
6. Integration with manual ritual counters for Tawaf and Sa'i
7. Offline functionality for complete guide access

#### Story 2.3: MyIbadah Manual Ritual Counters
**As a pilgrim performing Tawaf or Sa'i,**
**I want manual counters to track my progress around the Kaaba and between Safa-Marwah,**
**so that I can maintain accurate count even without GPS assistance.**

**Acceptance Criteria:**
1. Manual Tawaf counter with large, easy-to-tap increment buttons (7 rounds total)
2. Manual Sa'i counter with directional tracking between Safa and Marwah (7 rounds total)
3. Visual progress indicators showing current round and remaining rounds
4. Audio feedback for round completion (optional, user-configurable)
5. Reset functionality for restarting counting if mistakes occur
6. Counter state persistence across app sessions and device restarts
7. Large button design suitable for use during physical movement

#### Story 2.4: GPS-Guided Tawaf Counter (Premium Feature)
**As a premium subscriber performing Tawaf,**
**I want GPS-guided automatic counting around the Kaaba,**
**so that I can focus entirely on my spiritual devotion without manual counting distractions.**

**Acceptance Criteria:**
1. Google Maps API integration for precise location tracking around Kaaba
2. Automatic round detection and counting for 7 Tawaf circuits
3. Manual override button for GPS accuracy issues or personal preference
4. Visual display showing current position relative to Kaaba and progress
5. Round completion notifications with optional audio alerts
6. GPS accuracy validation with fallback to manual counting when needed
7. Battery optimization to prevent excessive drain during extended ritual

#### Story 2.5: GPS-Guided Sa'i Counter (Premium Feature)
**As a premium subscriber performing Sa'i,**
**I want GPS-guided automatic counting between Safa and Marwah,**
**so that I can maintain spiritual focus while ensuring accurate ritual completion.**

**Acceptance Criteria:**
1. Google Maps API integration for tracking movement between Safa and Marwah hills
2. Automatic directional detection and round counting (7 total rounds)
3. Clear visual indicators for current direction (Safa→Marwah or Marwah→Safa)
4. Manual override functionality for GPS issues or user preference
5. Progress visualization showing current round and direction
6. Completion celebration with appropriate spiritual messaging
7. Integration with manual counter as backup system

#### Story 2.6: Knowledge Hub Navigation & Integration
**As a user exploring the Knowledge Hub,**
**I want intuitive navigation between guides and tools,**
**so that I can easily access all pilgrimage assistance features in one place.**

**Acceptance Criteria:**
1. Clean tab structure within Knowledge Hub for Hajj Guide, Umrah Guide, and MyIbadah
2. Quick access buttons to switch between manual and GPS-guided counters
3. Premium feature gating with clear upgrade prompts for GPS-guided tools
4. Integration between guides and counters (launch counter from guide steps)
5. Help documentation and tutorial for first-time users
6. Consistent design language matching overall app Islamic theming
7. Accessibility features for elderly users with larger touch targets

### Epic 3: Friday Sermon Integration & Content Platform

**Epic Goal:** Deploy complete Friday Sermon tab with sophisticated YouTube API integration using @tubesermon channel, implementing unified audio-caption streaming with synchronized playback, multi-step search algorithm (date/language/mosque filtering) for Live and Last Friday sermons in 6 languages from Mecca and Medina.

#### Story 3.1: YouTube Content Extraction & Unified Streaming Setup
**As a developer,**
**I want to implement unified audio-caption extraction from @tubesermon YouTube videos,**
**so that users get synchronized spiritual content with minimal data usage and battery drain.**

**Acceptance Criteria:**
1. youtube_explode_dart package integration for simultaneous audio and caption extraction
2. @tubesermon channel access (Channel ID: UCB0qibtjzOIemPjQSaoWkGg) with StreamManifest parsing
3. Audio quality optimization targeting 64kbps AAC/Opus streams for battery efficiency
4. Caption track extraction in VTT/SRT format with precise timestamps
5. Content selection prioritizing lowest bitrate audio + available caption languages (Arabic, English)
6. Unified SQLite caching for 30 minutes storing both audio URLs and caption text
7. Error handling for extraction failures with fallback to theislamicinformation.com audio-only mode

#### Story 3.2: Enhanced Sermon Search Algorithm Implementation
**As a user searching for specific Friday sermons,**
**I want the app to find exactly one matching sermon using precise filtering,**
**so that I can access the exact spiritual content I need without confusion.**

**Acceptance Criteria:**
1. Hijri date conversion using Al Adhan API for current/last Friday calculation
2. Multi-step filtering implementation:
   - **Step 1:** User selection (location, language, sermon type)
   - **Step 2:** Date filtering with Hijri format "YYYY-MM-DD"
   - **Step 3:** Language keyword matching per specified dictionary
   - **Step 4:** Mosque-specific filtering per language-mosque keyword mapping
   - **Step 5:** Exact match validation ensuring single result
3. Complete language keyword system: english, melayu, indonesia, türkçe, فارسی, اردو
4. Complete mosque keyword system with language-specific terms (makkah/haram/prophet/nabawi/nebevi/نبوی/نبوى)
5. Search result validation ensuring exactly 1 matching video per query
6. Error handling when no matches found with alternative suggestions
7. Example title format validation: "Friday Khutbah [1447-03-20] Mescid-i Haram Türkçe"

#### Story 3.3: Synchronized Audio-Caption Playback Engine
**As a user consuming Friday sermon content,**
**I want synchronized audio and captions with seamless playback controls,**
**so that I can follow spiritual teachings clearly in my preferred format.**

**Acceptance Criteria:**
1. just_audio package integration with AudioSource.uri for optimized streaming
2. Custom Flutter widget for real-time caption synchronization with audio position
3. Background playback capability with optional caption notifications
4. Predictive buffering: 30-second audio buffer + pre-loaded caption segments
5. Caption display controls: toggle on/off without interrupting audio playback
6. Multi-language caption support with automatic Arabic/English detection
7. Accessibility features: text scaling, high contrast, and synchronized text highlighting

#### Story 3.4: Performance & Battery Optimization
**As a user with limited device battery during pilgrimage,**
**I want efficient sermon streaming that minimizes battery and data usage,**
**so that I can access spiritual content throughout long pilgrimage days.**

**Acceptance Criteria:**
1. Battery efficiency targeting <10% drain per session with captions adding <1% overhead
2. Network resilience with 10-30 second buffering for audio and caption synchronization
3. Memory optimization with caption text typically <5KB vs audio streams in MB
4. Quality optimization controls allowing user preference for data/battery balance
5. Offline capability for cached captions working after first load
6. Intelligent caching strategy minimizing repeated API calls and data usage
7. Background processing optimization to prevent interference with other app features

#### Story 3.5: Enhanced Content Discovery & Search Integration
**As a user exploring Friday sermon content,**
**I want powerful search capabilities within sermon transcripts,**
**so that I can find specific spiritual topics and teachings efficiently.**

**Acceptance Criteria:**
1. In-transcript search functionality for finding specific topics within sermon content
2. Search integration across cached caption text for offline topic discovery
3. Topic highlighting and navigation within sermon timeline
4. Search result context display showing surrounding sermon content
5. Bookmarking capability for important sermon segments
6. Search history for frequently accessed spiritual topics
7. Integration with sermon metadata for enhanced content organization

#### Story 3.6: Fallback Mechanisms & Content Resilience
**As a user dependent on spiritual content access,**
**I want reliable sermon access even when primary systems fail,**
**so that my spiritual practice remains uninterrupted during pilgrimage.**

**Acceptance Criteria:**
1. HTML scraping fallback from theislamicinformation.com when YouTube API fails
2. Audio-only mode operation when caption extraction fails
3. Cached content prioritization when network connectivity is poor
4. Graceful degradation maintaining core functionality during partial failures
5. User notification system explaining content availability and limitations
6. Retry mechanisms with intelligent backoff for temporary API failures
7. Alternative content suggestions when specific sermons unavailable

#### Story 3.7: Premium Integration & Access Control
**As a business stakeholder,**
**I want Friday Sermon features to demonstrate clear premium value,**
**so that this sophisticated content platform drives subscription conversions.**

**Acceptance Criteria:**
1. Complete Friday Sermon tab restricted to premium subscribers with sophisticated feature set
2. Premium feature showcase highlighting unified audio-caption delivery capabilities
3. Subscription verification with seamless access for active premium users
4. Free preview functionality showcasing audio quality and caption synchronization
5. Premium upgrade prompts emphasizing advanced content discovery and search features
6. Integration with subscription management ensuring secure access control
7. Usage analytics tracking premium feature engagement for business optimization

### Epic 4: Historical Places & MyItineraries System

**Epic Goal:** Implement complete Historical Places tab with comprehensive Islamic site database (name, period, category, description, GPS, Hadith/Quran references) and integrated MyItineraries functionality, enabling custom pilgrimage planning and itinerary management. This epic provides educational content and planning tools that enhance the spiritual journey through historical context and organized visit planning.

#### Story 4.1: Historical Places Database Implementation
**As a pilgrim interested in Islamic history,**
**I want access to comprehensive information about historical sites in Mecca, Medina, and surrounding areas,**
**so that I can understand the significance of places I visit during my pilgrimage.**

**Acceptance Criteria:**
1. Complete database of Islamic historical places in Mecca, Medina, and road between cities
2. Required fields for each place: name, historical period, category, description, GPS coordinates
3. Historical period classification (Prophet SAW time, Rashidun Caliphate, Umayyad, Abbasid, etc.)
4. Category system (Mosque, Battlefield, Cave, Mountain, Well, House, etc.)
5. Detailed descriptions with historical context and significance
6. Accurate GPS coordinates for navigation and mapping integration
7. Offline data storage for complete access without internet connectivity

#### Story 4.2: Access Information & Safety Disclaimers
**As a pilgrim planning visits to historical sites,**
**I want current access information and safety guidance,**
**so that I can plan visits appropriately and safely during my pilgrimage.**

**Acceptance Criteria:**
1. Current access status for each historical place (Open, Restricted, Closed, Seasonal)
2. Visiting hours information where applicable
3. Entry requirements and restrictions (if any)
4. Safety disclaimers for challenging locations (mountains, remote sites)
5. Crowd level indicators during pilgrimage seasons
6. Transportation recommendations and parking availability
7. Special considerations for elderly or disabled visitors

#### Story 4.3: Hadith & Quran References Integration
**As a Muslim seeking authentic Islamic knowledge,**
**I want verified Hadith and Quran references for each historical place,**
**so that I can understand the religious significance and authenticity of each site.**

**Acceptance Criteria:**
1. Authenticated Hadith references with source citations (Bukhari, Muslim, etc.)
2. Relevant Quran verses with chapter and verse numbers
3. Arabic text with English translations for religious references
4. Scholar verification and approval for all religious content
5. Clear distinction between confirmed historical facts and traditional accounts
6. Source credibility indicators for different levels of authentication
7. Premium feature: Extended religious commentary and scholarly analysis

#### Story 4.4: MyItineraries Creation & Management System
**As a pilgrim planning my pilgrimage journey,**
**I want to create and manage custom itineraries with historical places,**
**so that I can organize my visits efficiently and ensure I don't miss important sites.**

**Acceptance Criteria:**
1. Itinerary creation interface allowing custom naming and organization
2. "Add to MyItinerary" button integration from Historical Places listings
3. Drag-and-drop reordering of places within itineraries
4. Multiple itinerary support for different trip segments (Mecca, Medina, combined)
5. Itinerary sharing capabilities for family and group coordination
6. Estimated visit times and travel duration calculations
7. Premium feature: Advanced itinerary optimization for efficient routing

#### Story 4.5: Navigation & Directions Integration
**As a pilgrim visiting historical places,**
**I want reliable directions and navigation assistance,**
**so that I can find and reach each site efficiently during my pilgrimage.**

**Acceptance Criteria:**
1. "Get Directions" button integration with device default mapping apps
2. GPS coordinate accuracy verification for reliable navigation
3. Multiple transportation options (walking, driving, public transport)
4. Distance and estimated travel time calculations from current location
5. Offline map integration for areas with poor connectivity
6. Landmark-based directions for areas where GPS may be unreliable
7. Integration with Quick Map functionality from Home tab

#### Story 4.6: Search, Filter & Discovery Features
**As a user exploring Islamic historical content,**
**I want powerful search and filtering capabilities,**
**so that I can discover places relevant to my interests and available time.**

**Acceptance Criteria:**
1. Text search across place names, descriptions, and categories
2. Filter by historical period, category, and location (Mecca/Medina/Road)
3. Filter by access status and current availability
4. Sort options (alphabetical, distance, historical period, category)
5. Recently viewed places tracking for easy return access
6. Favorites system for bookmarking important places
7. Premium feature: Advanced filters by Hadith authenticity level and scholarly consensus

#### Story 4.7: Home Tab Integration & Quick Access
**As a user navigating the app,**
**I want seamless integration between Historical Places and other app features,**
**so that I can access my itineraries and planning tools from the main dashboard.**

**Acceptance Criteria:**
1. MyItineraries tile in Home tab 2x2 grid providing quick access
2. Current active itinerary display with next planned location
3. Quick actions for adding current location to active itinerary
4. Integration with Family Finder for sharing location during historical site visits
5. Notification system for planned itinerary items based on current location
6. Recently visited places display for easy reference
7. Cross-feature integration enabling smooth workflow between planning and execution

### Epic 5: Family Safety System

**Epic Goal:** Deploy Family Finder with QR group creation, multi-technology tracking (Bluetooth/WiFi Direct/Google Maps), preset messaging system ("Come to me", "Go to hotel"), and directional guidance to family members, ensuring family safety during crowded pilgrimage conditions. This epic provides critical safety infrastructure that addresses one of the most pressing concerns for pilgrimage families.

#### Story 5.1: QR Group Creation & Management
**As a family leader organizing group pilgrimage,**
**I want to create a family group that others can join easily,**
**so that we can coordinate safely during crowded pilgrimage activities.**

**Acceptance Criteria:**
1. QR code generation for family group creation with unique group identifiers
2. Preset expiry date configuration for group QR codes (1 day, 3 days, 7 days, custom)
3. QR code scanning functionality for joining existing family groups
4. Group member management with add/remove capabilities
5. Group leader designation with administrative privileges
6. Multiple group support for complex family structures or tour groups
7. Offline QR code functionality for areas with poor connectivity

#### Story 5.2: Multi-Technology Location Tracking
**As a family member in a crowded pilgrimage environment,**
**I want reliable location tracking that works even when GPS fails,**
**so that my family can find me regardless of network or signal conditions.**

**Acceptance Criteria:**
1. Bluetooth Low Energy (BLE) proximity detection for close-range family tracking
2. WiFi Direct peer-to-peer connection for medium-range communication
3. Google Maps API integration for precise GPS-based location sharing
4. Automatic technology switching based on signal strength and availability
5. Battery optimization to minimize drain during extended pilgrimage days
6. Privacy controls allowing location sharing pause/resume
7. Redundant tracking ensuring backup when primary methods fail

#### Story 5.3: Real-Time Family Map Display
**As a family member wanting to locate others,**
**I want to see all family members on a real-time map,**
**so that I can understand everyone's location and plan reunion efficiently.**

**Acceptance Criteria:**
1. Real-time map display showing all active family member locations
2. Individual family member identification with names/photos/icons
3. Location accuracy indicators showing GPS, WiFi, or Bluetooth-based positioning
4. Last known location display when current location unavailable
5. Map zoom and navigation controls optimized for crowded area visibility
6. Offline map functionality using cached location data
7. Privacy indicators showing who can see current user's location

#### Story 5.4: Preset Messaging & Communication System
**As a family member needing to communicate during pilgrimage,**
**I want quick preset messages that work without typing,**
**so that I can communicate efficiently even in crowded or stressful situations.**

**Acceptance Criteria:**
1. Preset message system with common pilgrimage communication needs:
   - "Come to me" with automatic location sharing
   - "Go to hotel" with hotel location reference
   - "I'm safe" for general check-ins
   - "Need help" for emergency situations
   - "Wait for me" for timing coordination
2. One-tap message sending to individual family members or entire group
3. Message delivery confirmation and read receipts
4. Custom message creation for specific family needs
5. Multi-language support for preset messages
6. Offline message queuing for delivery when connectivity returns
7. Emergency priority messaging with enhanced delivery attempts

#### Story 5.5: Directional Guidance & Navigation
**As a family member trying to reach another family member,**
**I want clear directional guidance to their location,**
**so that I can navigate crowded pilgrimage areas efficiently and safely.**

**Acceptance Criteria:**
1. "Get Direction" functionality to selected family member's current location
2. Real-time route updates as family member location changes
3. Distance and estimated arrival time calculations
4. Turn-by-turn navigation optimized for pedestrian movement in crowds
5. Landmark-based directions for areas where GPS accuracy is poor
6. Alternative route suggestions when primary path is blocked
7. Arrival notifications when family members reach each other

#### Story 5.6: Emergency Features & Safety Protocols
**As a family concerned about safety during pilgrimage,**
**I want emergency features that ensure quick response during crisis situations,**
**so that we can handle emergencies effectively and get help when needed.**

**Acceptance Criteria:**
1. Emergency button triggering immediate location broadcast to all family members
2. Automatic emergency detection when family member hasn't moved for extended period
3. Emergency contact integration with local emergency services information
4. Family check-in reminders and automated safety confirmations
5. Lost family member protocol with systematic search coordination
6. Emergency message escalation with enhanced delivery priority
7. Integration with device emergency features and local emergency protocols

#### Story 5.7: Home Tab Integration & Quick Access
**As a user needing quick access to family safety features,**
**I want Family Finder prominently available from the main dashboard,**
**so that I can access safety features immediately during any app usage.**

**Acceptance Criteria:**
1. Family Finder tile in Home tab 2x2 grid for immediate access
2. Quick status display showing online family members and last check-in times
3. Emergency access button visible from Home tab for crisis situations
4. Integration with other app features for seamless family coordination
5. Notification system for family member location updates and messages
6. Quick group switching for users managing multiple family groups
7. Premium vs free feature differentiation with clear upgrade pathways

### Epic 6: Home Dashboard Complete Integration

**Epic Goal:** Implement complete Home tab with news integration (external browser links), weather forecast for holy cities, arrow pointing to selected saved location in Quick Map middle section, and 2x2 tiled icons (Quick Map, Family Finder, MyItineraries, Crowd Insights) with all dependencies resolved and tiles fully functional. This epic delivers the unified dashboard that brings together all Ziarah features in an intuitive, accessible interface.

#### Story 6.1: News Integration & External Browser Links
**As a pilgrim wanting to stay informed about Hajj and Umrah developments,**
**I want access to relevant news headlines that open in external browsers,**
**so that I can stay updated on important pilgrimage information while using the app.**

**Acceptance Criteria:**
1. News feed integration displaying Hajj and Umrah related headlines in Home tab top section
2. External browser link functionality opening full articles outside the app
3. News source curation focusing on reliable Islamic and pilgrimage-related content
4. Headline refresh functionality with appropriate caching to minimize data usage
5. Offline headline storage showing last cached news when connectivity unavailable
6. News categorization (Hajj updates, Umrah information, general Islamic news)
7. User preference settings for news frequency and source selection

#### Story 6.2: Weather Forecast for Holy Cities
**As a pilgrim planning daily activities,**
**I want accurate weather forecasts for Mecca and Medina,**
**so that I can prepare appropriately for outdoor pilgrimage activities.**

**Acceptance Criteria:**
1. Weather forecast display for both Mecca and Medina in Home tab top section
2. Current weather conditions with temperature, humidity, and conditions
3. 3-day forecast showing expected weather patterns for trip planning
4. Pilgrimage-specific weather insights (heat advisories, prayer time conditions)
5. Weather data caching for offline access during poor connectivity
6. Location-based weather switching when user travels between holy cities
7. Weather alert notifications for extreme conditions affecting pilgrimage activities

#### Story 6.3: Quick Map with Selected Location Arrow
**As a user saving important locations during pilgrimage,**
**I want visual indication of my selected location in the Quick Map,**
**so that I can quickly orient myself and navigate to saved places.**

**Acceptance Criteria:**
1. Quick Map display in Home tab middle section with clear location indicator
2. Arrow pointing to currently selected saved location from Quick Map functionality
3. Location selection interface allowing switching between saved locations
4. Visual map orientation showing user's current position relative to selected location
5. Distance and direction information to selected location
6. Integration with saved locations from Quick Map feature (Hotels, Gates, custom locations)
7. Offline map functionality showing cached location data when connectivity poor

#### Story 6.4: 2x2 Tiled Icons Integration
**As a user needing quick access to key app features,**
**I want organized tile access to main functions from the Home dashboard,**
**so that I can efficiently navigate to essential pilgrimage tools.**

**Acceptance Criteria:**
1. 2x2 grid layout in Home tab bottom section with four primary tiles:
   - **Quick Map tile:** Access to location saving and map functionality
   - **Family Finder tile:** Access to family safety and tracking features (from Epic 5)
   - **MyItineraries tile:** Access to itinerary management (from Epic 4)
   - **Crowd Insights tile:** Access to crowd prediction and analysis
2. Tile status indicators showing active family groups, current itinerary, etc.
3. Quick actions available directly from tiles without full feature navigation
4. Visual design consistency with overall app Islamic theming
5. Accessibility features including larger touch targets and clear labeling
6. Premium feature indicators on tiles requiring subscription
7. Tile customization allowing user preference for tile arrangement

#### Story 6.5: Crowd Insights Implementation
**As a pilgrim planning optimal visit times,**
**I want data-driven crowd predictions for holy sites,**
**so that I can plan visits during less crowded periods for better spiritual experience.**

**Acceptance Criteria:**
1. Crowd Insights integration using Kaggle dataset (user ziya07, CC0 Public Domain)
2. Historical crowd pattern analysis for Mecca, Medina, and surrounding pilgrimage locations
3. Real-time crowd level indicators for major pilgrimage sites
4. Optimal visit time recommendations based on historical data and current conditions
5. Crowd level predictions for upcoming hours and days
6. Integration with MyItineraries for crowd-optimized itinerary suggestions
7. Visual crowd level indicators (Low, Medium, High, Extreme) with color coding

#### Story 6.6: Dashboard State Management & Personalization
**As a user with personalized pilgrimage preferences,**
**I want the Home dashboard to reflect my current pilgrimage status and preferences,**
**so that the interface adapts to my specific needs and journey stage.**

**Acceptance Criteria:**
1. Dashboard state persistence across app sessions and device restarts
2. Personalization based on current pilgrimage stage (Pre-pilgrimage, In Mecca, In Medina, Post-pilgrimage)
3. Dynamic content prioritization based on current location and pilgrimage progress
4. User preference integration for news sources, weather locations, and tile priorities
5. Recent activity tracking showing last used features and locations
6. Quick access to frequently used functions based on usage patterns
7. Dashboard customization options allowing user control over information density

#### Story 6.7: Cross-Feature Integration & Workflow Optimization
**As a user managing complex pilgrimage logistics,**
**I want seamless integration between Home dashboard and all app features,**
**so that I can efficiently coordinate all aspects of my pilgrimage from one central location.**

**Acceptance Criteria:**
1. Deep linking between Home dashboard tiles and their respective full features
2. Contextual notifications appearing on Home dashboard from all app features
3. Quick action completion without leaving Home dashboard for simple tasks
4. Integration status indicators showing connectivity and feature availability
5. Emergency feature access prominently available from Home dashboard
6. Cross-feature data sharing enabling intelligent recommendations and insights
7. Workflow optimization reducing navigation steps between related features

## Checklist Results Report

### Executive Summary
- **Overall PRD Completeness:** 92% complete
- **MVP Scope Appropriateness:** Just Right - well-balanced between minimal and viable
- **Readiness for Architecture Phase:** Ready with minor recommendations
- **Most Critical Concerns:** Technical risk areas flagged for architect investigation, some integration complexity

### Category Analysis Table

| Category                         | Status  | Critical Issues |
| -------------------------------- | ------- | --------------- |
| 1. Problem Definition & Context  | PASS    | None |
| 2. MVP Scope Definition          | PASS    | None |
| 3. User Experience Requirements  | PASS    | None |
| 4. Functional Requirements       | PASS    | None |
| 5. Non-Functional Requirements   | PASS    | None |
| 6. Epic & Story Structure        | PASS    | None |
| 7. Technical Guidance            | PARTIAL | High complexity areas need architect deep-dive |
| 8. Cross-Functional Requirements | PASS    | None |
| 9. Clarity & Communication       | PASS    | None |

### Technical Readiness Assessment

**READY FOR ARCHITECT** - The PRD and epics are comprehensive, properly structured, and ready for architectural design. The identified technical complexity areas are appropriate for architect investigation rather than blocking concerns.

**Key Technical Investigation Areas:**
- GPS accuracy solutions for crowded Haram environments (2M+ pilgrims)
- Multi-technology family tracking implementation (Bluetooth/WiFi Direct/Google Maps)
- YouTube API rate limiting and cost optimization strategies at scale
- Real-time location synchronization architecture for family safety
- Audio-caption synchronization system using youtube_explode_dart + just_audio

### Recommendations
1. **Immediate Actions:** Proceed to architecture phase, prioritize GPS accuracy investigation
2. **Architecture Phase Focus:** Technical risk mitigation strategies, performance optimization approach
3. **Development Preparation:** Set up technical validation environment, establish API monitoring

## Next Steps

### UX Expert Prompt

The Ziarah PRD is complete and ready for UX design. Please initiate UX Expert mode and create comprehensive UI/UX designs based on this PRD.

**Key UX Focus Areas:**
- Islamic-appropriate design language with reverent color palette
- 5-tab navigation optimized for pilgrimage conditions (gloved hands, crowded spaces)
- Home dashboard 2x2 tile layout integration
- GPS ritual counter interfaces for spiritual focus
- Family safety emergency access patterns
- Accessibility compliance (WCAG AA) for elderly pilgrims
- Multi-language content display (6 languages for sermons)

**Critical UX Deliverables Needed:**
- Complete wireframe set for all 5 tabs
- Interactive prototypes for GPS-guided ritual assistance
- Family Finder emergency workflow designs
- Premium vs free feature visual differentiation
- Islamic cultural sensitivity validation

### Architect Prompt

The Ziarah PRD is validated and ready for technical architecture. Please initiate Architect mode and create comprehensive system architecture based on this PRD.

**Critical Technical Investigation Areas:**
- GPS accuracy solutions for crowded Haram environments (2M+ pilgrims)
- Multi-technology family tracking implementation (Bluetooth/WiFi Direct/Google Maps)
- YouTube API rate limiting and cost optimization strategies at scale
- Real-time location synchronization architecture for family safety
- Audio-caption synchronization system using youtube_explode_dart + just_audio

**Required Architecture Deliverables:**
- Complete system architecture diagram
- API integration strategy and cost modeling
- Database schema for offline-first functionality
- Security architecture for encrypted family location data
- Performance optimization strategy for 12+ hour battery life
- Deployment and CI/CD pipeline design
- Technical risk mitigation plans for identified complexity areas

**Implementation Priorities:**
1. Foundation infrastructure (Epic 1)
2. GPS service architecture (Epic 2)
3. Content platform integration (Epic 3-4)
4. Real-time safety systems (Epic 5)
5. Dashboard integration (Epic 6)