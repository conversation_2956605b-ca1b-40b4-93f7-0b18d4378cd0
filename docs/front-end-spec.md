# ziarah UI/UX Specification
*Comprehensive Pilgrimage Companion Mobile Application*

## Introduction

This document defines the user experience goals, information architecture, user flows, and visual design specifications for ziarah - a comprehensive pilgrimage companion application serving the global Muslim community. Based on the validated PRD, <PERSON><PERSON><PERSON> addresses critical gaps in Islamic pilgrimage technology with GPS-guided ritual counting, multi-technology family safety systems, authentic spiritual content, and offline-first architecture designed specifically for sacred spaces.

### Project Context & Vision

**Market Opportunity**: 2M+ annual Hajj pilgrims and 7M+ Umrah pilgrims currently struggle with fragmented tools, manual ritual counting errors, family separation concerns, and lack of authentic multilingual spiritual content.

**Core Innovation**: Purpose-built pilgrimage technology that understands the intersection of technology and Islamic worship in crowded sacred spaces, rather than treating pilgrimage as regular travel.

### Overall UX Goals & Principles

#### Target User Personas

**Primary Pilgrim** (Core User)
- Muslims performing Hajj or Umrah pilgrimage
- Age range: 25-65, varying technical literacy
- Needs: Reliable spiritual guidance, safety coordination, authentic content
- Context: Crowded sacred spaces, limited connectivity, extended usage days
- Pain points: Manual counting errors, family separation fears, fragmented apps

**Group Coordinator** (Family Leader/Tour Guide)
- Manages family/group logistics and safety during pilgrimage
- Needs: Family tracking, emergency communication, itinerary coordination
- Critical features: Family Finder, MyItineraries, emergency protocols
- High responsibility for group safety and spiritual experience

**Elderly Pilgrim** (Accessibility Focus)
- 55+ years old, potentially limited mobility and tech experience
- Needs: Large touch targets, simplified interfaces, reliable features
- Accessibility requirements: WCAG AA compliance, voice feedback
- Special considerations: Gloved hands, crowded conditions, fatigue

#### Usability Goals

- **Spiritual Focus First**: Interface design never interrupts or distracts from religious devotion
- **Reliability in Sacred Spaces**: 99.5% uptime for critical features (prayer times, GPS tracking)
- **Sacred Space Appropriateness**: Respectful, reverential design suitable for holy environments
- **Family Safety Assurance**: Immediate access to family coordination and emergency features
- **Multi-language Accessibility**: Native support for 6 languages with cultural sensitivity
- **Offline Resilience**: Core functionality available without internet connectivity
- **Battery Endurance**: 12+ hour operation during extended pilgrimage days
- **Crowd Condition Optimization**: Interface works with gloved hands in crowded conditions

#### Design Principles

1. **Sacred Context First** - Every design decision honors the spiritual significance of pilgrimage
2. **Reliability Over Features** - Core functionality must work flawlessly in challenging conditions
3. **Family Safety Priority** - Emergency and safety features are always accessible
4. **Cultural Inclusivity** - Design accommodates diverse Muslim cultures and languages
5. **Respectful Information Density** - Balance comprehensive guidance with visual clarity
6. **Accessibility by Default** - Support elderly pilgrims and users with impairments
7. **Privacy & Islamic Values** - Data handling respects Islamic privacy principles

## Information Architecture (IA)

### Primary Navigation Structure (5-Tab Bottom Navigation)

Based on PRD functional requirements and Epic structure:

```mermaid
graph TD
    A[Bottom Navigation - Always Visible] --> B[🏠 Home]
    A --> C[🕐 Prayer Times]
    A --> D[📚 Knowledge Hub]
    A --> E[🕌 Friday Sermon]
    A --> F[🏛️ Historical Places]

    %% Home Tab (Epic 6)
    B --> B1[📰 News Feed - External Links]
    B --> B2[🌤️ Weather - Mecca/Medina]
    B --> B3[🗺️ Quick Map Preview - Selected Location]
    B --> B4[2x2 Feature Grid]

    B4 --> B4A[🗺️ Quick Map - Location Management]
    B4 --> B4B[👨‍👩‍👧‍👦 Family Finder - Safety System]
    B4 --> B4C[📋 MyItineraries - Trip Planning]
    B4 --> B4D[👥 Crowd Insights - Optimal Timing]

    %% Prayer Times Tab (Epic 1)
    C --> C1[🕐 Daily Prayer Times - Al Adhan API]
    C --> C2[🔔 Azan Notifications - Customizable]
    C --> C3[📅 Gregorian/Hijri Converter - Date System]
    C --> C4[🧭 Qibla Direction - Compass]

    %% Knowledge Hub Tab (Epic 2)
    D --> D1[🕋 Hajj Guide + Tracker - Complete Ritual]
    D --> D2[🕋 Umrah Guide + Tracker - Focused Journey]
    D --> D3[📱 MyIbadah Tools - Ritual Assistance]

    D3 --> D3A[Manual Tawaf Counter - 7 Circuits]
    D3 --> D3B[Manual Sa'i Counter - 7 Rounds]
    D3 --> D3C[GPS Tawaf Counter - Premium Feature]
    D3 --> D3D[GPS Sa'i Counter - Premium Feature]

    %% Friday Sermon Tab (Epic 3) - Premium Only
    E --> E1[📡 Live Sermon - Current Friday]
    E --> E2[📻 Last Friday Sermon - Previous Week]
    E --> E3[🌍 Location Selection - Mecca/Medina]
    E --> E4[🗣️ Language Selection - 6 Languages]
    E --> E5[🎵 Audio Player - just_audio Integration]
    E --> E6[📝 Synchronized Captions - Real-time Text]

    %% Historical Places Tab (Epic 4)
    F --> F1[🔍 Search & Filter - Category/Period]
    F --> F2[📍 Place Categories - Mosque/Battlefield/Cave]
    F --> F3[⏳ Historical Periods - Prophet/Caliphate]
    F --> F4[📖 Place Details - GPS/References]
    F --> F5[📋 Itinerary Integration - Add Button]
```

### Navigation Patterns & Hierarchy

**Primary Navigation (Bottom Tabs)**
- **Always visible** for instant access to essential pilgrimage functions
- **Tab badges** for time-sensitive notifications (prayer times, family alerts)
- **Islamic iconography** with text labels for cultural clarity
- **Touch-optimized** for gloved hands and crowded conditions

**Secondary Navigation (Within Tabs)**
- **Feature grids** (2x2) for organized tool access on Home tab
- **Modal overlays** for focused tasks (Family Finder setup, Sermon selection)
- **Stack navigation** for detailed content (Place details, Guide steps)
- **Floating action buttons** for emergency features (Family Finder alerts)

**Information Hierarchy Priorities**
1. **Safety/Emergency** - Family Finder emergency access from any screen
2. **Time-Critical** - Prayer times, live sermons, crowd insights
3. **Core Pilgrimage** - GPS ritual assistance, guides, historical places
4. **Planning & Coordination** - Itineraries, saved locations, preferences

## User Flows

### Core Pilgrimage Flow: GPS-Guided Ritual Assistance

**User Goal**: Complete Tawaf or Sa'i with GPS assistance while maintaining spiritual focus

**Entry Points**: Knowledge Hub → MyIbadah → GPS Counter (Premium), or Emergency manual override

**Success Criteria**: 7 complete circuits tracked accurately with minimal interface interaction

#### GPS Ritual Flow Diagram

```mermaid
graph TD
    A[Pilgrim Approaches Ritual Site] --> B[Opens Knowledge Hub]

    B --> C{Premium Subscriber?}
    C -->|Yes| D[Access GPS-Guided Counters]
    C -->|No| E[Manual Counters + Upgrade Prompt]

    D --> F{Select Ritual Type}
    F -->|Tawaf| G[GPS Tawaf Counter - Kaaba Circuit]
    F -->|Sa'i| H[GPS Sa'i Counter - Safa-Marwah]

    %% GPS Tawaf Flow
    G --> G1[GPS Location Verification]
    G1 --> G2{GPS Accuracy OK?}
    G2 -->|Yes| G3[Automatic Circuit Tracking]
    G2 -->|No| G4[Fallback to Manual Override]

    G3 --> G5[Round Completion Detection]
    G5 --> G6[Visual/Audio Feedback]
    G6 --> G7{7 Rounds Complete?}
    G7 -->|No| G3
    G7 -->|Yes| I[Ritual Completion Celebration]

    %% GPS Sa'i Flow
    H --> H1[Start Point Detection - Safa/Marwah]
    H1 --> H2[Directional Movement Tracking]
    H2 --> H3[End Point Detection + Round Count]
    H3 --> H4{7 Rounds Complete?}
    H4 -->|No| H2
    H4 -->|Yes| I

    %% Manual Override Path
    G4 --> J[Large Manual Counter Interface]
    E --> J
    J --> K[Manual Round Increment]
    K --> L{7 Rounds Complete?}
    L -->|No| K
    L -->|Yes| I

    I --> M[Progress Update to Guide]
    M --> N[Return to Pilgrimage Journey]
```

#### Critical Edge Cases

**GPS Accuracy Issues**:
- Automatic fallback to manual counters when GPS signal poor
- Clear user notification about accuracy status
- Manual override button always visible during GPS operation
- Battery optimization prevents GPS drain in extended usage

**Crowded Environment Challenges**:
- Alternative positioning using nearby landmarks when GPS fails
- Simplified interface with minimal interaction requirements
- Large touch targets suitable for crowded conditions and gloved hands
- Offline functionality when network connectivity compromised

### Family Safety Flow: Emergency Coordination

**User Goal**: Locate and communicate with family members during emergency or separation

**Entry Points**: Home tab Family Finder tile, Emergency button, Push notification

**Success Criteria**: Family contact established and location shared within 30 seconds

#### Emergency Family Safety Flow

```mermaid
graph TD
    A[Emergency/Separation Situation] --> B{Family Finder Access}
    B -->|Home Tile| C[Family Finder Dashboard]
    B -->|Emergency Button| D[Direct Emergency Mode]
    B -->|Push Notification| E[Respond to Family Alert]

    C --> F[View Family Map - Real-time Locations]
    F --> G{Technology Available?}
    G -->|GPS + Internet| H[Google Maps Integration]
    G -->|WiFi Direct| I[Local Network Tracking]
    G -->|Bluetooth Only| J[Proximity Detection]

    H --> K[Precise Location Display]
    I --> L[Medium-Range Tracking]
    J --> M[Close-Range Detection]

    K --> N[Select Family Member]
    L --> N
    M --> N

    N --> O{Communication Action}
    O -->|Get Directions| P[Navigation to Family Member]
    O -->|Send Message| Q[Preset Message Selection]
    O -->|Emergency Alert| R[Emergency Broadcast to All]

    Q --> Q1[Message Options]
    Q1 --> Q1A["Come to me" + Location]
    Q1 --> Q1B["Go to hotel" + Hotel Location]
    Q1 --> Q1C["I'm safe" + Status Update]
    Q1 --> Q1D["Need help" + Emergency Alert]

    D --> R
    R --> S[Enhanced Delivery Attempts]
    S --> T[All Available Technologies]
    T --> U[Family Notification + Location]

    P --> V[Turn-by-Turn Guidance]
    V --> W[Arrival Confirmation]
    W --> X[Family Reunification]
```

### Friday Sermon Discovery Flow (Premium Feature)

**User Goal**: Access Friday sermon in preferred language from chosen mosque with synchronized captions

**Entry Points**: Friday Sermon tab, Home tab sermon notifications, scheduled reminders

**Success Criteria**: Exact sermon located and playing with synchronized captions within 10 seconds

#### Advanced Sermon Selection Algorithm

```mermaid
graph TD
    A[User Opens Friday Sermon Tab] --> B[Premium Verification]
    B -->|Premium Active| C[Sermon Parameter Selection]
    B -->|Free User| D[Premium Upgrade Prompt]

    C --> E[Location Selection - Mecca/Medina]
    C --> F[Language Selection - 6 Options]
    C --> G[Type Selection - Live/Last Friday]

    E --> H[Hijri Date Calculation]
    F --> H
    G --> H

    H --> I[YouTube API Search - @tubesermon Channel]
    I --> J[Multi-Step Filtering Algorithm]

    J --> K[Step 1: Date Filter]
    K --> K1["Find titles with Hijri date: 'YYYY-MM-DD'"]

    K1 --> L[Step 2: Language Filter]
    L --> L1[Apply Language Keywords]
    L1 --> L1A[English: 'english']
    L1 --> L1B[Malay: 'melayu']
    L1 --> L1C[Indonesian: 'indonesia']
    L1 --> L1D[Turkish: 'türkçe']
    L1 --> L1E[Persian: 'فارسی']
    L1 --> L1F[Urdu: 'اردو']

    L1 --> M[Step 3: Mosque Filter]
    M --> M1{Language-Specific Mosque Keywords}
    M1 -->|Mecca| M1A[English: 'makkah', Malay/Indonesian: 'haram', etc.]
    M1 -->|Medina| M1B[English: 'prophet', Malay: 'nabawi', Turkish: 'nebevi', etc.]

    M1 --> N[Step 4: Exact Match Validation]
    N --> O{Single Result Found?}

    O -->|Yes| P[Load Audio + Caption Streams]
    O -->|No| Q[Alternative Suggestions]

    P --> R[youtube_explode_dart Extraction]
    R --> S[Unified Audio-Caption Streaming]

    S --> T[just_audio Playback Engine]
    S --> U[Synchronized Caption Display]

    T --> V[Background Playback Support]
    U --> W[Real-time Text Synchronization]

    V --> X[30-Second Predictive Buffering]
    W --> Y[Caption Search & Navigation]

    Q --> Z[Fallback Options]
    Z --> Z1[Different Date Suggestions]
    Z --> Z2[Different Language Options]
    Z --> Z3[Web Scraping Fallback]
```

## Component Library & Design System

### Core Component Requirements

Based on PRD technical requirements and Epic specifications:

#### Islamic-Appropriate Design Language

**Color Palette** (Reverent, Sacred Space Appropriate)
- **Primary**: Deep Blue (#1B365D) - Reminiscent of holy mosque architecture
- **Secondary**: Gold Accent (#D69E2E) - Islamic geometric patterns
- **Sacred**: White (#FFFFFF) - Purity, spiritual focus
- **Success**: Green (#38A169) - Completion, Islamic symbolism
- **Warning**: Orange (#DD6B20) - Caution, crowd alerts
- **Error**: Red (#E53E3E) - Emergency, safety alerts
- **Neutral Grays**: (#2D3748, #4A5568, #718096) - Text, borders, backgrounds

**Typography System** (Multi-Script Support)
- **Primary Font**: Roboto/System Default for Latin scripts
- **Arabic Support**: Noto Sans Arabic for Arabic text and numerals
- **Sizing Scale**: 12sp, 14sp, 16sp, 18sp, 20sp, 24sp, 28sp, 32sp
- **Weight Scale**: Regular (400), Medium (500), Semi-Bold (600), Bold (700)
- **Line Height**: 1.5x for readability in multiple languages

#### Core UI Components

**1. Navigation Components**

**Bottom Tab Navigation**
```yaml
component: BottomTabNavigation
requirements:
  - 5 tabs: Home, Prayer Times, Knowledge Hub, Friday Sermon, Historical Places
  - Always visible, fixed positioning
  - Tab badges for notifications (prayer alerts, family emergencies)
  - Islamic iconography with text labels
  - Touch targets: minimum 44dp height for accessibility
  - Active state: Primary color background + icon color change
  - Premium indicators: Gold accent for premium-only tabs
```

**Feature Grid (2x2 Home Layout)**
```yaml
component: FeatureGrid
requirements:
  - Consistent 2x2 layout with equal spacing
  - Large touch targets (minimum 88dp) for crowded conditions
  - Icon + text label format for clear communication
  - Status indicators (active family groups, current itinerary)
  - Premium differentiation with visual indicators
  - Accessibility: high contrast, screen reader support
```

**2. Pilgrimage-Specific Components**

**GPS Ritual Counter Interface**
```yaml
component: GPSRitualCounter
requirements:
  - Large, prominent count display (32sp minimum)
  - GPS accuracy status indicator (visual + text)
  - Manual override button always visible
  - Circuit/round progress visualization (circular progress bar)
  - Completion celebration animation (Islamic geometric patterns)
  - Battery status indicator during extended usage
  - Offline functionality with cached progress
```

**Prayer Times Widget**
```yaml
component: PrayerTimesWidget
requirements:
  - Next prayer countdown with visual progress
  - All 5 daily prayers with times in large, readable format
  - Hijri/Gregorian date display with conversion
  - Qibla direction indicator with compass
  - Notification settings quick access
  - Arabic/local numeral support
  - Accessibility: voice feedback for times
```

**Family Safety Map Component**
```yaml
component: FamilyMap
requirements:
  - Real-time family member location indicators
  - User identification (names, photos, custom icons)
  - Location accuracy indicators (GPS/WiFi/Bluetooth status)
  - Last known location display when current unavailable
  - Emergency alert visual indicators (red pulsing)
  - Privacy controls (location sharing on/off)
  - Offline map with cached family locations
```

**3. Content Display Components**

**Sermon Player Interface**
```yaml
component: SermonPlayer
requirements:
  - Audio-first design with large play/pause controls (88dp minimum)
  - Background playback with persistent notification controls
  - Caption overlay with adjustable text size (14sp-24sp range)
  - Synchronized caption highlighting with audio position
  - Language selection with flag indicators
  - Caption search functionality within transcript
  - Buffering indicators and offline capability status
```

**Historical Place Card**
```yaml
component: HistoricalPlaceCard
requirements:
  - Place name, category, and historical period display
  - GPS coordinates and distance from current location
  - Hadith/Quran reference indicators
  - "Add to Itinerary" and "Get Directions" action buttons
  - Image gallery support with historical photos
  - Expandable description with respectful Islamic content
  - Premium feature indicators for extended content
```

### Accessibility Requirements (WCAG AA Compliance)

**Visual Accessibility**
- **Color Contrast**: Minimum 4.5:1 for normal text, 3:1 for large text
- **Text Scaling**: Support up to 200% text size increase
- **Focus Indicators**: Clear visual focus for keyboard/switch navigation
- **Color Independence**: Information not conveyed by color alone

**Motor Accessibility**
- **Touch Targets**: Minimum 44dp with 8dp spacing between interactive elements
- **Large Button Mode**: 88dp touch targets for elderly users and crowded conditions
- **Gesture Alternatives**: Tap alternatives for complex gestures
- **Timeout Extensions**: Extended timeouts for users who need more time

**Cognitive Accessibility**
- **Simple Navigation**: Consistent navigation patterns throughout app
- **Clear Language**: Simple, respectful language avoiding technical jargon
- **Error Prevention**: Confirmation dialogs for destructive actions
- **Help Integration**: Contextual help and tutorials for complex features

### Premium vs Free Visual Differentiation

**Free Tier Visual Indicators**
- Grayed-out premium features with "Upgrade" labels
- Feature cards with subtle premium indicators (gold corner badge)
- Limited functionality hints within free features

**Premium Tier Visual Enhancements**
- Gold accent colors for premium-enabled features
- Enhanced visual feedback and animations
- Advanced progress indicators and detailed statistics
- Exclusive premium content badges and visual elements

### Premium Feature Comparison Screen (Paywall)

**Component: PremiumUpgradeScreen**

This critical conversion screen appears when free users attempt to access premium features or from the upgrade prompts throughout the app.

```yaml
component: PremiumUpgradeScreen
trigger_points:
  - GPS-guided ritual counter access attempt
  - Friday Sermon tab access attempt
  - Advanced Family Finder features (Google Maps integration)
  - MyItineraries creation attempt
  - Advanced Historical Places features

layout_structure:
  header:
    - App logo with premium gold accent
    - "Unlock Your Complete Pilgrimage Experience" headline
    - "$10/year - Less than $1 per month" pricing emphasis

  feature_comparison:
    format: side-by-side_comparison_table
    visual_style: Islamic_geometric_divider_lines

    comparison_categories:
      - family_safety: "👨‍👩‍👧‍👦 Family Safety & Coordination"
      - spiritual_guidance: "🕋 GPS-Guided Spiritual Assistance"
      - authentic_content: "🕌 Authentic Islamic Content"
      - trip_planning: "📋 Advanced Trip Planning"

  call_to_action:
    primary_button: "Start Your Sacred Journey - $10/year"
    secondary_button: "Continue with Basic Features"
    trust_indicators: "✓ 30-day money back guarantee"

feature_comparison_table:
  family_safety:
    free: "Basic Bluetooth tracking only"
    premium: "✓ GPS tracking with Google Maps\n✓ Preset emergency messages\n✓ Last known location storage"

  spiritual_guidance:
    free: "Manual Tawaf & Sa'i counters"
    premium: "✓ GPS-guided automatic counting\n✓ Circuit completion detection\n✓ Spiritual focus without distraction"

  authentic_content:
    free: "❌ No Friday Sermon access"
    premium: "✓ Live & archived sermons from holy mosques\n✓ 6 languages with synchronized captions\n✓ Audio + text search functionality"

  trip_planning:
    free: "Basic historical place information"
    premium: "✓ Create & manage custom itineraries\n✓ Verified Hadith & Quran references\n✓ Optimal timing with crowd insights"

visual_design_requirements:
  color_scheme:
    - Premium features: Gold accent (#D69E2E) backgrounds
    - Free features: Neutral gray (#718096) with limitations text
    - CTA button: Primary blue (#1B365D) with gold border

  islamic_visual_elements:
    - Subtle geometric patterns as section dividers
    - Respectful iconography (no human/animal imagery)
    - Mosque architecture-inspired borders

  accessibility:
    - High contrast for readability
    - Large touch targets (minimum 44dp)
    - Screen reader compatibility
    - Text scaling support up to 200%

conversion_optimization:
  value_propositions:
    - "Focus on your spiritual journey, not manual counting"
    - "Keep your family safe in crowded holy spaces"
    - "Access authentic content directly from Mecca & Medina"
    - "Plan your pilgrimage like never before"

  social_proof:
    - "Join thousands of pilgrims who've enhanced their journey"
    - "Trusted by families worldwide for pilgrimage safety"

  urgency_elements:
    - "Limited time: Start your pilgrimage with confidence"
    - "Don't let manual counting distract from your spiritual focus"

emotional_triggers:
  - Spiritual enhancement: "Deepen your connection during Tawaf"
  - Family safety: "Never lose track of loved ones again"
  - Authenticity: "Access the same sermons heard in the holy mosques"
  - Convenience: "Everything you need in one blessed app"
```

**Screen Flow Integration:**
- Appears as full-screen modal with Islamic-appropriate backdrop
- Can be dismissed but returns strategically when premium features accessed
- Post-subscription success screen with gratitude message and feature activation
- Smooth transition back to the premium feature that triggered the upgrade

### App Onboarding Flow (Welcome Screens)

**Component: OnboardingFlow**

The first-time user experience introducing ziarah's core value propositions through swipeable screens, designed to build excitement and establish the app's spiritual context.

```yaml
component: OnboardingFlow
trigger_condition: first_app_launch
total_screens: 5
navigation: horizontal_swipe_with_skip_option

screen_structure:
  - Welcome screen (brand introduction)
  - GPS Spiritual Assistance (core value prop)
  - Family Safety System (safety value prop)
  - Authentic Islamic Content (content value prop)
  - Get Started (final CTA with account creation)

screen_1_welcome:
  visual:
    background: gradient_blue_to_white_islamic_pattern
    illustration: stylized_kaaba_silhouette_with_geometric_patterns
    no_human_animal_imagery: true

  content:
    headline: "Welcome to Your Sacred Journey"
    subheadline: "ziarah - Your complete pilgrimage companion"
    description: "Designed specifically for Hajj and Umrah pilgrims, combining spiritual guidance with modern technology"

  ui_elements:
    skip_button: "Skip" (top right, subtle)
    next_button: "Begin Journey" (primary CTA)
    page_indicators: dots_with_islamic_geometric_styling

screen_2_gps_assistance:
  visual:
    background: deep_blue_with_gold_accents
    illustration: circular_progress_indicator_with_kaaba_center
    animation: subtle_rotation_suggesting_tawaf_movement

  content:
    headline: "GPS-Guided Spiritual Focus"
    key_benefit: "Never lose count during Tawaf or Sa'i again"
    description: "Automatic circuit counting lets you focus entirely on your spiritual connection"
    premium_hint: "Premium feature - Free trial available"

  ui_elements:
    try_now_button: "Try GPS Counter" (leads to premium trial)
    next_button: "Continue"

screen_3_family_safety:
  visual:
    background: warm_gradient_with_family_iconography
    illustration: connected_dots_representing_family_network
    color_scheme: safety_greens_and_blues

  content:
    headline: "Keep Your Family Safe & Connected"
    key_benefit: "Multi-technology tracking in crowded holy spaces"
    description: "GPS, WiFi Direct, and Bluetooth ensure you never lose track of loved ones"
    features_list:
      - "Real-time location sharing"
      - "Emergency preset messages"
      - "Works offline when needed"

  ui_elements:
    demo_button: "See Family Finder" (interactive demo)
    next_button: "Continue"

screen_4_authentic_content:
  visual:
    background: mosque_architecture_inspired_gradient
    illustration: audio_waveform_with_islamic_calligraphy_elements
    color_scheme: gold_and_deep_blue

  content:
    headline: "Authentic Islamic Content"
    key_benefit: "Direct access to sermons from holy mosques"
    description: "Live and archived Friday sermons from Mecca and Medina in 6 languages with synchronized captions"
    premium_highlight: "Premium exclusive feature"

  ui_elements:
    preview_button: "Preview Sermon" (audio sample)
    next_button: "Almost Done"

screen_5_get_started:
  visual:
    background: celebratory_gold_and_white_pattern
    illustration: app_interface_mockup_showing_home_tab
    style: clean_modern_islamic_aesthetic

  content:
    headline: "Your Pilgrimage Companion Awaits"
    description: "Join thousands of pilgrims who've enhanced their spiritual journey with ziarah"
    value_reminder: "Prayer times, GPS guidance, family safety, and authentic content - all in one app"

  ui_elements:
    primary_cta: "Create Your Account"
    secondary_cta: "Continue as Guest"
    trust_indicators: "✓ 100% Halal ✓ Privacy Protected ✓ Scholar Approved"

interaction_design:
  swipe_behavior:
    - Horizontal swipe between screens
    - Smooth transitions with Islamic-inspired easing
    - Automatic progression option (5 seconds per screen)
    - Swipe indicators show progress with geometric patterns

  skip_functionality:
    - "Skip" button available on all screens except final
    - Skip leads directly to account creation screen
    - Skip tracking for onboarding optimization

  accessibility:
    - Voice-over support for screen reader users
    - High contrast mode compatibility
    - Large touch targets (minimum 44dp)
    - Alternative text for all visual elements

premium_conversion_strategy:
  trial_offers:
    - GPS counter: "Try 3 free GPS-guided circuits"
    - Family Finder: "Create one free family group"
    - Sermons: "Listen to one complete sermon with captions"

  conversion_triggers:
    - Trial expiration notifications
    - Feature limitation reminders during peak usage
    - Special onboarding pricing (if applicable)

cultural_sensitivity:
  visual_guidelines:
    - No human or animal representations
    - Islamic geometric patterns and calligraphy-inspired elements
    - Colors evocative of holy mosque architecture
    - Respectful imagery focusing on spiritual themes

  language_considerations:
    - Simple, respectful language
    - Arabic terms properly transliterated
    - Cultural context appropriate for global Muslim audience
    - Avoid assumptions about pilgrimage experience level

analytics_tracking:
  onboarding_metrics:
    - Completion rate per screen
    - Skip rate analysis
    - Time spent on each screen
    - CTA engagement rates
    - Trial-to-premium conversion from onboarding

  optimization_goals:
    - >80% complete onboarding flow
    - >15% trial signup rate from onboarding
    - >25% premium conversion from onboarding trials
```

**Implementation Notes:**
- Onboarding appears only on first app launch (stored in local preferences)
- Can be manually accessed later through Settings > "App Introduction"
- Integrates with analytics for conversion optimization
- Respects user preference to skip and doesn't re-appear unless explicitly requested

### Settings Menu Screen

**Component: SettingsScreen**

Comprehensive settings menu accessible from user profile/menu, allowing users to configure location, content preferences, notifications, and app behavior after initial setup.

```yaml
component: SettingsScreen
access_points:
  - User profile menu
  - Home tab menu button
  - Bottom navigation overflow menu
  - Quick settings from notification panel

screen_layout:
  navigation: grouped_list_with_icons_and_descriptions
  search: settings_search_functionality
  visual_style: islamic_themed_with_geometric_dividers

settings_groups:
  location_and_prayer:
    icon: "📍"
    title: "Location & Prayer Times"
    items:
      - current_location:
          label: "Current Location"
          value: "{city}, {country}"
          action: location_selection_screen
          description: "Affects prayer times and family finder"

      - prayer_calculation:
          label: "Prayer Calculation Method"
          value: "{method_name}"
          action: prayer_method_selection
          description: "Choose calculation used by your local mosque"

      - madhab_selection:
          label: "Islamic School (Madhab)"
          value: "{madhab}"
          options: ["Hanafi", "Shafi/Maliki/Hanbali"]
          description: "Affects Asr prayer calculation"

      - hijri_calendar:
          label: "Hijri Calendar Method"
          value: "{hijri_method}"
          options: ["Umm al-Qura", "ISNA", "Muslim World League"]

  notifications:
    icon: "🔔"
    title: "Notifications"
    items:
      - prayer_notifications:
          label: "Prayer Time Notifications"
          toggle: enabled_by_default
          sub_settings:
            - timing: ["At time", "5 min before", "10 min before", "15 min before"]
            - prayers: individual_prayer_toggles
            - azan_sound: ["Traditional Mecca", "Traditional Medina", "Soft", "Silent"]

      - family_safety:
          label: "Family Safety Alerts"
          toggle: enabled_by_default
          description: "High priority notifications that bypass Do Not Disturb"

      - sermon_reminders:
          label: "Friday Sermon Reminders"
          toggle: disabled_by_default
          premium_required: true
          frequency: ["Weekly", "Monthly", "Never"]

  language_and_content:
    icon: "🌐"
    title: "Language & Content"
    items:
      - interface_language:
          label: "App Language"
          value: "{current_language}"
          options: ["English", "العربية (Arabic)"]

      - sermon_languages:
          label: "Sermon Languages"
          premium_required: true
          multi_select: true
          options:
            - "🇺🇸 English"
            - "🇲🇾 Bahasa Melayu"
            - "🇮🇩 Bahasa Indonesia"
            - "🇹🇷 Türkçe"
            - "🇮🇷 فارسی (Persian)"
            - "🇵🇰 اردو (Urdu)"

      - numeral_system:
          label: "Number Display"
          options: ["Western (1,2,3)", "Arabic-Indic (١,٢,٣)"]

  premium_and_account:
    icon: "👤"
    title: "Account & Subscription"
    items:
      - subscription_status:
          label: "Premium Subscription"
          value: "{subscription_status}"
          action: premium_management_screen
          upgrade_button: visible_for_free_users

      - account_info:
          label: "Account Information"
          value: "{email}"
          action: account_details_screen

      - data_sync:
          label: "Sync Preferences"
          description: "Sync settings across devices"
          toggle: enabled_by_default

  family_and_safety:
    icon: "👨‍👩‍👧‍👦"
    title: "Family & Safety"
    items:
      - family_groups:
          label: "My Family Groups"
          value: "{active_groups_count} active"
          action: family_groups_management

      - location_sharing:
          label: "Location Sharing"
          toggle: user_controlled
          description: "Share location with family members"

      - emergency_contacts:
          label: "Emergency Contacts"
          action: emergency_contacts_screen
          description: "Contacts for emergency situations"

  app_preferences:
    icon: "⚙️"
    title: "App Preferences"
    items:
      - theme:
          label: "App Theme"
          options: ["System Default", "Light", "Dark"]
          description: "Visual appearance of the app"

      - battery_optimization:
          label: "Battery Optimization"
          action: battery_settings_guide
          description: "Optimize for 12+ hour pilgrimage usage"

      - data_usage:
          label: "Data Usage"
          sub_settings:
            - offline_mode: toggle
            - auto_download: ["WiFi only", "WiFi + Mobile", "Never"]
            - cache_size: slider_control

  help_and_support:
    icon: "❓"
    title: "Help & Support"
    items:
      - app_tutorial:
          label: "App Tutorial"
          action: replay_onboarding_flow
          description: "Replay the welcome screens"

      - help_center:
          label: "Help Center"
          action: help_documentation
          description: "Guides and frequently asked questions"

      - contact_support:
          label: "Contact Support"
          action: support_contact_options
          description: "Get help with technical issues"

      - feedback:
          label: "Send Feedback"
          action: feedback_form
          description: "Help us improve ziarah"

  privacy_and_legal:
    icon: "🔒"
    title: "Privacy & Legal"
    items:
      - privacy_settings:
          label: "Privacy Settings"
          action: privacy_controls_screen
          description: "Control data collection and sharing"

      - data_export:
          label: "Export My Data"
          action: data_export_screen
          description: "Download your data (GDPR compliance)"

      - privacy_policy:
          label: "Privacy Policy"
          action: privacy_policy_document

      - terms_of_service:
          label: "Terms of Service"
          action: terms_document

  about:
    icon: "ℹ️"
    title: "About"
    items:
      - app_version:
          label: "App Version"
          value: "{version} ({build})"
          action: version_details

      - islamic_compliance:
          label: "Islamic Compliance"
          description: "Content verified by Islamic scholars"
          action: compliance_information

      - credits:
          label: "Credits & Acknowledgments"
          action: credits_screen

interaction_design:
  search_functionality:
    placeholder: "Search settings..."
    searchable_fields: [labels, descriptions, group_names]
    instant_results: highlight_matching_settings

  premium_differentiation:
    visual_indicators: gold_accent_for_premium_settings
    upgrade_prompts: contextual_upgrade_buttons
    feature_locks: clear_premium_required_messaging

  accessibility:
    screen_reader: full_voiceover_support
    text_scaling: supports_up_to_200_percent
    high_contrast: compatible_with_system_settings
    keyboard_navigation: full_keyboard_accessibility

error_handling:
  network_issues:
    offline_mode: settings_cached_and_sync_when_connected
    api_failures: graceful_degradation_with_retry_options

  permission_conflicts:
    location_denied: clear_explanation_and_manual_alternatives
    notification_blocked: guide_to_system_settings

  data_corruption:
    settings_reset: backup_and_restore_functionality
    factory_reset: complete_app_data_reset_option
```

**Navigation Integration:**
- Accessible from profile menu in any tab
- Settings search for quick access to specific preferences
- Deep linking support for direct access to specific setting screens
- Contextual settings access from relevant features (e.g., notification settings from Prayer Times tab)

## Wireframes & Visual Mockups

### Key Screen Layouts

Based on the PRD requirements and user flow analysis, here are the essential screen layouts that require detailed visual design:

#### Home Tab Layout (2x2 Grid)

```
┌─────────────────────────────────────┐
│ ☰ ziarah                    👤 🔔   │ ← Header with menu, profile, notifications
├─────────────────────────────────────┤
│ 📰 Hajj/Umrah News Headlines        │ ← Top section (1/3)
│ ←→ Swipeable news cards             │
├─────────────────────────────────────┤
│ 🌤️ Mecca: 32°C  |  Medina: 28°C   │
├─────────────────────────────────────┤
│ 🗺️ Quick Map Preview               │ ← Middle section (1/3)
│     📍 → Selected: Grand Mosque     │ ← Arrow pointing to saved location
│     📏 850m away                    │
├─────────────────────────────────────┤
│ ┌──────────┬──────────┐            │ ← Bottom section (1/3)
│ │ 🗺️ Quick │👨‍👩‍👧‍👦Family│            │ ← 2x2 Feature Grid
│ │   Map    │ Finder   │            │
│ │ 📍 3 saved│ 🔴 2 active│           │ ← Status indicators
│ ├──────────┼──────────┤            │
│ │📋MyItin- │👥 Crowd  │            │
│ │ eraries  │ Insights │            │
│ │ 🕌 5 places│📊 Medium │            │ ← Status indicators
│ └──────────┴──────────┘            │
└─────────────────────────────────────┘
```

#### Prayer Times Tab Layout

```
┌─────────────────────────────────────┐
│      📅 Safar 15, 1447 AH          │ ← Hijri date
│      📅 September 23, 2025          │ ← Gregorian date
├─────────────────────────────────────┤
│        🧭 Qibla Direction           │ ← Compass widget
│           ↗️ 67° NE                 │
├─────────────────────────────────────┤
│ 🌅 Fajr    ●  4:32 AM              │ ← Current prayer highlighted
│ ☀️ Sunrise    5:58 AM              │
│ 🌞 Dhuhr      12:15 PM             │
│ 🌇 Asr       3:42 PM              │
│ 🌆 Maghrib   6:28 PM              │
│ 🌙 Isha   ●  7:58 PM              │ ← Next prayer
├─────────────────────────────────────┤
│    ⏱️ Next Prayer: Isha            │ ← Countdown widget
│       in 2 hours 15 minutes        │
│    ████████░░ 80%                  │ ← Progress bar
├─────────────────────────────────────┤
│ 🔔 Notifications: ON  📱 Settings   │ ← Quick controls
└─────────────────────────────────────┘
```

#### GPS Ritual Counter Interface

```
┌─────────────────────────────────────┐
│ ← GPS Tawaf Counter          📶 GPS │ ← Back button + GPS status
├─────────────────────────────────────┤
│                                     │
│        ┌─────────────────┐          │
│        │   🕋 Kaaba      │          │ ← Visual center reference
│        │                 │          │
│        │        3        │          │ ← Large current count
│        │                 │          │
│        │   of 7 rounds   │          │
│        └─────────────────┘          │
│                                     │
│    ████████████░░░░ 43%             │ ← Circular progress bar
├─────────────────────────────────────┤
│ 📍 GPS Accuracy: High  🔋 Battery: 85%│ ← Status indicators
├─────────────────────────────────────┤
│        🔄 Manual Override           │ ← Always visible option
├─────────────────────────────────────┤
│     ✅ Round 1    ✅ Round 2       │ ← Completed rounds
│     ✅ Round 3    🔄 Round 4       │ ← Current round
│     ⭕ Round 5    ⭕ Round 6       │ ← Pending rounds
│     ⭕ Round 7                     │
└─────────────────────────────────────┘
```

#### Family Finder Map Interface

```
┌─────────────────────────────────────┐
│ ← Family Finder            🆘 Emergency│ ← Emergency button always visible
├─────────────────────────────────────┤
│ 👨‍👩‍👧‍👦 Al-Rahman Family Group (4 members)│ ← Group info
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │  📍Ahmed (You)  🟢            │ │ ← Map with family locations
│ │                               │ │
│ │     📍Fatima 🟢               │ │ ← GPS accuracy indicators
│ │                               │ │   🟢 High 🟡 Medium 🔴 Poor
│ │           📍Omar 🟡           │ │
│ │                               │ │
│ │  📍Aisha 🔴                   │ │ ← Last known location
│ │  (2 min ago)                  │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ 👤 Fatima         📏 50m   🔴 HELP  │ ← Family member status
│ 👤 Omar           📏 120m   💬 MSG  │ ← Distance + quick actions
│ 👤 Aisha          📏 ???    📍 FIND │ ← Lost member actions
├─────────────────────────────────────┤
│ 💬 Quick Messages:                  │ ← Preset messages
│ [Come to me] [Go to hotel] [I'm safe]│
└─────────────────────────────────────┘
```

#### Friday Sermon Player Interface

```
┌─────────────────────────────────────┐
│ ← Friday Sermon        🔍 Search    │ ← Back button + search
├─────────────────────────────────────┤
│ 🕌 Live from Masjid al-Haram        │ ← Sermon info
│ 📅 Safar 15, 1447 | 🇺🇸 English     │ ← Date + language
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │ 🎵 ████████████████████░░░░     │ │ ← Audio waveform
│ │                                 │ │
│ │    ⏮️    ⏸️    ⏭️               │ │ ← Large playback controls
│ │                                 │ │
│ │   15:32 / 42:16   🔊 ▬▬▬○▬     │ │ ← Time + volume
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ 📝 Captions: ON  📏 Text Size: M   │ ← Caption controls
├─────────────────────────────────────┤
│ "O believers! Remember Allah with   │ ← Synchronized captions
│ much remembrance, and glorify Him   │   with highlighting
│ morning and evening."               │
├─────────────────────────────────────┤
│ 🔄 Background Play: ON  💾 Download │ ← Additional controls
└─────────────────────────────────────┘
```

#### Historical Places Detail View

```
┌─────────────────────────────────────┐
│ ← Cave of Hira              📤 Share│ ← Back + share
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │      📸 Image Gallery           │ │ ← Photo gallery
│ │    ←  Current Image (1/5)  →    │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ 🏔️ Cave of Hira                   │ ← Place name
│ 📍 Jabal al-Nour, Mecca            │ ← Location
│ ⏳ Prophet Muhammad (PBUH) Era      │ ← Historical period
│ 🏞️ Mountain Cave                   │ ← Category
├─────────────────────────────────────┤
│ 📖 Description                      │ ← Expandable sections
│ The cave where Prophet Muhammad     │
│ (PBUH) received his first revelation │
│ from Angel Gabriel (Jibril)...      │
│                                     │
│ 📜 Islamic References               │ ← Hadith/Quran
│ • Sahih Bukhari, Volume 1, Book 1  │
│ • Quran: Al-Alaq (96:1-5)         │
├─────────────────────────────────────┤
│ 📏 2.3km from your location         │ ← Distance info
│ 🚗 15 min drive • 🚶 45 min walk   │ ← Travel estimates
├─────────────────────────────────────┤
│    📋 Add to Itinerary  🧭 Directions│ ← Action buttons
└─────────────────────────────────────┘
```

### Mobile Responsive Considerations

**Compact Screens (5.0" - 320dp width)**
- Reduce 2x2 grid padding and icon sizes
- Stack weather information vertically
- Minimize family member list to essential info only
- Single column layout for settings

**Large Screens (6.7" - 428dp width)**
- Larger touch targets for easier interaction
- More descriptive text in grid tiles
- Enhanced family map with more detail
- Side-by-side layouts where appropriate

**Accessibility Adaptations**
- All touch targets minimum 44dp (88dp for elderly users)
- High contrast mode support
- Text scaling up to 200% without horizontal scrolling
- Voice feedback for GPS counter and critical actions

## Error States & Empty States

### Error State Management

Error states in ziarah must be handled with particular sensitivity, as users depend on the app during critical pilgrimage moments. All errors should provide clear guidance and alternative solutions.

#### Network Connection Errors

```yaml
network_error_states:
  no_internet:
    visual: 📡❌ icon with Islamic geometric pattern background
    headline: "No Internet Connection"
    description: "Using offline features. Prayer times and manual counters are still available."
    actions:
      primary: "Try Again"
      secondary: "Use Offline Mode"
    offline_indicators: show_cached_data_timestamps

  api_timeout:
    visual: ⏱️ with loading spinner that stopped
    headline: "Service Temporarily Unavailable"
    description: "Our servers are busy. Your data is safe and we're working to restore service."
    actions:
      primary: "Retry"
      secondary: "Use Cached Data"
    islamic_messaging: "Sabr (patience) is a virtue"

  sermon_streaming_failed:
    visual: 🕌 with audio wave that stopped
    headline: "Sermon Unavailable"
    description: "The requested sermon couldn't be loaded. Try a different date or language."
    actions:
      primary: "Try Different Date"
      secondary: "Browse Available Sermons"
      tertiary: "Use Downloaded Content"
```

#### GPS and Location Errors

```yaml
location_error_states:
  gps_unavailable:
    visual: 📍❌ with compass-like Islamic geometric design
    headline: "GPS Signal Not Available"
    description: "Manual counters are ready to use while we search for a signal."
    actions:
      primary: "Switch to Manual Counter"
      secondary: "Keep Trying GPS"
    context: "You're in a sacred space - manual counting is equally blessed"

  location_permission_denied:
    visual: 🔒📍 with respectful privacy-themed design
    headline: "Location Permission Needed"
    description: "We need location access for prayer times and family safety features."
    actions:
      primary: "Enable Location"
      secondary: "Set Location Manually"
    privacy_assurance: "Your location is encrypted and never shared"

  gps_accuracy_poor:
    visual: 📶 with 1-2 bars and Islamic pattern
    headline: "GPS Accuracy Limited"
    description: "Signal is weak in this area. Manual override recommended."
    actions:
      primary: "Use Manual Counter"
      secondary: "Continue with GPS"
    guidance: "Manual counting allows better spiritual focus"
```

#### Family Safety Errors

```yaml
family_safety_errors:
  family_member_offline:
    visual: 👤⭕ with last known location indicator
    headline: "Family Member Offline"
    description: "Ahmed was last seen 15 minutes ago near Grand Mosque entrance."
    actions:
      primary: "Send Emergency Message"
      secondary: "Call Emergency Contact"
      tertiary: "Get Directions to Last Location"
    emergency_escalation: "Automatic emergency protocol in 30 minutes"

  group_creation_failed:
    visual: 👨‍👩‍👧‍👦❌ with QR code broken
    headline: "Group Creation Failed"
    description: "Couldn't create family group. Check internet connection."
    actions:
      primary: "Try Again"
      secondary: "Create Manual Group"
    alternative: "Use Bluetooth-only tracking until online"

  bluetooth_unavailable:
    visual: 📶❌ with Bluetooth symbol
    headline: "Bluetooth Not Available"
    description: "Family tracking limited to GPS only."
    actions:
      primary: "Enable Bluetooth"
      secondary: "Continue with GPS Only"
    impact: "Close-range family detection disabled"
```

#### Authentication and Premium Errors

```yaml
auth_premium_errors:
  subscription_expired:
    visual: 💳⏰ with respectful gold accent
    headline: "Premium Subscription Expired"
    description: "Your premium features are no longer available."
    actions:
      primary: "Renew Subscription"
      secondary: "Continue with Basic Features"
    grace_period: "Premium features available for 3 more days"

  payment_failed:
    visual: 💳❌ with secure payment icons
    headline: "Payment Could Not Be Processed"
    description: "Please update your payment method to continue premium access."
    actions:
      primary: "Update Payment Method"
      secondary: "Try Different Payment"
    support: "Contact support if issues persist"

  account_sync_failed:
    visual: 🔄❌ with cloud sync icon
    headline: "Sync Failed"
    description: "Your settings couldn't be synced across devices."
    actions:
      primary: "Try Sync Again"
      secondary: "Use Local Settings"
    reassurance: "All data is safe on this device"
```

### Empty State Design

Empty states should encourage engagement while maintaining the app's spiritual context.

#### Empty Content States

```yaml
empty_states:
  no_family_groups:
    visual: 👨‍👩‍👧‍👦➕ with Islamic family illustration
    headline: "No Family Groups Yet"
    description: "Create a group to keep your family safe during pilgrimage."
    actions:
      primary: "Create Family Group"
      secondary: "Join Existing Group"
    benefit: "Stay connected in crowded holy spaces"

  no_saved_locations:
    visual: 📍➕ with map and Islamic geometric points
    headline: "No Saved Locations"
    description: "Save important places like your hotel or meeting points."
    actions:
      primary: "Add Current Location"
      secondary: "Search for Places"
    suggestions: "Popular: Hotel, Gate 1, Prayer Area"

  no_itinerary_items:
    visual: 📋✨ with historical places icons
    headline: "Your Itinerary is Empty"
    description: "Add historical places to plan your spiritual journey."
    actions:
      primary: "Explore Historical Places"
      secondary: "Create Custom Itinerary"
    inspiration: "Discover places from the Prophet's (PBUH) time"

  no_downloaded_sermons:
    visual: 🕌📱 with download arrow
    headline: "No Offline Sermons"
    description: "Download sermons for offline listening during pilgrimage."
    actions:
      primary: "Download Recent Sermons"
      secondary: "Browse Sermon Library"
    premium_note: "Premium feature - Upgrade to access"

  search_no_results:
    visual: 🔍❌ with respectful "not found" design
    headline: "No Results Found"
    description: "Try different keywords or check spelling."
    actions:
      primary: "Clear Search"
      secondary: "Browse All Items"
    suggestions: "Popular searches: Kaaba, Prophet's Mosque, Safa"

  first_time_prayer_times:
    visual: 🕐✨ with prayer times layout preview
    headline: "Welcome to Prayer Times"
    description: "Your personalized prayer schedule is ready."
    actions:
      primary: "View Today's Times"
      secondary: "Customize Notifications"
    location_note: "Based on your location: {city}"
```

#### Offline Mode Empty States

```yaml
offline_empty_states:
  sermon_offline:
    visual: 🕌📱❌ with offline indicator
    headline: "No Offline Sermons Available"
    description: "Connect to internet to download sermons for offline listening."
    actions:
      primary: "Connect to WiFi"
      secondary: "Use Manual Prayer Guide"
    offline_alternatives: "Prayer guides and counters still available"

  news_offline:
    visual: 📰❌ with last updated timestamp
    headline: "Latest News Unavailable"
    description: "Last updated 2 hours ago. Connect to see latest updates."
    actions:
      primary: "Try Connection"
      secondary: "View Cached Articles"
    cached_count: "5 articles available offline"

  historical_places_offline:
    visual: 🏛️📱 with basic info icon
    headline: "Limited Information Available"
    description: "Full place details require internet connection."
    actions:
      primary: "Connect for Full Details"
      secondary: "View Basic Information"
    available_offline: "Name, location, and basic description available"
```

### Error Message Tone and Language

**Islamic Context Considerations:**
- Use respectful, patient language reflecting Islamic values
- Include concept of Sabr (patience) during technical difficulties
- Acknowledge that difficulties are part of the journey
- Provide spiritual perspective on challenges

**Examples of Appropriate Error Messaging:**
- "Sabr (patience) is rewarded - we're working to restore service"
- "Even in difficulties, your spiritual journey continues"
- "Manual methods are equally blessed when technology fails"
- "Your safety remains our priority while we reconnect"

**Avoid:**
- Frustrating or blame-oriented language
- Technical jargon that may confuse users
- Urgent language that creates anxiety during sacred activities
- Messages that interrupt spiritual focus unnecessarily

## Loading States & Transitions

### Loading State Design Philosophy

Loading states in ziarah should maintain the spiritual context and provide reassurance during potentially stressful pilgrimage moments. All loading indicators should use Islamic-appropriate design elements and respectful messaging.

#### Primary Loading Patterns

```yaml
loading_patterns:
  geometric_spinner:
    visual: Islamic geometric pattern (8-pointed star) rotating
    usage: General app loading, API calls, content refresh
    animation: Smooth rotation, 1.2s duration, continuous
    colors: Primary blue (#1B365D) with gold accent (#D69E2E)
    accessibility: Reduced motion alternative using pulsing opacity

  prayer_time_loading:
    visual: Crescent moon with subtle breathing animation
    usage: Prayer time calculations, location-based updates
    animation: Gentle scale 0.9-1.1, 2s duration, continuous
    text: "Calculating prayer times for your location..."
    timeout: 10 seconds with fallback to manual entry

  gps_signal_acquisition:
    visual: Concentric circles expanding from center point (Kaaba-like)
    usage: GPS lock, location services, family tracking
    animation: Ripple effect, 1.5s intervals, fade out
    text: "Acquiring GPS signal..." / "Connecting to family..."
    progress: Signal strength indicator

  content_streaming:
    visual: Audio waveform with Islamic geometric background
    usage: Friday sermon loading, audio content
    animation: Waveform bars moving, 0.8s cycle
    text: "Loading sermon..." / "Preparing audio..."
    buffer_indicator: Progress bar with Islamic pattern fill

  family_safety_loading:
    visual: Connected dots forming family network pattern
    usage: Family Finder initialization, group creation
    animation: Dots connecting with flowing light paths
    text: "Connecting family members..." / "Creating secure group..."
    safety_emphasis: Emphasis on security and privacy
```

#### Screen-Specific Loading States

```yaml
screen_loading_states:
  home_tab_initial_load:
    components:
      news_section:
        skeleton: Card-based skeleton with Islamic pattern placeholders
        timeout: 8 seconds
        fallback: "Latest news unavailable - using cached content"

      weather_widget:
        skeleton: Temperature placeholders with city names
        timeout: 5 seconds
        fallback: Generic weather advice for pilgrimage

      quick_map:
        skeleton: Map outline with loading location pin
        timeout: 10 seconds
        fallback: Manual location selection

      feature_grid:
        skeleton: 2x2 grid with icon placeholders
        timeout: 3 seconds (local data)
        fallback: Basic grid without status indicators

  prayer_times_loading:
    sequence:
      1. Location detection (0-3s): GPS acquiring animation
      2. API call (3-8s): Prayer calculation spinner
      3. Date conversion (8-10s): Hijri calendar loading
    complete_state: All prayer times displayed with next prayer highlighted
    error_fallback: Manual location entry with offline prayer calculations

  family_finder_initialization:
    sequence:
      1. Permission request (0-2s): Location/Bluetooth permission UI
      2. Service startup (2-5s): Multi-technology initialization
      3. Group connection (5-15s): Family member discovery
      4. Map rendering (15-20s): Real-time location display
    privacy_loading: "Securing your family's location data..."
    error_states: Progressive fallback through GPS->WiFi->Bluetooth

  sermon_player_loading:
    sequence:
      1. Search algorithm (0-5s): "Finding sermon..."
      2. Stream extraction (5-15s): "Preparing audio and captions..."
      3. Content buffering (15-25s): "Loading for offline listening..."
    progress_indicators:
      audio_extraction: Percentage with waveform animation
      caption_sync: "Synchronizing text with audio..."
    premium_loading: "Accessing premium sermon library..."

  historical_places_detail:
    sequence:
      1. Basic info (0-1s): Name, location, category
      2. Description (1-3s): Historical content with Islamic references
      3. Media loading (3-8s): Image gallery and additional content
      4. Interactive features (8-12s): Maps, directions, itinerary integration
    skeleton_design: Islamic manuscript-inspired placeholder layouts
    progressive_disclosure: Show content as it loads rather than all-or-nothing
```

#### Transition Animations

```yaml
transition_animations:
  tab_navigation:
    animation: Slide transition with Islamic easing curve
    duration: 300ms
    direction: Horizontal for tab switches
    accessibility: Respects reduced motion preferences
    loading_state: Show destination tab skeleton during transition

  modal_presentation:
    premium_upgrade_screen:
      entrance: Scale up from center with gold accent glow
      duration: 400ms
      background: Subtle Islamic geometric pattern fade-in

    family_finder_emergency:
      entrance: Slide up from bottom with urgent animation
      duration: 200ms (faster for emergency)
      visual_priority: Red accent with pulsing attention

    settings_screen:
      entrance: Slide in from right (standard iOS pattern)
      duration: 350ms
      return_transition: Slide out to right with settings save confirmation

  content_refresh:
    pull_to_refresh:
      visual: Crescent moon that completes circle when pulled sufficient distance
      haptic_feedback: Light impact on refresh threshold reached
      animation: Smooth rotation during refresh process
      success: Green checkmark with "Updated" message

    auto_refresh:
      prayer_times: Subtle pulse on time updates every minute
      family_locations: Smooth position updates without jarring movement
      sermon_progress: Smooth caption highlighting without text jumping

  loading_to_content:
    skeleton_to_real:
      animation: Fade transition from skeleton to actual content
      duration: 200ms
      stagger: Different content sections appear with 50ms delays
      smooth_reveal: Content slides in from skeleton positions

    error_to_retry:
      animation: Error state slides out, loading state slides in
      duration: 300ms
      feedback: Subtle haptic feedback on retry button press
      hope_messaging: "Trying again..." with patient tone
```

#### Progress Indicators

```yaml
progress_indicators:
  determinate_progress:
    sermon_download:
      visual: Circular progress with Islamic geometric pattern
      text: "Downloading sermon: 45% (2.3MB of 5.1MB)"
      time_estimate: "About 30 seconds remaining"
      offline_benefit: "Available for offline listening"

    itinerary_sync:
      visual: Linear progress bar with mosque milestone markers
      text: "Syncing itinerary: 3 of 7 places updated"
      step_indication: Clear indication of current step
      completion: Success animation with spiritual messaging

  indeterminate_progress:
    family_search:
      visual: Pulsing family network diagram
      text: "Searching for family members nearby..."
      timeout: 30 seconds with manual search option
      context: "This may take longer in crowded areas"

    authentication:
      visual: Secure connection animation with Islamic pattern
      text: "Signing you in securely..."
      privacy: "Your data is protected with encryption"
      timeout: 15 seconds with retry option

  micro_interactions:
    button_press_feedback:
      visual: Subtle scale down (0.95) with gold accent flash
      duration: 150ms
      haptic: Light impact for confirmation
      islamic_context: Respectful interaction without distraction

    successful_action:
      visual: Green checkmark with gentle expansion animation
      duration: 800ms
      text: Action-specific success message
      spiritual_touch: "Barakallahu feeki" (May Allah bless you) for major completions
```

### Performance Optimization

```yaml
loading_performance:
  skeleton_screens:
    implementation: Show immediate skeleton on screen entry
    benefit: Perceived performance improvement
    design: Islamic-themed placeholder content
    accessibility: Screen reader announces "Loading content"

  progressive_loading:
    priority_content: Prayer times and family safety features load first
    secondary_content: Historical places and sermons load after core features
    background_loading: Non-critical content loads in background
    cache_strategy: Prioritize frequently accessed content

  offline_first:
    cached_content: Show cached content immediately with refresh option
    update_strategy: Background updates with subtle notification
    offline_indicators: Clear indication when content is cached vs live
    graceful_degradation: Full functionality with cached data

  battery_consideration:
    efficient_animations: Optimize animations for battery life during long pilgrimage days
    loading_timeouts: Reasonable timeouts to prevent battery drain
    background_limits: Minimal background loading during low battery
    user_control: Options to reduce animations for battery saving
```

### Cultural Sensitivity in Loading States

**Islamic Design Elements:**
- Use geometric patterns instead of representational imagery
- Incorporate crescent moon and star motifs respectfully
- Apply gold and blue color schemes reminiscent of Islamic architecture
- Include Arabic calligraphy-inspired flourishes (non-textual)

**Messaging Guidelines:**
- Use patient, respectful language acknowledging the sacred context
- Include Islamic concepts like Sabr (patience) when appropriate
- Avoid anxiety-inducing urgency during spiritual activities
- Provide spiritual perspective on waiting and delays

**Examples of Culturally Appropriate Loading Messages:**
- "Preparing your spiritual companion..." (app startup)
- "Connecting you to the sacred..." (location/prayer services)
- "Gathering your family safely..." (family finder)
- "Accessing blessed content..." (sermon loading)
- "Your patience is rewarded..." (long loading processes)

## Responsive Design Strategy

### Device Size Considerations

**Target Android Devices** (API 21+ per PRD)
- **Compact**: 5.0" screens (1080x1920) - Minimum viable size
- **Standard**: 5.5"-6.1" screens (1080x2340) - Primary target
- **Large**: 6.2"-6.7" screens (1440x3200) - Premium experience
- **Foldable**: Future consideration for expanding market

**Adaptive Layout Principles**
- **Bottom Navigation**: Fixed height across all screen sizes (72dp)
- **Content Areas**: Flexible with minimum touch target maintenance
- **Typography**: Responsive scaling while maintaining readability
- **Spacing**: Consistent 16dp base unit with proportional scaling

### Orientation Handling

**Portrait Primary** (Pilgrimage Context)
- All core features optimized for portrait usage
- Landscape support for map views and sermon playback
- Orientation lock options for ritual counter usage

**Critical Feature Considerations**
- **GPS Counters**: Portrait-only to prevent accidental rotation during rituals
- **Family Map**: Landscape support for better spatial awareness
- **Sermon Player**: Landscape support for caption readability
- **Prayer Times**: Portrait primary with landscape adaptation

## Performance & Battery Optimization

### Battery Life Requirements (12+ Hours per PRD)

**GPS Services Optimization**
- Adaptive GPS polling based on user activity (stationary vs. moving)
- GPS accuracy degradation gracefully handled with manual fallbacks
- Background location services limited to essential family safety features
- Battery status integration with feature availability notifications

**Data Usage Optimization**
- **Prayer Times**: 24-hour caching reduces API calls
- **Sermon Content**: Audio compression and predictive caching
- **Family Tracking**: Efficient data packaging for location updates
- **Offline Priority**: Core features available without internet connectivity

**Memory Management**
- **SQLite Caching**: Efficient local storage for offline functionality
- **Image Optimization**: Historical place photos with progressive loading
- **Audio Streaming**: Optimized buffering preventing memory overflow
- **Background Processing**: Minimal background tasks to preserve battery

## Platform Integration & Native Features

### Android-Specific Requirements

**Notification System**
- **Prayer Time Notifications**: Local scheduling with accurate timing
- **Family Safety Alerts**: High-priority notifications for emergency situations
- **Background Playback**: Sermon audio with notification controls
- **Do Not Disturb Integration**: Respect system DND settings with exception for emergencies

**Location Services**
- **Google Maps API**: High-precision GPS for ritual assistance
- **Google Play Location**: Battery-optimized location for family tracking
- **Offline Maps**: Cached map data for poor connectivity areas
- **Geofencing**: Automatic prayer time adjustments based on location

**Device Integration**
- **Bluetooth Low Energy**: Family tracking in close proximity
- **WiFi Direct**: Medium-range family communication
- **Camera**: QR code scanning for family group joining
- **Storage**: Local SQLite database for offline functionality
- **Biometric Authentication**: Secure premium feature access

### Security & Privacy Implementation

**Islamic Privacy Values**
- **Prayer Time Privacy**: Location tracking paused during prayer times
- **Data Minimization**: Only collect data essential for functionality
- **Family Data Encryption**: End-to-end encryption for family location sharing
- **User Consent**: Clear consent management for all data collection

**GDPR Compliance** (European Users)
- **Data Portability**: User data export functionality
- **Right to Deletion**: Complete user data removal capability
- **Consent Management**: Granular privacy controls
- **Data Processing Transparency**: Clear explanation of data usage

## Implementation Priorities & Development Phases

### Phase 1: Foundation (Epic 1)
**Timeline**: Weeks 1-4
**Focus**: Core infrastructure, authentication, Prayer Times
**Deliverables**:
- Complete Prayer Times tab with Al Adhan API integration
- User authentication and subscription system
- Basic app navigation and Material Design 3 theming

### Phase 2: Core Pilgrimage Features (Epic 2)
**Timeline**: Weeks 5-8
**Focus**: Knowledge Hub, GPS ritual assistance
**Deliverables**:
- Hajj and Umrah guides with progress tracking
- Manual and GPS-guided ritual counters
- Premium feature differentiation implementation

### Phase 3: Content Platform (Epic 3)
**Timeline**: Weeks 9-12
**Focus**: Friday Sermon integration and audio-caption system
**Deliverables**:
- YouTube API integration with sophisticated search algorithm
- Unified audio-caption streaming with just_audio
- Multi-language sermon support

### Phase 4: Planning & Safety (Epics 4-5)
**Timeline**: Weeks 13-16
**Focus**: Historical Places, Family Finder, MyItineraries
**Deliverables**:
- Historical places database with Hadith/Quran references
- Multi-technology family tracking system
- Itinerary management and planning tools

### Phase 5: Integration & Polish (Epic 6)
**Timeline**: Weeks 17-20
**Focus**: Home dashboard integration, optimization
**Deliverables**:
- Complete Home tab with news, weather, feature grid
- Crowd insights integration
- Performance optimization and battery life enhancement

## Success Metrics & Validation

### User Experience Success Criteria

**Usability Metrics**
- Prayer time accuracy: 99.9% precision across all global locations
- GPS ritual counting: <5% manual override usage in normal conditions
- Family location accuracy: <10 meter precision in GPS-optimal conditions
- App launch time: <3 seconds on target devices
- GPS lock time: <2 seconds in clear sky conditions

**Engagement Metrics**
- Daily active usage during pilgrimage season: >80% of registered users
- Average session duration: 15+ minutes during pilgrimage activities
- Feature adoption: >60% of premium users utilize GPS ritual assistance
- Family safety usage: >90% of groups utilize Family Finder features

**Business Success Metrics**
- Premium conversion rate: 15% within 12 months (per PRD target)
- Customer satisfaction: >4.5/5 rating on Google Play Store
- Retention rate: >80% month-over-month during pilgrimage seasons
- Support ticket volume: <2% of active users require assistance

### Cultural Sensitivity Validation

**Islamic Compliance Review**
- Religious content accuracy verified by Islamic scholars
- Cultural appropriateness tested across diverse Muslim communities
- Privacy practices aligned with Islamic values and principles
- User interface respectful of sacred space contexts

**Multi-Cultural Testing**
- Language accuracy and cultural nuance validation across 6 supported languages
- Regional Islamic practice variations accommodated in design
- Accessibility testing with diverse age groups and technical literacy levels

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-09-23 | 3.0 | Complete specification based on validated PRD with Epic structure | Sally (UX Expert) |
| 2025-09-23 | 2.0 | Initial ziarah-specific rewrite (deprecated) | Sally (UX Expert) |
| 2025-09-23 | 1.0 | Generic information architecture (deprecated) | Sally (UX Expert) |

---

## Next Steps & Handoff Requirements

### For Development Team
1. **Technical Architecture Review**: Validate GPS accuracy solutions and multi-technology family tracking implementation
2. **API Cost Modeling**: Establish YouTube API rate limiting and cost optimization strategies at scale
3. **Performance Testing Environment**: Set up technical validation for 12+ hour battery life requirements
4. **Islamic Content Validation**: Establish scholar review process for religious content accuracy

### For Design Team
1. **Visual Design System**: Create comprehensive Islamic-appropriate design library
2. **Interactive Prototypes**: Build high-fidelity prototypes for GPS ritual interfaces and Family Finder workflows
3. **Accessibility Testing**: Validate WCAG AA compliance with elderly users and assistive technology
4. **Cultural Sensitivity Review**: Conduct design validation across diverse Muslim cultural contexts

### For Product Team
1. **Premium Feature Strategy**: Refine freemium conversion optimization based on UX specification
2. **Launch Market Research**: Validate target user personas and regional variations
3. **Success Metrics Framework**: Implement tracking for user experience and business success criteria
4. **Stakeholder Review**: Present comprehensive UX specification for final approval and development handoff

---

*This specification serves as the definitive UX foundation for ziarah development, ensuring that the final product delivers exceptional spiritual assistance while respecting Islamic values and supporting the global Muslim pilgrimage community.*