# Epic List

## Epic 1: Foundation & Prayer Times Infrastructure
**Goal:** Establish project foundation with authentication system and complete Prayer Times tab functionality, delivering immediate value through accurate global prayer times with Al Adhan API integration and Hijri calendar conversion while setting up infrastructure for advanced features.

## Epic 2: Knowledge Hub & GPS-Guided Ritual Assistance
**Goal:** Implement complete Knowledge Hub tab with Ibadah Guides for Hajj and Umrah (with progress trackers) and MyIbadah GPS-guided Tawaf/Sa'i counters, providing the core pilgrimage assistance value that differentiates Ziarah from basic Islamic apps.

## Epic 3: Friday Sermon Integration & Content Platform
**Goal:** Deploy complete Friday Sermon tab with sophisticated YouTube API integration using @tubesermon channel, implementing the multi-step search algorithm (date/language/mosque filtering) for Live and Last Friday sermons in 6 languages from Mecca and Medina.

## Epic 4: Historical Places & MyItineraries System
**Goal:** Implement complete Historical Places tab with comprehensive Islamic site database (name, period, category, description, GPS, Hadith/Quran references) and integrated MyItineraries functionality, enabling custom pilgrimage planning and itinerary management.

## Epic 5: Family Safety System
**Goal:** Deploy Family Finder with QR group creation, multi-technology tracking (Bluetooth/WiFi Direct/Google Maps), preset messaging system ("Come to me", "Go to hotel"), and directional guidance to family members, ensuring family safety during crowded pilgrimage conditions.

## Epic 6: Home Dashboard Complete Integration
**Goal:** Implement complete Home tab with news integration (external browser links), weather forecast for holy cities, arrow pointing to selected saved location in Quick Map middle section, and 2x2 tiled icons (Quick Map, Family Finder, MyItineraries, Crowd Insights) with all dependencies resolved and tiles fully functional.
