# Requirements

## Functional Requirements

**FR1:** The app shall provide GPS-guided Tawaf counter in MyIbadah tools that automatically tracks pilgrim progress around the Kaaba using Google Maps API with manual override capabilities when GPS accuracy is compromised

**FR2:** The app shall provide GPS-guided Sa'i counter in MyIbadah tools that automatically tracks pilgrim progress between Safa and Marwah using Google Maps API with manual override capabilities

**FR3:** The app shall implement Family Finder with QR code group creation (with preset expiry dates), Bluetooth/WiFi Direct detection, Google Maps API integration, preset messaging system ("Come to me", "Go to hotel"), and directional guidance to family members

**FR4:** The app shall integrate with @tubesermon YouTube channel using YouTube Data API to provide Live Friday Sermon and Last Friday Sermon from Mecca and Medina with specific search algorithms using Hijri date conversion, language keywords, and mosque-specific filters for 6 languages (English, Persian, Turkish, Urdu, Malay, Indonesian)

**FR5:** The app shall provide local daily prayer times using Al Adhan API (https://aladhan.com/prayer-times-api) with azan notifications and Gregorian to Hijri date conversion using Islamic Calendar API (https://aladhan.com/islamic-calendar-api)

**FR6:** The app shall display News section with Hajj/Umrah news titles that direct users to external browser when clicked

**FR7:** The app shall display weather forecast specifically for holy cities (Mecca and Medina) in the Home tab top section

**FR8:** The app shall provide Quick Map functionality allowing users to add and save custom locations (Hotel, Gate, etc.) with arrow pointing to selected location in Home tab middle section

**FR9:** The app shall provide Crowd Insights using Kaggle dataset (user ziya07, CC0 Public Domain) showing data-driven crowd predictions for holy sites in Mecca, Medina, and surrounding pilgrimage locations

**FR10:** The app shall provide MyItineraries feature connected to Historical Places tab, allowing users to view and edit custom pilgrimage itineraries

**FR11:** The app shall provide comprehensive Ibadah Guide for Hajj with progress tracker in Knowledge Hub tab

**FR12:** The app shall provide comprehensive Ibadah Guide for Umrah with progress tracker in Knowledge Hub tab

**FR13:** The app shall display Historical Places with detailed information including name, historical period, category, description, GPS locations, access information with disclaimers, Hadith/Quran references, "Add to MyItinerary" button, and "Get directions" button

**FR14:** The app shall implement freemium subscription model with $10/year premium tier and secure payment processing through Google Play Billing

**FR15:** The app shall provide offline functionality for critical features including prayer times, manual ritual counters, and cached historical information

## Non-Functional Requirements

**NFR1:** The app shall maintain 99.5% uptime for critical features (prayer times, GPS tracking) with <2 second response times for location-based queries

**NFR2:** The app shall launch in <3 seconds and acquire GPS location within <2 seconds on target Android devices (API level 21+)

**NFR3:** The app shall function reliably in offline mode for core features when network connectivity is poor or unavailable

**NFR4:** The app shall implement end-to-end encryption for family location data and personal information to ensure privacy compliance

**NFR5:** The app shall optimize API usage through caching strategies to maintain profitability at $10/year subscription price point

**NFR6:** The app shall support devices with GPS, Bluetooth 4.0+, and WiFi capabilities while maintaining maximum 500MB storage footprint

**NFR7:** The app shall ensure Islamic data sensitivity by preventing tracking during prayer times and private worship periods

**NFR8:** The app shall maintain GDPR compliance for European users with clear data retention policies and user consent management

**NFR9:** The app shall optimize battery usage to support 12+ hour pilgrimage days through efficient location services, background processing management, and adaptive refresh rates

**NFR10:** The Friday Sermon search algorithm shall find exactly 1 matching video per search query using the specified multi-step filtering process (date → language → mosque → validation)
