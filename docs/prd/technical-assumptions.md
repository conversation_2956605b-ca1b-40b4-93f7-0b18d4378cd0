# Technical Assumptions

## Repository Structure: Monorepo
**Single repository containing Flutter mobile app, Firebase backend configuration, and shared documentation. This approach simplifies dependency management, enables atomic commits across frontend/backend changes, and reduces complexity for solo/small team development while supporting future cross-platform expansion.**

## Service Architecture
**Hybrid approach combining monolithic Flutter app with serverless backend services. Core app functionality runs as single deployable unit while leveraging Firebase Functions for real-time features (family tracking, notifications) and external API orchestration. This balances development simplicity with scalability for real-time location services.**

## Testing Requirements
**Unit + Integration testing focused on critical path validation. Priority testing areas: GPS accuracy simulation, API integration reliability, offline functionality, and subscription flow validation. Manual testing required for physical location features and family finder scenarios in crowded environments.**

## Additional Technical Assumptions and Requests

**Frontend Framework & Platform:**
- **Flutter 3.x** for Android development with future iOS expansion capability
- **Target Android API Level 21+** (Android 5.0+) for maximum device compatibility
- **Material Design 3** with Islamic-appropriate customizations for consistent UX

**Backend & Infrastructure:**
- **Firebase suite** for authentication, real-time database, cloud messaging, and analytics
- **Google Cloud Platform** hosting for scalability and API integration ecosystem
- **SQLite** for local caching and offline-first data persistence

**External API Integration:**
- **Al <PERSON>han API** for prayer times and Islamic calendar conversion
- **YouTube Data API v3** for @tubesermon channel integration with strict rate limiting
- **Google Maps API** for GPS-guided ritual assistance and family location services
- **Kaggle CC0 dataset integration** for crowd insights (user ziya07 dataset)

**Performance & Optimization:**
- **Offline-first architecture** with background sync for prayer times and historical content
- **API caching strategies** with 24-hour prayer time cache and intelligent refresh policies
- **Battery optimization** through adaptive GPS polling and background service management
- **Progressive loading** for historical places content and sermon transcripts

**Security & Compliance:**
- **End-to-end encryption** for family location data using Firebase security rules
- **Islamic data sensitivity** with prayer time detection to pause location tracking
- **GDPR compliance** implementation with clear consent management for EU users
- **Google Play Billing integration** for secure subscription processing

**Development & Deployment:**
- **CI/CD pipeline** using GitHub Actions for automated testing and Play Store deployment
- **Version control strategy** with feature branches and semantic versioning
- **Error tracking** via Firebase Crashlytics for production issue monitoring
- **Analytics implementation** using Firebase Analytics with privacy-compliant user behavior tracking
