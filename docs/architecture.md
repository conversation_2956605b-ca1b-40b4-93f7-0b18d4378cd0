# Ziarah Fullstack Architecture Document

## Introduction

This document outlines the complete fullstack architecture for Ziarah, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

This unified approach combines what would traditionally be separate backend and frontend architecture documents, streamlining the development process for modern fullstack applications where these concerns are increasingly intertwined.

### Starter Template or Existing Project

**N/A - Greenfield project**

This is a greenfield Flutter mobile application for Islamic pilgrimage assistance. No existing starter templates or codebases are being extended. The project will be built from scratch with Firebase backend services, targeting Android initially with future iOS expansion capability.

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-23 | 1.0 | Initial fullstack architecture document | Winston (Architect) |

## High Level Architecture

### Technical Summary

Ziarah employs a **hybrid mobile-first architecture** combining Flutter's cross-platform framework with Firebase's serverless backend ecosystem. The **offline-first, real-time synchronization** approach ensures core pilgrimage features (prayer times, manual counters, family tracking) operate reliably in challenging connectivity environments while leveraging cloud services for enhanced premium features. The architecture integrates **multi-technology location services** (GPS, Bluetooth Low Energy, WiFi Direct) through Firebase Realtime Database for family safety coordination, while **YouTube API integration** with sophisticated search algorithms delivers authentic Islamic content. **Google Cloud Platform hosting** provides scalable infrastructure supporting the projected 100,000 downloads with $10/year freemium sustainability, ensuring 99.5% uptime for critical spiritual guidance during pilgrimage.

### Platform and Infrastructure Choice

Based on PRD requirements and technical assumptions, I'm recommending:

**Recommended Platform: Firebase + Google Cloud Platform**

**Alternative Options Considered:**
1. **Firebase + GCP (Recommended)**: Rapid development, real-time capabilities, excellent Flutter integration, cost-effective for freemium model
2. **AWS Full Stack**: More enterprise features but higher complexity for mobile-first approach and steeper learning curve
3. **Supabase + Vercel**: Open-source alternative but limited real-time family tracking capabilities and less mature Flutter ecosystem

**Platform:** Google Cloud Platform with Firebase
**Key Services:** Firebase Auth, Realtime Database, Cloud Functions, Firebase Storage, Cloud Messaging, YouTube Data API, Google Maps API, Al Adhan API
**Deployment Host and Regions:** Global deployment with primary regions in Middle East (asia-west1), Europe (europe-west1), and North America (us-central1) for optimal pilgrimage user coverage

### Repository Structure

**Structure:** Monorepo with feature-based organization
**Monorepo Tool:** Flutter's built-in package system with melos for mono-repo management
**Package Organization:** Feature-driven modules (prayer_times, family_finder, gps_counter, sermon_player) with shared core utilities and Islamic-specific components

Rationale: Monorepo approach enables atomic commits across mobile app and cloud functions, simplifies dependency management for Islamic date calculations and prayer time algorithms, and supports efficient code sharing between features while maintaining clear separation of concerns for different pilgrimage functionalities.

### High Level Architecture Diagram

```mermaid
graph TD
    A[Mobile Users] --> B[Flutter Mobile App]
    B --> C[Firebase Authentication]
    B --> D[Google Maps API]
    B --> E[YouTube Data API]
    B --> F[Al Adhan Prayer API]

    B --> G[Firebase Realtime Database]
    B --> H[Firebase Cloud Functions]
    B --> I[Firebase Storage]

    G --> J[Family Location Data]
    G --> K[User Preferences]
    G --> L[Offline Cache]

    H --> M[Prayer Time Calculations]
    H --> N[Sermon Search Algorithm]
    H --> O[Family Safety Notifications]

    I --> P[Historical Places Images]
    I --> Q[Cached Audio Content]

    R[External APIs] --> F
    R --> E
    R --> S[Kaggle Crowd Data]

    T[Device Hardware] --> U[GPS/Location]
    T --> V[Bluetooth LE]
    T --> W[WiFi Direct]
    T --> X[Local SQLite]

    B --> T
```

### Architectural Patterns

- **Offline-First Architecture:** SQLite local storage with Firebase background sync ensures core functionality during poor connectivity - _Rationale:_ Pilgrimage environments often have unreliable network access, spiritual activities cannot depend on internet
- **Real-Time Observer Pattern:** Firebase Realtime Database for family location updates and emergency notifications - _Rationale:_ Family safety requires immediate location updates and emergency communication capabilities
- **Repository Pattern:** Abstract data access for prayer times, historical places, and user preferences with offline/online switching - _Rationale:_ Enables seamless offline/online data flow and simplifies testing of Islamic calendar calculations
- **BLoC State Management:** Business Logic Components for complex state management across GPS tracking, family coordination, and content streaming - _Rationale:_ Provides predictable state management for safety-critical features and complex pilgrimage workflows
- **Multi-Technology Integration Pattern:** Facade pattern for GPS/Bluetooth/WiFi location services with graceful degradation - _Rationale:_ Family safety requires redundant location technologies when GPS fails in crowded sacred spaces
- **Freemium Feature Gate Pattern:** Decorator pattern for premium feature access control with seamless upgrade flow - _Rationale:_ Enables sustainable business model while providing essential Islamic functions for free
- **Islamic Calendar Abstraction:** Strategy pattern for different Hijri calculation methods and prayer time schools - _Rationale:_ Accommodates diverse Islamic practices across global Muslim community
- **Content Streaming Pipeline:** Chain of responsibility for YouTube content extraction, audio processing, and caption synchronization - _Rationale:_ Delivers authentic Islamic content with efficient data usage and battery optimization

## Tech Stack

This is the **DEFINITIVE technology selection** for the entire Ziarah project. This table serves as the single source of truth - all development must use these exact versions.

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| Frontend Language | Dart | 3.1+ | Flutter mobile app development | Type-safe, compiled language with excellent async support for real-time location updates |
| Frontend Framework | Flutter | 3.13+ | Cross-platform mobile framework | Superior GPS/location performance, offline-first capabilities, Google services integration |
| UI Component Library | Material Design 3 | Built-in | Islamic-appropriate UI components | Native Flutter theming with Islamic color customization, accessibility compliance |
| State Management | flutter_bloc | 8.1+ | Predictable state management | Handles complex family tracking states, GPS counter logic, and real-time data flows |
| Backend Language | Dart | 3.1+ | Firebase Cloud Functions | Unified language across stack, reduces context switching for solo/small team development |
| Backend Framework | Firebase Functions | Latest | Serverless API endpoints | Auto-scaling, pay-per-use pricing ideal for freemium model, real-time database triggers |
| API Style | REST + Real-time | HTTP/WebSocket | RESTful APIs with real-time subscriptions | REST for data operations, WebSocket for family location updates and emergency alerts |
| Database | Firebase Firestore + SQLite | Latest | Primary cloud + local cache | Firestore for real-time sync, SQLite for offline-first prayer times and user data |
| Cache | Firebase Firestore Cache + SQLite | Built-in | Multi-layer caching strategy | 24-hour prayer time cache, historical places offline data, sermon content buffering |
| File Storage | Firebase Storage | Latest | Images, audio content, user uploads | Scalable storage for historical place photos, sermon audio cache, user profile images |
| Authentication | Firebase Auth | Latest | User accounts and premium verification | Google Sign-In integration, secure premium subscription validation, privacy compliance |
| Frontend Testing | flutter_test + integration_test | Built-in | Unit and integration testing | Critical path testing for GPS accuracy, family safety features, offline functionality |
| Backend Testing | Firebase Test Lab + functions-framework | Latest | Cloud function testing and device testing | Automated testing across Android devices, prayer time calculation validation |
| E2E Testing | patrol | 2.0+ | End-to-end user journey testing | Real device testing for GPS ritual assistance, family finder scenarios, subscription flows |
| Build Tool | Flutter Build | Built-in | Mobile app compilation and packaging | Optimized builds for Android release, future iOS support, automated signing |
| Bundler | Flutter Build Runner | Built-in | Code generation and asset bundling | JSON serialization, route generation, Islamic calendar data compilation |
| IaC Tool | Firebase CLI + GitHub Actions | Latest | Infrastructure as code deployment | Automated Firebase deployment, environment management, CI/CD pipeline integration |
| CI/CD | GitHub Actions | Latest | Automated testing and deployment | Parallel testing, automated Play Store deployment, Firebase function deployment |
| Monitoring | Firebase Crashlytics + Analytics | Latest | Crash reporting and usage analytics | Privacy-compliant user behavior tracking, performance monitoring, error reporting |
| Logging | Firebase Performance + flutter_logs | Latest | Performance monitoring and debugging | Battery usage tracking, GPS accuracy monitoring, API response time analysis |
| CSS Framework | Flutter Theming | Built-in | Islamic-appropriate visual design | Material Design 3 with custom Islamic color palette, typography, and spacing |
| Audio Processing | youtube_explode_dart | 1.7+ | YouTube sermon extraction | Extracts audio streams and captions from @tubesermon channel |
| Audio Playback | just_audio | 0.9+ | Unified audio-caption playback | Background playback with synchronized caption display |
| QR Code | qr_flutter + qr_code_scanner | Latest | Family group QR creation/scanning | Generate group QR codes with expiry, scan to join groups |
| Bluetooth | flutter_blue_plus | 1.17+ | Close-range family tracking | BLE proximity detection for Family Finder backup |
| WiFi Direct | wifi_direct_flutter | Latest | Medium-range family communication | P2P connection when GPS/internet unavailable |
| Location Services | geolocator | 10.0+ | GPS positioning and distance | High-precision location for ritual counters and family tracking |
| HTTP Client | dio | 5.3+ | API calls and caching | Weather API, news scraping, Kaggle dataset access |
| Weather API | openweathermap_api | 0.1+ | Holy cities weather forecast | Mecca and Medina weather data for Home tab |
| Data Analysis | csv + http | Built-in | Kaggle crowd dataset processing | Parse CC0 crowd data from user ziya07 for insights |
| Web Scraping | html + http | Built-in | News headlines extraction | Hajj/Umrah news for Home tab external links |

## Data Models

Based on the PRD requirements and Epic structure, I've identified the core data models that will be shared between frontend and backend:

### User

**Purpose:** Central user entity managing authentication, preferences, and subscription status

**Key Attributes:**
- id: String - Unique Firebase user identifier
- email: String - User authentication email
- displayName: String? - Optional display name for family identification
- isPremium: bool - Premium subscription status
- subscriptionExpiry: DateTime? - Premium subscription end date
- location: LatLng? - Current user location for prayer times
- prayerCalculationMethod: String - Islamic calculation method preference
- madhab: String - Islamic school (Hanafi/Shafi) for prayer calculations
- language: String - App interface language preference
- sermonLanguages: List<String> - Preferred sermon languages (premium)
- hijriMethod: String - Hijri calendar calculation method
- createdAt: DateTime - Account creation timestamp
- lastActive: DateTime - Last app usage for analytics

#### TypeScript Interface
```typescript
interface User {
  id: string;
  email: string;
  displayName?: string;
  isPremium: boolean;
  subscriptionExpiry?: Date;
  location?: {
    latitude: number;
    longitude: number;
  };
  prayerCalculationMethod: string;
  madhab: 'hanafi' | 'shafi';
  language: string;
  sermonLanguages: string[];
  hijriMethod: string;
  createdAt: Date;
  lastActive: Date;
}
```

#### Relationships
- One-to-many with FamilyGroup (as creator or member)
- One-to-many with SavedLocation
- One-to-many with Itinerary
- One-to-one with UserPreferences

### FamilyGroup

**Purpose:** Family safety coordination with QR-based group creation and multi-technology tracking

**Key Attributes:**
- id: String - Unique group identifier
- name: String - Family group name
- creatorId: String - User ID of group creator
- qrCode: String - Generated QR code for joining
- qrExpiry: DateTime - QR code expiration date
- isActive: bool - Group active status
- memberIds: List<String> - List of member user IDs
- emergencyContactPhone: String? - Emergency contact number
- hotelLocation: LatLng? - Shared hotel location reference
- createdAt: DateTime - Group creation timestamp
- lastActivity: DateTime - Last group activity for cleanup

#### TypeScript Interface
```typescript
interface FamilyGroup {
  id: string;
  name: string;
  creatorId: string;
  qrCode: string;
  qrExpiry: Date;
  isActive: boolean;
  memberIds: string[];
  emergencyContactPhone?: string;
  hotelLocation?: {
    latitude: number;
    longitude: number;
  };
  createdAt: Date;
  lastActivity: Date;
}
```

#### Relationships
- Many-to-many with User (group members)
- One-to-many with FamilyLocationUpdate
- One-to-many with FamilyMessage

### FamilyLocationUpdate

**Purpose:** Real-time location sharing for family safety with multi-technology support

**Key Attributes:**
- id: String - Update identifier
- groupId: String - Family group reference
- userId: String - Member providing location
- location: LatLng - Current GPS coordinates
- accuracy: double - Location accuracy in meters
- locationMethod: String - GPS/Bluetooth/WiFi source
- batteryLevel: int - Device battery percentage
- isEmergency: bool - Emergency status flag
- timestamp: DateTime - Location update time
- expiresAt: DateTime - Auto-cleanup timestamp

#### TypeScript Interface
```typescript
interface FamilyLocationUpdate {
  id: string;
  groupId: string;
  userId: string;
  location: {
    latitude: number;
    longitude: number;
  };
  accuracy: number;
  locationMethod: 'gps' | 'bluetooth' | 'wifi_direct';
  batteryLevel: number;
  isEmergency: boolean;
  timestamp: Date;
  expiresAt: Date;
}
```

#### Relationships
- Many-to-one with FamilyGroup
- Many-to-one with User

### FamilyMessage

**Purpose:** Preset messaging system for family communication during pilgrimage

**Key Attributes:**
- id: String - Message identifier
- groupId: String - Family group reference
- senderId: String - User sending message
- messageType: String - Preset message type
- customMessage: String? - Custom text if not preset
- senderLocation: LatLng? - Location when message sent
- timestamp: DateTime - Message send time
- isEmergency: bool - Emergency priority flag
- deliveredTo: List<String> - User IDs who received message
- readBy: List<String> - User IDs who read message

#### TypeScript Interface
```typescript
interface FamilyMessage {
  id: string;
  groupId: string;
  senderId: string;
  messageType: 'come_to_me' | 'go_to_hotel' | 'im_safe' | 'need_help' | 'wait_for_me' | 'custom';
  customMessage?: string;
  senderLocation?: {
    latitude: number;
    longitude: number;
  };
  timestamp: Date;
  isEmergency: boolean;
  deliveredTo: string[];
  readBy: string[];
}
```

#### Relationships
- Many-to-one with FamilyGroup
- Many-to-one with User (sender)

### HistoricalPlace

**Purpose:** Islamic historical sites database with Hadith/Quran references and navigation integration

**Key Attributes:**
- id: String - Unique place identifier
- name: String - Historical place name
- nameArabic: String? - Arabic name if applicable
- category: String - Place type (Mosque, Cave, Battlefield)
- historicalPeriod: String - Time period (Prophet SAW, Rashidun, etc)
- description: String - Detailed historical description
- location: LatLng - GPS coordinates
- city: String - Mecca, Medina, or Road Between
- accessInfo: String - Current access status and guidelines
- disclaimer: String - Safety and visiting disclaimers
- hadithReferences: List<String> - Authenticated Hadith citations
- quranReferences: List<String> - Relevant Quran verses
- imageUrls: List<String> - Historical photos
- isVerified: bool - Scholar verification status
- isPremiumContent: bool - Premium feature flag

#### TypeScript Interface
```typescript
interface HistoricalPlace {
  id: string;
  name: string;
  nameArabic?: string;
  category: 'mosque' | 'cave' | 'battlefield' | 'mountain' | 'well' | 'house';
  historicalPeriod: 'prophet' | 'rashidun' | 'umayyad' | 'abbasid' | 'modern';
  description: string;
  location: {
    latitude: number;
    longitude: number;
  };
  city: 'mecca' | 'medina' | 'road_between';
  accessInfo: string;
  disclaimer: string;
  hadithReferences: string[];
  quranReferences: string[];
  imageUrls: string[];
  isVerified: boolean;
  isPremiumContent: boolean;
}
```

#### Relationships
- Many-to-many with Itinerary
- One-to-many with SavedLocation

### Itinerary

**Purpose:** Custom pilgrimage planning with historical places and optimal timing

**Key Attributes:**
- id: String - Itinerary identifier
- userId: String - Owner user ID
- name: String - Itinerary name
- description: String - Trip description
- type: String - Hajj, Umrah, or Combined
- placeIds: List<String> - Ordered historical place IDs
- estimatedDuration: int - Total visit time in hours
- isShared: bool - Family sharing status
- crowdOptimized: bool - Optimized for crowd insights
- createdAt: DateTime - Creation timestamp
- lastModified: DateTime - Last update time

#### TypeScript Interface
```typescript
interface Itinerary {
  id: string;
  userId: string;
  name: string;
  description: string;
  type: 'hajj' | 'umrah' | 'combined';
  placeIds: string[];
  estimatedDuration: number;
  isShared: boolean;
  crowdOptimized: boolean;
  createdAt: Date;
  lastModified: Date;
}
```

#### Relationships
- Many-to-one with User
- Many-to-many with HistoricalPlace

### SavedLocation

**Purpose:** User-defined locations for Quick Map and navigation

**Key Attributes:**
- id: String - Location identifier
- userId: String - Owner user ID
- name: String - Custom location name
- category: String - Hotel, Gate, Meeting Point, etc
- location: LatLng - GPS coordinates
- notes: String? - Optional user notes
- isDefault: bool - Default location for arrow display
- createdAt: DateTime - Save timestamp

#### TypeScript Interface
```typescript
interface SavedLocation {
  id: string;
  userId: string;
  name: string;
  category: 'hotel' | 'gate' | 'meeting_point' | 'custom';
  location: {
    latitude: number;
    longitude: number;
  };
  notes?: string;
  isDefault: boolean;
  createdAt: Date;
}
```

#### Relationships
- Many-to-one with User

### RitualProgress

**Purpose:** GPS-guided and manual ritual counting with spiritual progress tracking

**Key Attributes:**
- id: String - Progress session identifier
- userId: String - Performing pilgrim
- ritualType: String - Tawaf or Sai
- isGpsGuided: bool - GPS vs manual counting
- currentRound: int - Current circuit number
- totalRounds: int - Target rounds (7 for both)
- gpsAccuracy: double? - GPS accuracy when applicable
- batteryAtStart: int - Initial battery level
- startTime: DateTime - Ritual start timestamp
- completedAt: DateTime? - Completion time
- isCompleted: bool - Ritual completion status

#### TypeScript Interface
```typescript
interface RitualProgress {
  id: string;
  userId: string;
  ritualType: 'tawaf' | 'sai';
  isGpsGuided: boolean;
  currentRound: number;
  totalRounds: number;
  gpsAccuracy?: number;
  batteryAtStart: number;
  startTime: Date;
  completedAt?: Date;
  isCompleted: boolean;
}
```

#### Relationships
- Many-to-one with User

### CrowdInsight

**Purpose:** Kaggle dataset-based crowd predictions for optimal pilgrimage timing

**Key Attributes:**
- id: String - Insight identifier
- placeId: String - Historical place reference
- placeName: String - Place name for quick reference
- hourOfDay: int - Hour (0-23) for prediction
- dayOfWeek: int - Day of week (1-7)
- crowdLevel: String - Low, Medium, High, Extreme
- crowdScore: int - Numerical crowd score (0-100)
- optimalVisitTime: String - Recommended time range
- lastUpdated: DateTime - Data refresh timestamp
- source: String - Always "kaggle_ziya07"
- seasonType: String - Hajj, Umrah, Regular

#### TypeScript Interface
```typescript
interface CrowdInsight {
  id: string;
  placeId: string;
  placeName: string;
  hourOfDay: number;
  dayOfWeek: number;
  crowdLevel: 'low' | 'medium' | 'high' | 'extreme';
  crowdScore: number;
  optimalVisitTime: string;
  lastUpdated: Date;
  source: 'kaggle_ziya07';
  seasonType: 'hajj' | 'umrah' | 'regular';
}
```

#### Relationships
- Many-to-one with HistoricalPlace

### NewsItem

**Purpose:** Hajj and Umrah news headlines with external browser links

**Key Attributes:**
- id: String - News item identifier
- title: String - News headline
- url: String - External link URL
- source: String - News source website
- publishedAt: DateTime - Original publication date
- category: String - Hajj, Umrah, General
- isVerified: bool - Editorial verification status
- language: String - Content language
- imageUrl: String? - Optional thumbnail image
- cachedAt: DateTime - Local cache timestamp

#### TypeScript Interface
```typescript
interface NewsItem {
  id: string;
  title: string;
  url: string;
  source: string;
  publishedAt: Date;
  category: 'hajj' | 'umrah' | 'general';
  isVerified: boolean;
  language: string;
  imageUrl?: string;
  cachedAt: Date;
}
```

#### Relationships
- Standalone entity with no direct relationships

### WeatherData

**Purpose:** Holy cities weather forecast for pilgrimage planning

**Key Attributes:**
- id: String - Weather data identifier
- city: String - Mecca or Medina
- currentTemp: int - Current temperature in Celsius
- feelsLike: int - Feels-like temperature
- humidity: int - Humidity percentage
- condition: String - Weather condition description
- icon: String - Weather icon identifier
- forecast3Day: List<Map> - 3-day forecast data
- lastUpdated: DateTime - Data refresh timestamp
- source: String - Weather API source

#### TypeScript Interface
```typescript
interface WeatherData {
  id: string;
  city: 'mecca' | 'medina';
  currentTemp: number;
  feelsLike: number;
  humidity: number;
  condition: string;
  icon: string;
  forecast3Day: Array<{
    date: Date;
    tempMax: number;
    tempMin: number;
    condition: string;
  }>;
  lastUpdated: Date;
  source: string;
}
```

#### Relationships
- Standalone entity with city-based lookup

### SermonContent

**Purpose:** YouTube sermon audio and caption caching with search algorithm support

**Key Attributes:**
- id: String - Content identifier
- youtubeVideoId: String - YouTube video ID
- title: String - Video title from YouTube
- mosque: String - Mecca or Medina
- language: String - Sermon language
- hijriDate: String - Hijri date in YYYY-MM-DD format
- audioUrl: String - Extracted audio stream URL
- captionText: String - Full caption/transcript text
- captionTimestamps: List<Map> - Caption segments with timing
- duration: int - Audio duration in seconds
- extractedAt: DateTime - Content extraction timestamp
- expiresAt: DateTime - Cache expiry (30 minutes)
- isLive: bool - Live vs archived sermon
- channelId: String - Always UCB0qibtjzOIemPjQSaoWkGg

#### TypeScript Interface
```typescript
interface SermonContent {
  id: string;
  youtubeVideoId: string;
  title: string;
  mosque: 'mecca' | 'medina';
  language: 'english' | 'persian' | 'turkish' | 'urdu' | 'malay' | 'indonesian';
  hijriDate: string;
  audioUrl: string;
  captionText: string;
  captionTimestamps: Array<{
    startTime: number;
    endTime: number;
    text: string;
  }>;
  duration: number;
  extractedAt: Date;
  expiresAt: Date;
  isLive: boolean;
  channelId: string;
}
```

#### Relationships
- Standalone entity with search-based retrieval

### PilgrimageGuideProgress

**Purpose:** Hajj and Umrah guide completion tracking with step-by-step progress

**Key Attributes:**
- id: String - Progress identifier
- userId: String - User tracking progress
- guideType: String - Hajj or Umrah
- currentStepId: String - Current guide step
- completedSteps: List<String> - Completed step IDs
- totalSteps: int - Total guide steps
- completionPercentage: double - Progress percentage
- startedAt: DateTime - Guide start time
- lastActivity: DateTime - Last step completion
- isCompleted: bool - Full guide completion status
- notes: String? - User notes or reflections

#### TypeScript Interface
```typescript
interface PilgrimageGuideProgress {
  id: string;
  userId: string;
  guideType: 'hajj' | 'umrah';
  currentStepId: string;
  completedSteps: string[];
  totalSteps: number;
  completionPercentage: number;
  startedAt: Date;
  lastActivity: Date;
  isCompleted: boolean;
  notes?: string;
}
```

#### Relationships
- Many-to-one with User
- References predefined guide step structure

## API Specification

Based on the chosen Firebase + Cloud Functions architecture with REST API style, here's the complete API specification:

### REST API Specification

```yaml
openapi: 3.0.0
info:
  title: Ziarah Pilgrimage Companion API
  version: 1.0.0
  description: Comprehensive Islamic pilgrimage assistance API with Firebase Cloud Functions backend
servers:
  - url: https://ziarah-firebase-project.cloudfunctions.net/api
    description: Firebase Cloud Functions production endpoint
  - url: https://ziarah-firebase-project.cloudfunctions.net/api-dev
    description: Firebase Cloud Functions development endpoint

security:
  - FirebaseAuth: []

# External API Integrations (called directly from Flutter app)
external_apis:
  al_adhan_prayer_times:
    url: "https://api.aladhan.com/v1/timings/{date}"
    method: GET
    description: "Direct API call for daily prayer times"
    client: "Flutter app calls directly (no backend proxy needed)"
    failure_strategy:
      cache_duration: "24 hours"
      offline_fallback: "Static prayer time calculations using stored location coordinates"
      backup_apis: ["IslamicFinder API", "MuslimPro API"]
      degraded_mode: "Manual prayer time entry with time zone calculation"
      critical_impact: "HIGH - Prayer times are core spiritual functionality"

  al_adhan_hijri_conversion:
    url: "https://api.aladhan.com/v1/gToH/{date}"
    method: GET
    description: "Gregorian to Hijri date conversion"
    client: "Flutter app calls directly"
    failure_strategy:
      cache_duration: "30 days"
      offline_fallback: "Local Hijri calculation algorithm using Umm al-Qura method"
      backup_calculation: "Built-in Islamic calendar conversion library"
      degraded_mode: "Gregorian date display with manual Hijri input option"
      critical_impact: "MEDIUM - Important for Islamic context but not prayer functionality"

  youtube_data_api:
    url: "https://www.googleapis.com/youtube/v3/"
    method: GET
    description: "Search @tubesermon channel for Friday sermons"
    client: "Firebase Cloud Functions proxy"
    failure_strategy:
      cache_duration: "7 days for popular sermons"
      offline_fallback: "Pre-downloaded sermon library (latest 10 sermons per language)"
      backup_content: "Static Islamic audio content and Quran recitations"
      degraded_mode: "Text-based Friday sermon summaries"
      critical_impact: "LOW - Premium feature, alternative spiritual content available"

  google_maps_api:
    url: "https://maps.googleapis.com/maps/api/"
    method: GET
    description: "GPS services for ritual assistance and family tracking"
    client: "Flutter app with Firebase Cloud Functions for complex operations"
    failure_strategy:
      cache_duration: "Indefinite for holy site coordinates"
      offline_fallback: "Static coordinates for Kaaba, Safa, Marwah, major gates"
      backup_positioning: "Device GPS with manual coordinate verification"
      degraded_mode: "Manual counters with compass-based direction assistance"
      critical_impact: "HIGH - Core GPS features but manual alternatives available"

  openweathermap_api:
    url: "https://api.openweathermap.org/data/2.5/"
    method: GET
    description: "Weather data for Mecca and Medina"
    client: "Firebase Cloud Functions with caching"
    failure_strategy:
      cache_duration: "6 hours"
      offline_fallback: "Historical weather averages by month for holy cities"
      backup_data: "Static seasonal weather guidance for pilgrimage preparation"
      degraded_mode: "Remove weather widget, show general pilgrimage weather advice"
      critical_impact: "LOW - Convenience feature, not essential for pilgrimage"

paths:
  /auth/verify-premium:
    get:
      summary: Verify user premium subscription status
      description: Validates Firebase Auth token and checks premium subscription
      responses:
        '200':
          description: Premium status verified
          content:
            application/json:
              schema:
                type: object
                properties:
                  isPremium: { type: boolean }
                  expiresAt: { type: string, format: date-time }
                  subscriptionTier: { type: string }

  /family-groups:
    post:
      summary: Create new family group with QR code
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name: { type: string }
                qrExpiry: { type: string, format: date-time }
                emergencyContact: { type: string }
      responses:
        '201':
          description: Family group created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FamilyGroup'

    get:
      summary: Get user's family groups
      responses:
        '200':
          description: List of user's family groups
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FamilyGroup'

  /family-groups/{groupId}/join:
    post:
      summary: Join family group via QR code
      parameters:
        - name: groupId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                qrCode: { type: string }
      responses:
        '200':
          description: Successfully joined group
        '400':
          description: Invalid or expired QR code

  /family-groups/{groupId}/locations:
    post:
      summary: Update family location (different capabilities by tier)
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                location:
                  type: object
                  properties:
                    latitude: { type: number }
                    longitude: { type: number }
                locationMethod:
                  type: string
                  enum: [bluetooth, wifi_direct, gps]
                  description: "Free: bluetooth/wifi_direct only, Premium: adds GPS"
                batteryLevel: { type: integer }
                isEmergency: { type: boolean }
      responses:
        '200':
          description: Location updated (features vary by subscription)
          content:
            application/json:
              schema:
                type: object
                properties:
                  success: { type: boolean }
                  availableFeatures:
                    type: object
                    properties:
                      gpsTracking: { type: boolean, description: "Premium only" }
                      presetMessaging: { type: boolean, description: "Premium only" }
                      lastKnownLocation: { type: boolean, description: "Premium only" }
                      basicTracking: { type: boolean, description: "Always true" }

    get:
      summary: Get all family member locations
      parameters:
        - name: groupId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Current family locations
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FamilyLocationUpdate'

  /family-groups/{groupId}/messages:
    post:
      summary: Send family messages (Premium feature)
      security:
        - FirebaseAuth: []
        - PremiumRequired: []
      description: "Preset messaging system requires premium subscription"
      parameters:
        - name: groupId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FamilyMessage'
      responses:
        '200':
          description: Message sent successfully
        '403':
          description: Premium subscription required

  /sermons/extract-content:
    post:
      summary: Extract audio and captions from YouTube using youtube_explode_dart
      description: "Cloud Function processes @tubesermon channel with multi-step search algorithm"
      security:
        - FirebaseAuth: []
        - PremiumRequired: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                mosque: { type: string, enum: [mecca, medina] }
                language: { type: string, enum: [english, persian, turkish, urdu, malay, indonesian] }
                sermonType: { type: string, enum: [live, last_friday] }
                targetDate: { type: string, description: "Gregorian date for Hijri conversion" }
      responses:
        '200':
          description: Audio URL and caption text extracted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  audioUrl: { type: string, description: "64kbps AAC/Opus stream URL" }
                  captionText: { type: string, description: "Full VTT/SRT caption text" }
                  captionTimestamps:
                    type: array
                    items:
                      type: object
                      properties:
                        startTime: { type: number }
                        endTime: { type: number }
                        text: { type: string }
                  duration: { type: integer }
                  cacheExpiresAt: { type: string, description: "30-minute cache expiry" }
                  youtubeVideoId: { type: string }
                  title: { type: string }
        '404':
          description: "No matching sermon found with search algorithm"
        '403':
          description: "Premium subscription required"

  /historical-places:
    get:
      summary: Get Islamic historical places
      parameters:
        - name: city
          in: query
          schema:
            type: string
            enum: [mecca, medina, road_between]
        - name: category
          in: query
          schema:
            type: string
        - name: period
          in: query
          schema:
            type: string
        - name: search
          in: query
          schema:
            type: string
      responses:
        '200':
          description: List of historical places
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HistoricalPlace'

  /historical-places/{placeId}:
    get:
      summary: Get historical place details (features vary by tier)
      parameters:
        - name: placeId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Place information with tier-appropriate content
          content:
            application/json:
              schema:
                type: object
                properties:
                  # Free tier gets these fields
                  id: { type: string }
                  name: { type: string }
                  category: { type: string }
                  description: { type: string }
                  location:
                    type: object
                    properties:
                      latitude: { type: number }
                      longitude: { type: number }
                  city: { type: string }
                  accessInfo: { type: string }

                  # Premium tier gets additional fields
                  hadithReferences:
                    type: array
                    items: { type: string }
                    description: "Premium only - Authenticated Hadith citations"
                  quranReferences:
                    type: array
                    items: { type: string }
                    description: "Premium only - Relevant Quran verses"
                  canAddToItinerary:
                    type: boolean
                    description: "Premium only - MyItineraries integration"
                  isPremiumContent: { type: boolean }

  /itineraries:
    get:
      summary: Get user itineraries (Premium only)
      security:
        - FirebaseAuth: []
        - PremiumRequired: []
      description: "MyItineraries feature requires premium subscription"
      responses:
        '200':
          description: List of user itineraries
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Itinerary'
        '403':
          description: Premium subscription required

    post:
      summary: Create itinerary (Premium only)
      security:
        - FirebaseAuth: []
        - PremiumRequired: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Itinerary'
      responses:
        '201':
          description: Itinerary created successfully
        '403':
          description: Premium subscription required

  /crowd-insights:
    get:
      summary: Get crowd predictions for holy sites
      parameters:
        - name: placeId
          in: query
          schema:
            type: string
        - name: date
          in: query
          schema:
            type: string
            format: date
        - name: hour
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: Crowd insights data
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CrowdInsight'

  /news:
    get:
      summary: Get Hajj and Umrah news headlines
      parameters:
        - name: category
          in: query
          schema:
            type: string
            enum: [hajj, umrah, general]
        - name: language
          in: query
          schema:
            type: string
      responses:
        '200':
          description: News headlines with external links
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/NewsItem'

  /weather:
    get:
      summary: Get weather forecast for holy cities
      parameters:
        - name: city
          in: query
          required: true
          schema:
            type: string
            enum: [mecca, medina]
      responses:
        '200':
          description: Weather data for holy cities
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WeatherData'

  /ritual-progress:
    post:
      summary: Save ritual progress (GPS features premium only)
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                ritualType: { type: string, enum: [tawaf, sai] }
                isGpsGuided:
                  type: boolean
                  description: "Premium only - free users get manual counters only"
                currentRound: { type: integer }
                # GPS-specific fields (premium only)
                gpsAccuracy: { type: number, description: "Premium only" }
                location:
                  type: object
                  description: "Premium only"
                  properties:
                    latitude: { type: number }
                    longitude: { type: number }
      responses:
        '200':
          description: Progress saved with appropriate features
        '403':
          description: GPS features require premium subscription

    get:
      summary: Get user's ritual progress history
      responses:
        '200':
          description: Ritual progress records
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RitualProgress'

components:
  securitySchemes:
    FirebaseAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: Firebase Authentication ID token
    PremiumRequired:
      type: apiKey
      in: header
      name: X-Premium-Required
      description: Premium subscription validation

  schemas:
    FamilyGroup:
      type: object
      properties:
        id: { type: string }
        name: { type: string }
        creatorId: { type: string }
        qrCode: { type: string }
        qrExpiry: { type: string, format: date-time }
        isActive: { type: boolean }
        memberIds: { type: array, items: { type: string } }
        emergencyContactPhone: { type: string }
        hotelLocation:
          type: object
          properties:
            latitude: { type: number }
            longitude: { type: number }

    FamilyLocationUpdate:
      type: object
      properties:
        id: { type: string }
        groupId: { type: string }
        userId: { type: string }
        location:
          type: object
          properties:
            latitude: { type: number }
            longitude: { type: number }
        accuracy: { type: number }
        locationMethod: { type: string, enum: [gps, bluetooth, wifi_direct] }
        batteryLevel: { type: integer }
        isEmergency: { type: boolean }
        timestamp: { type: string, format: date-time }

    FamilyMessage:
      type: object
      properties:
        id: { type: string }
        groupId: { type: string }
        senderId: { type: string }
        messageType: { type: string, enum: [come_to_me, go_to_hotel, im_safe, need_help, wait_for_me, custom] }
        customMessage: { type: string }
        senderLocation:
          type: object
          properties:
            latitude: { type: number }
            longitude: { type: number }
        timestamp: { type: string, format: date-time }
        isEmergency: { type: boolean }

    HistoricalPlace:
      type: object
      properties:
        id: { type: string }
        name: { type: string }
        nameArabic: { type: string }
        category: { type: string, enum: [mosque, cave, battlefield, mountain, well, house] }
        historicalPeriod: { type: string, enum: [prophet, rashidun, umayyad, abbasid, modern] }
        description: { type: string }
        location:
          type: object
          properties:
            latitude: { type: number }
            longitude: { type: number }
        city: { type: string, enum: [mecca, medina, road_between] }
        accessInfo: { type: string }
        disclaimer: { type: string }
        hadithReferences: { type: array, items: { type: string } }
        quranReferences: { type: array, items: { type: string } }
        imageUrls: { type: array, items: { type: string } }
        isVerified: { type: boolean }
        isPremiumContent: { type: boolean }

    Itinerary:
      type: object
      properties:
        id: { type: string }
        userId: { type: string }
        name: { type: string }
        description: { type: string }
        type: { type: string, enum: [hajj, umrah, combined] }
        placeIds: { type: array, items: { type: string } }
        estimatedDuration: { type: integer }
        isShared: { type: boolean }
        crowdOptimized: { type: boolean }

    CrowdInsight:
      type: object
      properties:
        id: { type: string }
        placeId: { type: string }
        placeName: { type: string }
        hourOfDay: { type: integer }
        dayOfWeek: { type: integer }
        crowdLevel: { type: string, enum: [low, medium, high, extreme] }
        crowdScore: { type: integer }
        optimalVisitTime: { type: string }
        lastUpdated: { type: string, format: date-time }
        source: { type: string, enum: [kaggle_ziya07] }
        seasonType: { type: string, enum: [hajj, umrah, regular] }

    NewsItem:
      type: object
      properties:
        id: { type: string }
        title: { type: string }
        url: { type: string }
        source: { type: string }
        publishedAt: { type: string, format: date-time }
        category: { type: string, enum: [hajj, umrah, general] }
        isVerified: { type: boolean }
        language: { type: string }
        imageUrl: { type: string }

    WeatherData:
      type: object
      properties:
        id: { type: string }
        city: { type: string, enum: [mecca, medina] }
        currentTemp: { type: integer }
        feelsLike: { type: integer }
        humidity: { type: integer }
        condition: { type: string }
        icon: { type: string }
        forecast3Day:
          type: array
          items:
            type: object
            properties:
              date: { type: string, format: date }
              tempMax: { type: integer }
              tempMin: { type: integer }
              condition: { type: string }

    RitualProgress:
      type: object
      properties:
        id: { type: string }
        userId: { type: string }
        ritualType: { type: string, enum: [tawaf, sai] }
        isGpsGuided: { type: boolean }
        currentRound: { type: integer }
        totalRounds: { type: integer }
        gpsAccuracy: { type: number }
        batteryAtStart: { type: integer }
        startTime: { type: string, format: date-time }
        completedAt: { type: string, format: date-time }
        isCompleted: { type: boolean }
```

## Components

Based on the architectural patterns, tech stack, and data models, I've identified the major logical components/services across the fullstack:

### Home Tab Dashboard Component

**Responsibility:** Unified dashboard with three distinct sections and 2x2 feature grid

**Key Interfaces:**
- **Top Section**: News headlines (Hajj/Umrah) with external browser links + Weather forecast (Mecca & Medina)
- **Middle Section**: Arrow pointing to selected saved location from Quick Map
- **Bottom Section**: 2x2 tiled icons (Quick Map, Family Finder, MyItineraries, Crowd Insights)

**Dependencies:** News scraping service, OpenWeatherMap API, saved locations, all four feature services

**Technology Stack:** Web scraping (html package), OpenWeatherMap API, Firebase for saved locations, feature grid navigation

### Quick Map Component (Free Feature)

**Responsibility:** Location management with custom saved locations and arrow display on Home tab

**Key Interfaces:**
- Add/edit custom locations (Hotel, Gate, Meeting Points)
- Location selection for Home tab arrow display
- GPS coordinate saving and naming
- Integration with Google Maps for location picking

**Dependencies:** Google Maps API, device location services, local storage

**Technology Stack:** Google Maps API, geolocator, SQLite for saved locations, Material Design location picker

### Family Finder Component (Freemium - Basic vs Full)

**Responsibility:** Multi-technology family tracking with QR group creation and tiered feature access

**Key Interfaces:**
- **FREE TIER**: Basic tracking (Bluetooth LE + WiFi Direct only)
- **PREMIUM TIER**: Full features (adds Google Maps API + preset messaging + last known location)
- QR code group creation with preset expiry dates
- Family member map display (technology varies by tier)
- Directional guidance to family members (premium only)

**Dependencies:** QR code generation, Bluetooth LE, WiFi Direct, Google Maps API (premium), Firebase Realtime Database

**Technology Stack:** qr_flutter, flutter_blue_plus, wifi_direct_flutter, Google Maps API (premium), Firebase Realtime Database

### Prayer Times Component (Free Feature)

**Responsibility:** Complete prayer time functionality with Al Adhan API integration

**Key Interfaces:**
- Direct API calls to Al Adhan Prayer Times API (https://aladhan.com/prayer-times-api)
- Direct API calls to Al Adhan Islamic Calendar API (https://aladhan.com/islamic-calendar-api)
- Local daily prayer times display with azan notifications
- Gregorian to Hijri date conversion

**Dependencies:** Al Adhan APIs (direct Flutter app calls), device notification system

**Technology Stack:** Al Adhan APIs (direct integration), local_notifications, HTTP client (dio)

### Knowledge Hub Component (Mixed Freemium)

**Responsibility:** Ibadah guides and ritual assistance tools with freemium differentiation

**Key Interfaces:**
- **FREE**: Ibadah Guide for Hajj with tracker (full access)
- **FREE**: Ibadah Guide for Umrah with tracker (full access)
- **FREE**: MyIbadah manual counters for Tawaf and Sa'i (full access)
- **PREMIUM**: GPS-guided Tawaf counter with Google Maps API
- **PREMIUM**: GPS-guided Sa'i counter with Google Maps API

**Dependencies:** Google Maps API (premium features), device GPS, premium subscription validation

**Technology Stack:** Google Maps API (premium), geolocator (premium), manual counter widgets (free), premium feature gating

### Friday Sermon Component (Premium Only)

**Responsibility:** Complete sermon delivery system with sophisticated YouTube integration

**Key Interfaces:**
- Live Friday Sermon and Last Friday Sermon selection
- Location choice (Mecca/Medina) and language selection (6 languages)
- Sophisticated search algorithm implementation with exact matching
- Unified audio-caption streaming using youtube_explode_dart + just_audio
- Premium subscription gating (completely unavailable for free users)

**Dependencies:** YouTube Data API v3, @tubesermon channel, Al Adhan API for Hijri conversion, premium validation

**Technology Stack:** YouTube Data API, youtube_explode_dart, just_audio, sophisticated search algorithm, Firebase Cloud Functions, premium gating

### Historical Places Component (Mixed Freemium)

**Responsibility:** Islamic historical sites database with tiered feature access

**Key Interfaces:**
- **FREE**: Basic information (name, historical period, category, description, GPS locations, access information & disclaimer)
- **PREMIUM**: Advanced features (Hadith/Quran references + "Add to MyItinerary" button integration)
- "Get directions" button (available to all users)
- List filtering and search functionality

**Dependencies:** Historical places database, premium subscription validation, Google Maps API for directions

**Technology Stack:** Firebase Firestore for places database, Google Maps API for directions, premium feature gating for advanced content

## Offline Data Strategy & API Resilience

### Critical Offline Data Requirements

Based on the external API failure strategies defined above, the following offline data must be bundled with the application:

#### Embedded Static Data (App Bundle)
```yaml
holy_site_coordinates:
  kaaba_center: {lat: 21.422487, lng: 39.826206}
  safa_point: {lat: 21.423056, lng: 39.827778}
  marwah_point: {lat: 21.421944, lng: 39.828333}
  masjid_nabawi: {lat: 24.467775, lng: 39.611146}
  major_gates:
    - {name: "King Abdul Aziz Gate", lat: 21.421389, lng: 39.826944}
    - {name: "Umrah Gate", lat: 21.423611, lng: 39.827500}
    - {name: "King Fahd Gate", lat: 21.424722, lng: 39.825556}

prayer_calculation_constants:
  mecca_coordinates: {lat: 21.4225, lng: 39.8262}
  medina_coordinates: {lat: 24.4677, lng: 39.6111}
  calculation_methods:
    - umm_al_qura: {fajr_angle: 18.5, isha_angle: 90, isha_interval: 120}
    - muslim_world_league: {fajr_angle: 18, isha_angle: 17}
    - egyptian: {fajr_angle: 19.5, isha_angle: 17.5}

hijri_calculation_data:
  epoch_date: "622-07-16"  # Gregorian date of Hijri epoch
  leap_year_cycle: [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29]
  month_lengths: [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29]

weather_fallback_data:
  mecca_seasonal_averages:
    winter: {temp_avg: 25, humidity: 45, advice: "Mild weather, light layers recommended"}
    spring: {temp_avg: 32, humidity: 35, advice: "Warm days, stay hydrated"}
    summer: {temp_avg: 42, humidity: 30, advice: "Extreme heat, seek shade frequently"}
    autumn: {temp_avg: 35, humidity: 40, advice: "Hot weather continuing, protect from sun"}
  medina_seasonal_averages:
    winter: {temp_avg: 20, humidity: 50, advice: "Cool weather, warmer clothes needed"}
    spring: {temp_avg: 28, humidity: 40, advice: "Pleasant weather for walking"}
    summer: {temp_avg: 38, humidity: 35, advice: "Very hot, early morning visits recommended"}
    autumn: {temp_avg: 30, humidity: 45, advice: "Comfortable temperatures returning"}

backup_islamic_content:
  essential_duas:
    - {name: "Tawaf Dua", arabic: "...", transliteration: "...", translation: "..."}
    - {name: "Sa'i Dua", arabic: "...", transliteration: "...", translation: "..."}
  basic_historical_places:
    - {name: "Cave of Hira", period: "Prophet", category: "Cave", basic_description: "..."}
    - {name: "Masjid Quba", period: "Prophet", category: "Mosque", basic_description: "..."}
```

#### SQLite Offline Cache Schema
```sql
-- Prayer times cache (24-48 hour window)
CREATE TABLE prayer_times_cache (
    date TEXT PRIMARY KEY,
    location_lat REAL,
    location_lng REAL,
    fajr_time TEXT,
    sunrise_time TEXT,
    dhuhr_time TEXT,
    asr_time TEXT,
    maghrib_time TEXT,
    isha_time TEXT,
    calculation_method TEXT,
    cached_at TIMESTAMP,
    expires_at TIMESTAMP
);

-- Hijri date cache (30 day window)
CREATE TABLE hijri_cache (
    gregorian_date TEXT PRIMARY KEY,
    hijri_year INTEGER,
    hijri_month INTEGER,
    hijri_day INTEGER,
    month_name TEXT,
    cached_at TIMESTAMP
);

-- Sermon content cache (premium feature)
CREATE TABLE sermon_cache (
    video_id TEXT PRIMARY KEY,
    title TEXT,
    mosque TEXT,
    language TEXT,
    hijri_date TEXT,
    audio_file_path TEXT,
    captions_text TEXT,
    duration INTEGER,
    download_completed BOOLEAN,
    cached_at TIMESTAMP,
    expires_at TIMESTAMP
);

-- Weather data cache (6 hour refresh)
CREATE TABLE weather_cache (
    city TEXT PRIMARY KEY,
    current_temp INTEGER,
    feels_like INTEGER,
    humidity INTEGER,
    condition TEXT,
    forecast_json TEXT,
    cached_at TIMESTAMP,
    expires_at TIMESTAMP
);

-- Family location cache (for offline family finder)
CREATE TABLE family_location_cache (
    group_id TEXT,
    user_id TEXT,
    lat REAL,
    lng REAL,
    accuracy REAL,
    battery_level INTEGER,
    timestamp TIMESTAMP,
    PRIMARY KEY (group_id, user_id)
);
```

#### API Failure Detection & Recovery Logic
```yaml
failure_detection:
  timeout_thresholds:
    prayer_times_api: 10_seconds
    hijri_conversion_api: 5_seconds
    youtube_api: 15_seconds
    google_maps_api: 8_seconds
    weather_api: 12_seconds

  retry_strategies:
    exponential_backoff: [1s, 2s, 4s, 8s, 16s]
    max_retries: 3
    circuit_breaker: "Open circuit after 5 consecutive failures"
    recovery_test_interval: 60_seconds

graceful_degradation_ui:
  prayer_times_offline:
    message: "Using calculated prayer times for your location"
    indicator: "📱 Offline Mode"
    accuracy_note: "Times calculated locally, may vary by ±2 minutes"

  sermon_unavailable:
    message: "Sermon streaming unavailable - browse downloaded content"
    alternative: "Access Quran recitations and saved sermons"
    upgrade_path: "Premium subscribers: Download sermons for offline access"

  maps_offline:
    message: "GPS guidance limited - manual counting available"
    fallback: "Use compass and manual counter for spiritual focus"
    accuracy_note: "Sacred coordinates available for direction assistance"

  weather_fallback:
    message: "Current weather unavailable - showing seasonal guidance"
    content: "Historical averages and pilgrimage weather advice"
    refresh_option: "Try refreshing when connection improves"
```

### Implementation Priority for Offline Features
1. **Phase 1**: Prayer time offline calculations (critical spiritual functionality)
2. **Phase 2**: Static holy site coordinates and manual counters
3. **Phase 3**: Family location caching and Bluetooth fallbacks
4. **Phase 4**: Sermon downloading and offline content library
5. **Phase 5**: Weather fallback data and seasonal guidance

## Unified Project Structure

Based on the chosen monorepo approach with Flutter and Firebase, here's the complete project structure:

```
ziarah/
├── .github/                    # CI/CD workflows
│   └── workflows/
│       ├── ci.yaml            # Automated testing and linting
│       ├── deploy-functions.yaml # Firebase Functions deployment
│       └── deploy-android.yaml   # Play Store deployment
├── apps/                       # Application packages
│   ├── mobile/                 # Flutter mobile application
│   │   ├── android/           # Android-specific configuration
│   │   ├── lib/               # Flutter source code
│   │   │   ├── features/      # Feature-based organization
│   │   │   │   ├── home/      # Home tab with 2x2 grid
│   │   │   │   ├── prayer_times/ # Prayer Times (FREE - Al Adhan APIs)
│   │   │   │   ├── knowledge_hub/ # Mixed freemium (GPS premium)
│   │   │   │   ├── friday_sermon/ # PREMIUM ONLY (YouTube integration)
│   │   │   │   ├── historical_places/ # Mixed freemium (Hadith premium)
│   │   │   │   ├── family_finder/ # Mixed freemium (GPS premium)
│   │   │   │   ├── quick_map/ # FREE
│   │   │   │   ├── my_itineraries/ # PREMIUM ONLY
│   │   │   │   └── crowd_insights/ # FREE (Kaggle dataset)
│   │   │   ├── shared/        # Shared UI components
│   │   │   └── core/          # Core utilities and Islamic helpers
│   │   ├── assets/            # Islamic content and Kaggle dataset
│   │   └── pubspec.yaml       # Flutter dependencies
│   └── cloud_functions/       # Firebase Cloud Functions (Dart)
│       ├── lib/
│       │   ├── sermon_processor/ # YouTube API + search algorithm
│       │   ├── premium_validator/ # Subscription validation
│       │   └── family_safety/ # Real-time location processing
│       └── pubspec.yaml
├── packages/                   # Shared packages
│   ├── shared_models/         # Data models (Dart/TypeScript)
│   ├── islamic_utils/         # Islamic calculations and utilities
│   └── premium_core/          # Premium feature management
├── infrastructure/            # Firebase configuration
├── scripts/                   # Build and deployment scripts
├── docs/                      # Documentation
├── melos.yaml                 # Monorepo configuration
└── README.md
```

## Coding Standards

### Critical Fullstack Rules

- **Islamic Data Sensitivity:** Never track location during prayer times - automatically pause family location sharing during detected prayer periods
- **Freemium Enforcement:** Always validate premium subscription server-side - never trust client-side premium status
- **Family Safety Priority:** Emergency messages bypass rate limiting with <1s latency requirement
- **YouTube API Compliance:** Sermon search must find exactly 1 matching video or throw SermonNotFoundException
- **Prayer Time Accuracy:** Use Al Adhan API directly from Flutter with 24-hour caching
- **Offline-First Data:** Critical features work without internet - SQLite mirrors Firebase schema
- **Multi-Technology Fallback:** Family tracking gracefully degrades GPS → WiFi → Bluetooth
- **Audio-Caption Sync:** Extract audio and captions in single operation with 30s buffer
- **Battery Optimization:** Adaptive GPS polling based on user state and prayer times

## Performance Requirements & Testing Methodology

### 12+ Hour Battery Life Validation

**Critical Requirement from PRD**: App must support extended pilgrimage days (12+ hours) without charging

#### Battery Testing Protocol

##### Test Environment Setup
```yaml
test_devices:
  minimum_spec:
    - Samsung Galaxy A54 (5000mAh battery)
    - Google Pixel 6a (4410mAh battery)
    - OnePlus Nord CE 3 (5000mAh battery)

  standard_spec:
    - Samsung Galaxy S23 (3900mAh battery)
    - Google Pixel 7 (4355mAh battery)
    - OnePlus 11 (5000mAh battery)

test_conditions:
  brightness: 75% (outdoor visibility)
  wifi: Enabled but intermittent connection
  mobile_data: 4G/LTE active
  bluetooth: Always on (family tracking)
  gps: High accuracy mode
  background_apps: Standard pilgrimage environment
  ambient_temperature: 35°C (hot climate simulation)

pilgrimage_usage_simulation:
  duration: 14_hours_continuous
  location_changes: 25_per_hour (walking/movement simulation)
  feature_usage_patterns:
    prayer_times: Every_10_minutes (checking next prayer)
    gps_counter: 2_sessions_45_minutes_each (Tawaf/Sai)
    family_finder: Always_active_background
    sermon_streaming: 1_hour_total (Friday sermon)
    historical_places: 15_minutes_per_hour
    crowd_insights: 5_minutes_per_hour
```

##### Performance Benchmarks
```yaml
battery_consumption_targets:
  critical_features_only: ≤60%_battery_in_12_hours
  full_feature_usage: ≤70%_battery_in_12_hours
  aggressive_usage: ≤85%_battery_in_12_hours

acceptable_degradation:
  hour_8: ≤50%_battery_consumed
  hour_10: ≤65%_battery_consumed
  hour_12: ≤80%_battery_consumed
  emergency_reserve: ≥10%_battery_remaining

feature_specific_limits:
  gps_tracking: ≤15%_battery_per_hour_continuous
  background_family_finder: ≤3%_battery_per_hour
  sermon_streaming: ≤8%_battery_per_hour
  offline_mode: ≤5%_battery_per_hour_all_features
```

##### Battery Optimization Strategies
```yaml
adaptive_gps_polling:
  stationary_user:
    frequency: Every_30_seconds
    accuracy: BALANCED_POWER_ACCURACY
    justification: "User not moving, low frequency sufficient"

  walking_user:
    frequency: Every_10_seconds
    accuracy: HIGH_ACCURACY
    justification: "Movement detection for ritual progress"

  ritual_active:
    frequency: Every_3_seconds
    accuracy: HIGH_ACCURACY
    duration_limit: 45_minutes_maximum
    justification: "Critical GPS counting phase"

  prayer_time_pause:
    behavior: GPS_COMPLETELY_DISABLED
    duration: Prayer_duration_plus_5_minutes
    justification: "Respect spiritual focus, save battery"

background_task_optimization:
  family_location_sync:
    frequency: Every_15_seconds_when_active
    fallback: Every_60_seconds_when_battery_low
    suspend: During_prayer_times

  firebase_sync:
    frequency: Every_2_minutes_normal
    frequency_low_battery: Every_10_minutes
    batch_operations: Queue_and_sync_together

  sermon_buffering:
    preload: 2_minutes_ahead_normal
    preload_low_battery: 30_seconds_ahead
    cache_strategy: Aggressive_local_storage

screen_power_management:
  brightness_auto_adjust: Reduce_10_percent_after_8_hours
  screen_timeout: 30_seconds_default
  dark_mode_trigger: Enable_automatically_after_6_hours
  always_on_display: Disable_for_battery_conservation
```

##### Testing Automation Scripts
```yaml
automated_testing:
  battery_drain_test:
    framework: Flutter_integration_test_with_battery_plugin
    duration: 14_hours_simulation
    logging_interval: Every_1_minute
    metrics_tracked:
      - battery_percentage_remaining
      - cpu_usage_percentage
      - memory_usage_mb
      - gps_active_time
      - network_requests_count
      - screen_on_time

  feature_isolation_tests:
    gps_only: Test_GPS_tracking_battery_impact_isolation
    family_finder_only: Test_background_family_sync_impact
    sermon_streaming_only: Test_audio_playback_battery_drain
    offline_mode_only: Test_battery_life_with_cached_data

  regression_testing:
    trigger: Every_code_change_affecting_background_services
    baseline: Previous_version_battery_performance
    acceptance_criteria: ≤5_percent_performance_degradation
```

##### Performance Monitoring in Production
```yaml
real_world_monitoring:
  firebase_analytics_tracking:
    battery_level_snapshots: Log_every_hour_during_active_usage
    feature_usage_correlation: Track_battery_drain_per_feature
    crash_on_low_battery: Monitor_app_stability_below_15_percent

  user_feedback_collection:
    battery_life_survey: In_app_prompt_after_8_hour_session
    performance_rating: Rate_app_performance_after_pilgrimage
    feature_usage_optimization: Suggest_battery_saving_features

  automated_alerts:
    battery_drain_anomaly: Alert_if_drain_exceeds_targets_by_20_percent
    performance_regression: Alert_if_average_battery_life_drops_below_10_hours
    critical_feature_impact: Alert_if_GPS_or_family_features_exceed_limits
```

#### Development Phase Testing Schedule
```yaml
testing_milestones:
  phase_1_foundation:
    focus: Prayer_times_and_basic_navigation
    battery_target: 18_hours_basic_features
    testing_duration: 2_days_continuous

  phase_2_gps_features:
    focus: GPS_ritual_counters_and_family_tracking
    battery_target: 14_hours_with_GPS_active
    testing_duration: 3_days_realistic_usage

  phase_3_content_streaming:
    focus: Friday_sermons_and_historical_content
    battery_target: 12_hours_with_streaming
    testing_duration: 4_days_comprehensive_testing

  phase_4_full_integration:
    focus: All_features_simultaneous_usage
    battery_target: 12_hours_full_feature_set
    testing_duration: 1_week_real_world_simulation

  pre_launch_validation:
    focus: Production_ready_battery_optimization
    battery_target: 14_hours_with_margin_for_error
    testing_duration: 2_weeks_diverse_device_testing
```

## Executive Summary

### Architecture Completeness: 100% READY FOR DEVELOPMENT

**All PRD Requirements Addressed:**
- ✅ Home Tab (2x2 Grid): News, Weather, Quick Map, Family Finder, MyItineraries, Crowd Insights
- ✅ Prayer Times (FREE): Al Adhan direct integration, Hijri conversion, notifications
- ✅ Knowledge Hub (Mixed): Free guides/manual counters, Premium GPS assistance
- ✅ Friday Sermon (Premium): YouTube integration, 6-language support, sophisticated search
- ✅ Historical Places (Mixed): Free basic info, Premium Hadith/Quran + itineraries
- ✅ Correct freemium tier enforcement across all features with server-side validation
- ✅ Multi-technology family tracking (Bluetooth/WiFi/GPS) with graceful degradation
- ✅ Sophisticated YouTube sermon search algorithm with exact matching requirements
- ✅ Islamic cultural sensitivity with prayer time location suspension
- ✅ Battery optimization for 12+ hour pilgrimage usage
- ✅ Offline-first architecture with SQLite + Firebase synchronization

**Technical Foundation:**
- **Frontend:** Flutter 3.13+ with feature-based architecture and Islamic-appropriate theming
- **Backend:** Firebase Cloud Functions with Dart for unified development experience
- **Database:** Firebase Firestore + SQLite offline-first with automatic synchronization
- **External APIs:** Al Adhan (direct), YouTube Data API, Google Maps, Kaggle dataset
- **Deployment:** CI/CD with Google Play Store and Firebase automatic deployment

**Implementation Ready:** Complete technical specification provides definitive foundation for developing Ziarah as the premier Islamic pilgrimage companion application, serving the global Muslim community with authentic, respectful, and technologically advanced spiritual assistance.