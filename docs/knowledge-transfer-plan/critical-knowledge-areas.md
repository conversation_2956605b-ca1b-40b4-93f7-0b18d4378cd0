# Critical Knowledge Areas

## 1. Islamic Cultural & Technical Context

### Islamic Pilgrimage Domain Knowledge
```yaml
pilgrimage_fundamentals:
  hajj_requirements:
    - Five pillars of Islam context
    - Ritual sequence: Ihram → Tawaf → Sa'i → Arafat → Muzdalifah → Mina
    - Sacred geography: Kaaba, Safa/Marwah hills, holy sites
    - Timing importance: Hijri calendar, prayer times, ritual windows

  umrah_requirements:
    - Simplified pilgrimage structure
    - Core rituals: Ihram → Tawaf → Sa'i
    - Flexibility in timing and sequence
    - Year-round availability vs. Hajj seasonal requirement

  cultural_sensitivities:
    - Prayer time location tracking suspension (privacy)
    - Visual design restrictions (no human/animal imagery)
    - Language considerations (Arabic terms, transliteration)
    - Gender-separated family tracking considerations
```

### Technical Integration with Islamic Practices
```yaml
islamic_technical_requirements:
  gps_accuracy_criticality:
    - Kaaba center coordinates: 21.422487, 39.826206 (±2 meters)
    - Ritual completion detection: 7 circuits Tawaf, 7 rounds Sa'i
    - Sacred space boundaries: Haram area detection
    - Prayer direction (Qibla) calculation accuracy

  multilingual_complexity:
    - Arabic script support (RTL text rendering)
    - Islamic calendar system integration
    - Prayer calculation methods by region
    - Hadith/Quran reference formatting standards

  offline_spiritual_continuity:
    - Core spiritual functions must work without internet
    - Emergency family safety features priority
    - Respectful error messaging during failures
    - Alternative spiritual content when streaming fails
```

## 2. Architecture Decision Context

### Technology Choice Justifications
```yaml
flutter_firebase_rationale:
  flutter_advantages:
    - Single codebase for Android (initial) + iOS (future)
    - Superior GPS accuracy and location services
    - Excellent offline-first architecture support
    - Islamic UI component customization flexibility

  firebase_ecosystem_benefits:
    - Real-time family location tracking (Firebase Realtime Database)
    - Scalable serverless architecture (Cloud Functions)
    - Integrated authentication with premium subscription
    - Offline-first data synchronization built-in

  external_api_strategy:
    - Al Adhan API: Direct calls for prayer time accuracy
    - YouTube Data API: Sophisticated sermon search algorithm
    - Google Maps API: High-precision GPS for ritual assistance
    - Cached fallbacks for all external dependencies
```

### Freemium Business Model Implementation
```yaml
premium_feature_gating:
  server_side_validation:
    - Never trust client-side premium status
    - Firebase Auth integration with subscription validation
    - Premium feature enforcement at API level
    - Graceful degradation for expired subscriptions

  feature_tier_breakdown:
    free_tier:
      - Prayer times and notifications
      - Manual ritual counters (Tawaf/Sa'i)
      - Basic family tracking (Bluetooth/WiFi only)
      - Basic historical place information

    premium_tier:
      - GPS-guided automatic ritual counting
      - Google Maps integration for family safety
      - Friday sermon streaming with captions
      - Advanced historical places with Hadith/Quran references
      - MyItineraries with crowd optimization
```

## 3. Development Team Onboarding

### Required Pre-Development Setup
```yaml
development_environment:
  flutter_setup:
    - Flutter 3.13+ with Dart 3.1+
    - Android Studio with Flutter plugin
    - Firebase CLI and FlutterFire CLI
    - Device testing with GPS-enabled Android phones

  islamic_development_context:
    - Install Islamic calendar libraries for testing
    - Set up prayer time calculation validation tools
    - Configure multi-language testing environments
    - Understand halal design principle compliance

  external_service_accounts:
    required_apis:
      - Firebase project with Firestore, Auth, Functions, Storage
      - Google Maps API key with high-precision GPS access
      - YouTube Data API v3 key with quota management
      - Al Adhan API (no key required, but rate limiting)
      - OpenWeatherMap API key for weather data

    testing_accounts:
      - Multiple test Firebase Auth accounts
      - Premium subscription test setup
      - Family group testing with multiple devices
      - Islamic content validation accounts
```
