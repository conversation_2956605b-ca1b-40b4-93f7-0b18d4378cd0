# Story 1.1: Project Setup & Basic App Structure

## Status
Approved

## Story
**As a developer,**
**I want to establish the foundational Flutter project with proper architecture,**
**so that the team has a scalable codebase for building all Ziarah features.**

## Acceptance Criteria
1. Flutter 3.x project created with Android target (API Level 21+)
2. Material Design 3 theme configured with Islamic-appropriate color palette
3. Bottom navigation bar implemented with 5 tabs (Home, Prayer Times, Knowledge Hub, Friday Sermon, Historical Places)
4. Basic routing structure established for all main screens
5. Firebase project configured with Android app registration
6. SQLite database schema designed for offline data storage
7. Repository pattern implemented for data access abstraction

## Tasks / Subtasks
- [ ] **Flutter Project Initialization** (AC: 1)
  - [ ] Create new Flutter project with version 3.13+
  - [ ] Configure Android minimum SDK to API Level 21
  - [ ] Set up proper project structure following unified-project-structure.md
  - [ ] Initialize git repository with proper .gitignore for Flutter/Firebase

- [ ] **Material Design 3 Theme Setup** (AC: 2)
  - [ ] Implement Islamic-appropriate color palette (deep blues, golds, whites)
  - [ ] Configure Material Design 3 theme with Islamic color scheme
  - [ ] Set up typography supporting multiple scripts (Arabic, Latin, Southeast Asian)
  - [ ] Implement high contrast options for WCAG AA compliance

- [ ] **Bottom Navigation Implementation** (AC: 3)
  - [ ] Create 5-tab bottom navigation structure
  - [ ] Implement tab icons and labels (Home, Prayer Times, Knowledge Hub, Friday Sermon, Historical Places)
  - [ ] Configure navigation state management using flutter_bloc
  - [ ] Ensure accessibility compliance with proper semantic labels

- [ ] **Screen Routing Architecture** (AC: 4)
  - [ ] Set up go_router for navigation between main screens
  - [ ] Create placeholder screens for all 5 main tabs
  - [ ] Implement deep linking structure for future features
  - [ ] Configure route guards for premium feature access

- [ ] **Firebase Project Configuration** (AC: 5)
  - [ ] Create Firebase project and register Android app
  - [ ] Configure Firebase Authentication with Google Sign-In
  - [ ] Set up Firebase Firestore with security rules
  - [ ] Initialize Firebase Storage for content caching
  - [ ] Configure Firebase Analytics and Crashlytics

- [ ] **SQLite Database Schema** (AC: 6)
  - [ ] Design SQLite schema based on data-models.md specifications
  - [ ] Create database migration scripts for future updates
  - [ ] Implement User, SavedLocation, and basic cache tables
  - [ ] Set up database initialization and version management

- [ ] **Repository Pattern Implementation** (AC: 7)
  - [ ] Create abstract repository interfaces for all data sources
  - [ ] Implement concrete repositories for Firebase and SQLite
  - [ ] Set up dependency injection using get_it package
  - [ ] Create data layer abstraction following coding-standards.md

## Dev Notes

### Source Tree Structure
Based on unified-project-structure.md, implement the following structure:
```
apps/mobile/lib/
├── features/
│   ├── home/
│   ├── prayer_times/
│   ├── knowledge_hub/
│   ├── friday_sermon/
│   └── historical_places/
├── shared/
└── core/
```
[Source: architecture/unified-project-structure.md]

### Technology Stack Requirements
- **Frontend Language:** Dart 3.1+ for Flutter development
- **Frontend Framework:** Flutter 3.13+ for cross-platform mobile framework
- **UI Components:** Material Design 3 built-in for Islamic-appropriate UI components
- **State Management:** flutter_bloc 8.1+ for predictable state management
- **Backend:** Firebase suite (Auth, Firestore, Storage, Analytics, Crashlytics)
- **Local Database:** SQLite for offline-first data persistence
[Source: architecture/tech-stack.md]

### Data Models Implementation
Implement initial data models from data-models.md:
- **User**: Central authentication and preferences entity
- **SavedLocation**: User-defined locations for Quick Map
- **Basic cache tables**: For prayer times and offline functionality
[Source: architecture/data-models.md]

### Critical Coding Standards
- **Offline-First Data:** Critical features work without internet - SQLite mirrors Firebase schema
- **Freemium Enforcement:** Always validate premium subscription server-side
- **Islamic Data Sensitivity:** Never track location during prayer times
[Source: architecture/coding-standards.md]

### Testing Standards
- **Unit Testing:** Use flutter_test for all core functionality
- **Integration Testing:** Use integration_test for navigation and data flow
- **Test Location:** Place tests in test/ directory following Flutter conventions
- **Critical Path Testing:** Focus on authentication flow and data persistence
[Source: architecture/tech-stack.md]

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-24 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

### Agent Model Used
*To be filled by Dev Agent*

### Debug Log References
*To be filled by Dev Agent*

### Completion Notes List
*To be filled by Dev Agent*

### File List
*To be filled by Dev Agent*

## QA Results
*Results from QA Agent review will be added here after implementation*