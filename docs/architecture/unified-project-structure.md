# Unified Project Structure

Based on the chosen monorepo approach with Flutter and Firebase, here's the complete project structure:

```
ziarah/
├── .github/                    # CI/CD workflows
│   └── workflows/
│       ├── ci.yaml            # Automated testing and linting
│       ├── deploy-functions.yaml # Firebase Functions deployment
│       └── deploy-android.yaml   # Play Store deployment
├── apps/                       # Application packages
│   ├── mobile/                 # Flutter mobile application
│   │   ├── android/           # Android-specific configuration
│   │   ├── lib/               # Flutter source code
│   │   │   ├── features/      # Feature-based organization
│   │   │   │   ├── home/      # Home tab with 2x2 grid
│   │   │   │   ├── prayer_times/ # Prayer Times (FREE - Al Adhan APIs)
│   │   │   │   ├── knowledge_hub/ # Mixed freemium (GPS premium)
│   │   │   │   ├── friday_sermon/ # PREMIUM ONLY (YouTube integration)
│   │   │   │   ├── historical_places/ # Mixed freemium (Hadith premium)
│   │   │   │   ├── family_finder/ # Mixed freemium (GPS premium)
│   │   │   │   ├── quick_map/ # FREE
│   │   │   │   ├── my_itineraries/ # PREMIUM ONLY
│   │   │   │   └── crowd_insights/ # FREE (Kaggle dataset)
│   │   │   ├── shared/        # Shared UI components
│   │   │   └── core/          # Core utilities and Islamic helpers
│   │   ├── assets/            # Islamic content and Kaggle dataset
│   │   └── pubspec.yaml       # Flutter dependencies
│   └── cloud_functions/       # Firebase Cloud Functions (Dart)
│       ├── lib/
│       │   ├── sermon_processor/ # YouTube API + search algorithm
│       │   ├── premium_validator/ # Subscription validation
│       │   └── family_safety/ # Real-time location processing
│       └── pubspec.yaml
├── packages/                   # Shared packages
│   ├── shared_models/         # Data models (Dart/TypeScript)
│   ├── islamic_utils/         # Islamic calculations and utilities
│   └── premium_core/          # Premium feature management
├── infrastructure/            # Firebase configuration
├── scripts/                   # Build and deployment scripts
├── docs/                      # Documentation
├── melos.yaml                 # Monorepo configuration
└── README.md
```
