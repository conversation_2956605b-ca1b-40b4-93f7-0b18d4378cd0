# Tech Stack

This is the **DEFINITIVE technology selection** for the entire Ziarah project. This table serves as the single source of truth - all development must use these exact versions.

## Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| Frontend Language | Dart | 3.1+ | Flutter mobile app development | Type-safe, compiled language with excellent async support for real-time location updates |
| Frontend Framework | Flutter | 3.13+ | Cross-platform mobile framework | Superior GPS/location performance, offline-first capabilities, Google services integration |
| UI Component Library | Material Design 3 | Built-in | Islamic-appropriate UI components | Native Flutter theming with Islamic color customization, accessibility compliance |
| State Management | flutter_bloc | 8.1+ | Predictable state management | Handles complex family tracking states, GPS counter logic, and real-time data flows |
| Backend Language | Dart | 3.1+ | Firebase Cloud Functions | Unified language across stack, reduces context switching for solo/small team development |
| Backend Framework | Firebase Functions | Latest | Serverless API endpoints | Auto-scaling, pay-per-use pricing ideal for freemium model, real-time database triggers |
| API Style | REST + Real-time | HTTP/WebSocket | RESTful APIs with real-time subscriptions | REST for data operations, WebSocket for family location updates and emergency alerts |
| Database | Firebase Firestore + SQLite | Latest | Primary cloud + local cache | Firestore for real-time sync, SQLite for offline-first prayer times and user data |
| Cache | Firebase Firestore Cache + SQLite | Built-in | Multi-layer caching strategy | 24-hour prayer time cache, historical places offline data, sermon content buffering |
| File Storage | Firebase Storage | Latest | Images, audio content, user uploads | Scalable storage for historical place photos, sermon audio cache, user profile images |
| Authentication | Firebase Auth | Latest | User accounts and premium verification | Google Sign-In integration, secure premium subscription validation, privacy compliance |
| Frontend Testing | flutter_test + integration_test | Built-in | Unit and integration testing | Critical path testing for GPS accuracy, family safety features, offline functionality |
| Backend Testing | Firebase Test Lab + functions-framework | Latest | Cloud function testing and device testing | Automated testing across Android devices, prayer time calculation validation |
| E2E Testing | patrol | 2.0+ | End-to-end user journey testing | Real device testing for GPS ritual assistance, family finder scenarios, subscription flows |
| Build Tool | Flutter Build | Built-in | Mobile app compilation and packaging | Optimized builds for Android release, future iOS support, automated signing |
| Bundler | Flutter Build Runner | Built-in | Code generation and asset bundling | JSON serialization, route generation, Islamic calendar data compilation |
| IaC Tool | Firebase CLI + GitHub Actions | Latest | Infrastructure as code deployment | Automated Firebase deployment, environment management, CI/CD pipeline integration |
| CI/CD | GitHub Actions | Latest | Automated testing and deployment | Parallel testing, automated Play Store deployment, Firebase function deployment |
| Monitoring | Firebase Crashlytics + Analytics | Latest | Crash reporting and usage analytics | Privacy-compliant user behavior tracking, performance monitoring, error reporting |
| Logging | Firebase Performance + flutter_logs | Latest | Performance monitoring and debugging | Battery usage tracking, GPS accuracy monitoring, API response time analysis |
| CSS Framework | Flutter Theming | Built-in | Islamic-appropriate visual design | Material Design 3 with custom Islamic color palette, typography, and spacing |
| Audio Processing | youtube_explode_dart | 1.7+ | YouTube sermon extraction | Extracts audio streams and captions from @tubesermon channel |
| Audio Playback | just_audio | 0.9+ | Unified audio-caption playback | Background playback with synchronized caption display |
| QR Code | qr_flutter + qr_code_scanner | Latest | Family group QR creation/scanning | Generate group QR codes with expiry, scan to join groups |
| Bluetooth | flutter_blue_plus | 1.17+ | Close-range family tracking | BLE proximity detection for Family Finder backup |
| WiFi Direct | wifi_direct_flutter | Latest | Medium-range family communication | P2P connection when GPS/internet unavailable |
| Location Services | geolocator | 10.0+ | GPS positioning and distance | High-precision location for ritual counters and family tracking |
| HTTP Client | dio | 5.3+ | API calls and caching | Weather API, news scraping, Kaggle dataset access |
| Weather API | openweathermap_api | 0.1+ | Holy cities weather forecast | Mecca and Medina weather data for Home tab |
| Data Analysis | csv + http | Built-in | Kaggle crowd dataset processing | Parse CC0 crowd data from user ziya07 for insights |
| Web Scraping | html + http | Built-in | News headlines extraction | Hajj/Umrah news for Home tab external links |
