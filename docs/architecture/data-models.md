# Data Models

Based on the PRD requirements and Epic structure, I've identified the core data models that will be shared between frontend and backend:

## User

**Purpose:** Central user entity managing authentication, preferences, and subscription status

**Key Attributes:**
- id: String - Unique Firebase user identifier
- email: String - User authentication email
- displayName: String? - Optional display name for family identification
- isPremium: bool - Premium subscription status
- subscriptionExpiry: DateTime? - Premium subscription end date
- location: LatLng? - Current user location for prayer times
- prayerCalculationMethod: String - Islamic calculation method preference
- madhab: String - Islamic school (Hanafi/Shafi) for prayer calculations
- language: String - App interface language preference
- sermonLanguages: List<String> - Preferred sermon languages (premium)
- hijriMethod: String - Hijri calendar calculation method
- createdAt: DateTime - Account creation timestamp
- lastActive: DateTime - Last app usage for analytics

### TypeScript Interface
```typescript
interface User {
  id: string;
  email: string;
  displayName?: string;
  isPremium: boolean;
  subscriptionExpiry?: Date;
  location?: {
    latitude: number;
    longitude: number;
  };
  prayerCalculationMethod: string;
  madhab: 'hanafi' | 'shafi';
  language: string;
  sermonLanguages: string[];
  hijriMethod: string;
  createdAt: Date;
  lastActive: Date;
}
```

### Relationships
- One-to-many with FamilyGroup (as creator or member)
- One-to-many with SavedLocation
- One-to-many with Itinerary
- One-to-one with UserPreferences

## FamilyGroup

**Purpose:** Family safety coordination with QR-based group creation and multi-technology tracking

**Key Attributes:**
- id: String - Unique group identifier
- name: String - Family group name
- creatorId: String - User ID of group creator
- qrCode: String - Generated QR code for joining
- qrExpiry: DateTime - QR code expiration date
- isActive: bool - Group active status
- memberIds: List<String> - List of member user IDs
- emergencyContactPhone: String? - Emergency contact number
- hotelLocation: LatLng? - Shared hotel location reference
- createdAt: DateTime - Group creation timestamp
- lastActivity: DateTime - Last group activity for cleanup

### TypeScript Interface
```typescript
interface FamilyGroup {
  id: string;
  name: string;
  creatorId: string;
  qrCode: string;
  qrExpiry: Date;
  isActive: boolean;
  memberIds: string[];
  emergencyContactPhone?: string;
  hotelLocation?: {
    latitude: number;
    longitude: number;
  };
  createdAt: Date;
  lastActivity: Date;
}
```

### Relationships
- Many-to-many with User (group members)
- One-to-many with FamilyLocationUpdate
- One-to-many with FamilyMessage

## FamilyLocationUpdate

**Purpose:** Real-time location sharing for family safety with multi-technology support

**Key Attributes:**
- id: String - Update identifier
- groupId: String - Family group reference
- userId: String - Member providing location
- location: LatLng - Current GPS coordinates
- accuracy: double - Location accuracy in meters
- locationMethod: String - GPS/Bluetooth/WiFi source
- batteryLevel: int - Device battery percentage
- isEmergency: bool - Emergency status flag
- timestamp: DateTime - Location update time
- expiresAt: DateTime - Auto-cleanup timestamp

### TypeScript Interface
```typescript
interface FamilyLocationUpdate {
  id: string;
  groupId: string;
  userId: string;
  location: {
    latitude: number;
    longitude: number;
  };
  accuracy: number;
  locationMethod: 'gps' | 'bluetooth' | 'wifi_direct';
  batteryLevel: number;
  isEmergency: boolean;
  timestamp: Date;
  expiresAt: Date;
}
```

### Relationships
- Many-to-one with FamilyGroup
- Many-to-one with User

## FamilyMessage

**Purpose:** Preset messaging system for family communication during pilgrimage

**Key Attributes:**
- id: String - Message identifier
- groupId: String - Family group reference
- senderId: String - User sending message
- messageType: String - Preset message type
- customMessage: String? - Custom text if not preset
- senderLocation: LatLng? - Location when message sent
- timestamp: DateTime - Message send time
- isEmergency: bool - Emergency priority flag
- deliveredTo: List<String> - User IDs who received message
- readBy: List<String> - User IDs who read message

### TypeScript Interface
```typescript
interface FamilyMessage {
  id: string;
  groupId: string;
  senderId: string;
  messageType: 'come_to_me' | 'go_to_hotel' | 'im_safe' | 'need_help' | 'wait_for_me' | 'custom';
  customMessage?: string;
  senderLocation?: {
    latitude: number;
    longitude: number;
  };
  timestamp: Date;
  isEmergency: boolean;
  deliveredTo: string[];
  readBy: string[];
}
```

### Relationships
- Many-to-one with FamilyGroup
- Many-to-one with User (sender)

## HistoricalPlace

**Purpose:** Islamic historical sites database with Hadith/Quran references and navigation integration

**Key Attributes:**
- id: String - Unique place identifier
- name: String - Historical place name
- nameArabic: String? - Arabic name if applicable
- category: String - Place type (Mosque, Cave, Battlefield)
- historicalPeriod: String - Time period (Prophet SAW, Rashidun, etc)
- description: String - Detailed historical description
- location: LatLng - GPS coordinates
- city: String - Mecca, Medina, or Road Between
- accessInfo: String - Current access status and guidelines
- disclaimer: String - Safety and visiting disclaimers
- hadithReferences: List<String> - Authenticated Hadith citations
- quranReferences: List<String> - Relevant Quran verses
- imageUrls: List<String> - Historical photos
- isVerified: bool - Scholar verification status
- isPremiumContent: bool - Premium feature flag

### TypeScript Interface
```typescript
interface HistoricalPlace {
  id: string;
  name: string;
  nameArabic?: string;
  category: 'mosque' | 'cave' | 'battlefield' | 'mountain' | 'well' | 'house';
  historicalPeriod: 'prophet' | 'rashidun' | 'umayyad' | 'abbasid' | 'modern';
  description: string;
  location: {
    latitude: number;
    longitude: number;
  };
  city: 'mecca' | 'medina' | 'road_between';
  accessInfo: string;
  disclaimer: string;
  hadithReferences: string[];
  quranReferences: string[];
  imageUrls: string[];
  isVerified: boolean;
  isPremiumContent: boolean;
}
```

### Relationships
- Many-to-many with Itinerary
- One-to-many with SavedLocation

## Itinerary

**Purpose:** Custom pilgrimage planning with historical places and optimal timing

**Key Attributes:**
- id: String - Itinerary identifier
- userId: String - Owner user ID
- name: String - Itinerary name
- description: String - Trip description
- type: String - Hajj, Umrah, or Combined
- placeIds: List<String> - Ordered historical place IDs
- estimatedDuration: int - Total visit time in hours
- isShared: bool - Family sharing status
- crowdOptimized: bool - Optimized for crowd insights
- createdAt: DateTime - Creation timestamp
- lastModified: DateTime - Last update time

### TypeScript Interface
```typescript
interface Itinerary {
  id: string;
  userId: string;
  name: string;
  description: string;
  type: 'hajj' | 'umrah' | 'combined';
  placeIds: string[];
  estimatedDuration: number;
  isShared: boolean;
  crowdOptimized: boolean;
  createdAt: Date;
  lastModified: Date;
}
```

### Relationships
- Many-to-one with User
- Many-to-many with HistoricalPlace

## SavedLocation

**Purpose:** User-defined locations for Quick Map and navigation

**Key Attributes:**
- id: String - Location identifier
- userId: String - Owner user ID
- name: String - Custom location name
- category: String - Hotel, Gate, Meeting Point, etc
- location: LatLng - GPS coordinates
- notes: String? - Optional user notes
- isDefault: bool - Default location for arrow display
- createdAt: DateTime - Save timestamp

### TypeScript Interface
```typescript
interface SavedLocation {
  id: string;
  userId: string;
  name: string;
  category: 'hotel' | 'gate' | 'meeting_point' | 'custom';
  location: {
    latitude: number;
    longitude: number;
  };
  notes?: string;
  isDefault: boolean;
  createdAt: Date;
}
```

### Relationships
- Many-to-one with User

## RitualProgress

**Purpose:** GPS-guided and manual ritual counting with spiritual progress tracking

**Key Attributes:**
- id: String - Progress session identifier
- userId: String - Performing pilgrim
- ritualType: String - Tawaf or Sai
- isGpsGuided: bool - GPS vs manual counting
- currentRound: int - Current circuit number
- totalRounds: int - Target rounds (7 for both)
- gpsAccuracy: double? - GPS accuracy when applicable
- batteryAtStart: int - Initial battery level
- startTime: DateTime - Ritual start timestamp
- completedAt: DateTime? - Completion time
- isCompleted: bool - Ritual completion status

### TypeScript Interface
```typescript
interface RitualProgress {
  id: string;
  userId: string;
  ritualType: 'tawaf' | 'sai';
  isGpsGuided: boolean;
  currentRound: number;
  totalRounds: number;
  gpsAccuracy?: number;
  batteryAtStart: number;
  startTime: Date;
  completedAt?: Date;
  isCompleted: boolean;
}
```

### Relationships
- Many-to-one with User

## CrowdInsight

**Purpose:** Kaggle dataset-based crowd predictions for optimal pilgrimage timing

**Key Attributes:**
- id: String - Insight identifier
- placeId: String - Historical place reference
- placeName: String - Place name for quick reference
- hourOfDay: int - Hour (0-23) for prediction
- dayOfWeek: int - Day of week (1-7)
- crowdLevel: String - Low, Medium, High, Extreme
- crowdScore: int - Numerical crowd score (0-100)
- optimalVisitTime: String - Recommended time range
- lastUpdated: DateTime - Data refresh timestamp
- source: String - Always "kaggle_ziya07"
- seasonType: String - Hajj, Umrah, Regular

### TypeScript Interface
```typescript
interface CrowdInsight {
  id: string;
  placeId: string;
  placeName: string;
  hourOfDay: number;
  dayOfWeek: number;
  crowdLevel: 'low' | 'medium' | 'high' | 'extreme';
  crowdScore: number;
  optimalVisitTime: string;
  lastUpdated: Date;
  source: 'kaggle_ziya07';
  seasonType: 'hajj' | 'umrah' | 'regular';
}
```

### Relationships
- Many-to-one with HistoricalPlace

## NewsItem

**Purpose:** Hajj and Umrah news headlines with external browser links

**Key Attributes:**
- id: String - News item identifier
- title: String - News headline
- url: String - External link URL
- source: String - News source website
- publishedAt: DateTime - Original publication date
- category: String - Hajj, Umrah, General
- isVerified: bool - Editorial verification status
- language: String - Content language
- imageUrl: String? - Optional thumbnail image
- cachedAt: DateTime - Local cache timestamp

### TypeScript Interface
```typescript
interface NewsItem {
  id: string;
  title: string;
  url: string;
  source: string;
  publishedAt: Date;
  category: 'hajj' | 'umrah' | 'general';
  isVerified: boolean;
  language: string;
  imageUrl?: string;
  cachedAt: Date;
}
```

### Relationships
- Standalone entity with no direct relationships

## WeatherData

**Purpose:** Holy cities weather forecast for pilgrimage planning

**Key Attributes:**
- id: String - Weather data identifier
- city: String - Mecca or Medina
- currentTemp: int - Current temperature in Celsius
- feelsLike: int - Feels-like temperature
- humidity: int - Humidity percentage
- condition: String - Weather condition description
- icon: String - Weather icon identifier
- forecast3Day: List<Map> - 3-day forecast data
- lastUpdated: DateTime - Data refresh timestamp
- source: String - Weather API source

### TypeScript Interface
```typescript
interface WeatherData {
  id: string;
  city: 'mecca' | 'medina';
  currentTemp: number;
  feelsLike: number;
  humidity: number;
  condition: string;
  icon: string;
  forecast3Day: Array<{
    date: Date;
    tempMax: number;
    tempMin: number;
    condition: string;
  }>;
  lastUpdated: Date;
  source: string;
}
```

### Relationships
- Standalone entity with city-based lookup

## SermonContent

**Purpose:** YouTube sermon audio and caption caching with search algorithm support

**Key Attributes:**
- id: String - Content identifier
- youtubeVideoId: String - YouTube video ID
- title: String - Video title from YouTube
- mosque: String - Mecca or Medina
- language: String - Sermon language
- hijriDate: String - Hijri date in YYYY-MM-DD format
- audioUrl: String - Extracted audio stream URL
- captionText: String - Full caption/transcript text
- captionTimestamps: List<Map> - Caption segments with timing
- duration: int - Audio duration in seconds
- extractedAt: DateTime - Content extraction timestamp
- expiresAt: DateTime - Cache expiry (30 minutes)
- isLive: bool - Live vs archived sermon
- channelId: String - Always UCB0qibtjzOIemPjQSaoWkGg

### TypeScript Interface
```typescript
interface SermonContent {
  id: string;
  youtubeVideoId: string;
  title: string;
  mosque: 'mecca' | 'medina';
  language: 'english' | 'persian' | 'turkish' | 'urdu' | 'malay' | 'indonesian';
  hijriDate: string;
  audioUrl: string;
  captionText: string;
  captionTimestamps: Array<{
    startTime: number;
    endTime: number;
    text: string;
  }>;
  duration: number;
  extractedAt: Date;
  expiresAt: Date;
  isLive: boolean;
  channelId: string;
}
```

### Relationships
- Standalone entity with search-based retrieval

## PilgrimageGuideProgress

**Purpose:** Hajj and Umrah guide completion tracking with step-by-step progress

**Key Attributes:**
- id: String - Progress identifier
- userId: String - User tracking progress
- guideType: String - Hajj or Umrah
- currentStepId: String - Current guide step
- completedSteps: List<String> - Completed step IDs
- totalSteps: int - Total guide steps
- completionPercentage: double - Progress percentage
- startedAt: DateTime - Guide start time
- lastActivity: DateTime - Last step completion
- isCompleted: bool - Full guide completion status
- notes: String? - User notes or reflections

### TypeScript Interface
```typescript
interface PilgrimageGuideProgress {
  id: string;
  userId: string;
  guideType: 'hajj' | 'umrah';
  currentStepId: string;
  completedSteps: string[];
  totalSteps: number;
  completionPercentage: number;
  startedAt: Date;
  lastActivity: Date;
  isCompleted: boolean;
  notes?: string;
}
```

### Relationships
- Many-to-one with User
- References predefined guide step structure
