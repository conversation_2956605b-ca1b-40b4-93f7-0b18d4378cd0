# Coding Standards

## Critical Fullstack Rules

- **Islamic Data Sensitivity:** Never track location during prayer times - automatically pause family location sharing during detected prayer periods
- **Freemium Enforcement:** Always validate premium subscription server-side - never trust client-side premium status
- **Family Safety Priority:** Emergency messages bypass rate limiting with <1s latency requirement
- **YouTube API Compliance:** Sermon search must find exactly 1 matching video or throw SermonNotFoundException
- **Prayer Time Accuracy:** Use Al Adhan API directly from Flutter with 24-hour caching
- **Offline-First Data:** Critical features work without internet - SQLite mirrors Firebase schema
- **Multi-Technology Fallback:** Family tracking gracefully degrades GPS → WiFi → Bluetooth
- **Audio-Caption Sync:** Extract audio and captions in single operation with 30s buffer
- **Battery Optimization:** Adaptive GPS polling based on user state and prayer times
