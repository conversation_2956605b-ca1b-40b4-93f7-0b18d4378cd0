# API Specification

Based on the chosen Firebase + Cloud Functions architecture with REST API style, here's the complete API specification:

## REST API Specification

```yaml
openapi: 3.0.0
info:
  title: Ziarah Pilgrimage Companion API
  version: 1.0.0
  description: Comprehensive Islamic pilgrimage assistance API with Firebase Cloud Functions backend
servers:
  - url: https://ziarah-firebase-project.cloudfunctions.net/api
    description: Firebase Cloud Functions production endpoint
  - url: https://ziarah-firebase-project.cloudfunctions.net/api-dev
    description: Firebase Cloud Functions development endpoint

security:
  - FirebaseAuth: []

# External API Integrations (called directly from Flutter app)
external_apis:
  al_adhan_prayer_times:
    url: "https://api.aladhan.com/v1/timings/{date}"
    method: GET
    description: "Direct API call for daily prayer times"
    client: "Flutter app calls directly (no backend proxy needed)"
    failure_strategy:
      cache_duration: "24 hours"
      offline_fallback: "Static prayer time calculations using stored location coordinates"
      backup_apis: ["IslamicFinder API", "MuslimPro API"]
      degraded_mode: "Manual prayer time entry with time zone calculation"
      critical_impact: "HIGH - Prayer times are core spiritual functionality"

  al_adhan_hijri_conversion:
    url: "https://api.aladhan.com/v1/gToH/{date}"
    method: GET
    description: "Gregorian to Hijri date conversion"
    client: "Flutter app calls directly"
    failure_strategy:
      cache_duration: "30 days"
      offline_fallback: "Local Hijri calculation algorithm using Umm al-Qura method"
      backup_calculation: "Built-in Islamic calendar conversion library"
      degraded_mode: "Gregorian date display with manual Hijri input option"
      critical_impact: "MEDIUM - Important for Islamic context but not prayer functionality"

  youtube_data_api:
    url: "https://www.googleapis.com/youtube/v3/"
    method: GET
    description: "Search @tubesermon channel for Friday sermons"
    client: "Firebase Cloud Functions proxy"
    failure_strategy:
      cache_duration: "7 days for popular sermons"
      offline_fallback: "Pre-downloaded sermon library (latest 10 sermons per language)"
      backup_content: "Static Islamic audio content and Quran recitations"
      degraded_mode: "Text-based Friday sermon summaries"
      critical_impact: "LOW - Premium feature, alternative spiritual content available"

  google_maps_api:
    url: "https://maps.googleapis.com/maps/api/"
    method: GET
    description: "GPS services for ritual assistance and family tracking"
    client: "Flutter app with Firebase Cloud Functions for complex operations"
    failure_strategy:
      cache_duration: "Indefinite for holy site coordinates"
      offline_fallback: "Static coordinates for Kaaba, Safa, Marwah, major gates"
      backup_positioning: "Device GPS with manual coordinate verification"
      degraded_mode: "Manual counters with compass-based direction assistance"
      critical_impact: "HIGH - Core GPS features but manual alternatives available"

  openweathermap_api:
    url: "https://api.openweathermap.org/data/2.5/"
    method: GET
    description: "Weather data for Mecca and Medina"
    client: "Firebase Cloud Functions with caching"
    failure_strategy:
      cache_duration: "6 hours"
      offline_fallback: "Historical weather averages by month for holy cities"
      backup_data: "Static seasonal weather guidance for pilgrimage preparation"
      degraded_mode: "Remove weather widget, show general pilgrimage weather advice"
      critical_impact: "LOW - Convenience feature, not essential for pilgrimage"

paths:
  /auth/verify-premium:
    get:
      summary: Verify user premium subscription status
      description: Validates Firebase Auth token and checks premium subscription
      responses:
        '200':
          description: Premium status verified
          content:
            application/json:
              schema:
                type: object
                properties:
                  isPremium: { type: boolean }
                  expiresAt: { type: string, format: date-time }
                  subscriptionTier: { type: string }

  /family-groups:
    post:
      summary: Create new family group with QR code
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name: { type: string }
                qrExpiry: { type: string, format: date-time }
                emergencyContact: { type: string }
      responses:
        '201':
          description: Family group created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FamilyGroup'

    get:
      summary: Get user's family groups
      responses:
        '200':
          description: List of user's family groups
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FamilyGroup'

  /family-groups/{groupId}/join:
    post:
      summary: Join family group via QR code
      parameters:
        - name: groupId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                qrCode: { type: string }
      responses:
        '200':
          description: Successfully joined group
        '400':
          description: Invalid or expired QR code

  /family-groups/{groupId}/locations:
    post:
      summary: Update family location (different capabilities by tier)
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                location:
                  type: object
                  properties:
                    latitude: { type: number }
                    longitude: { type: number }
                locationMethod:
                  type: string
                  enum: [bluetooth, wifi_direct, gps]
                  description: "Free: bluetooth/wifi_direct only, Premium: adds GPS"
                batteryLevel: { type: integer }
                isEmergency: { type: boolean }
      responses:
        '200':
          description: Location updated (features vary by subscription)
          content:
            application/json:
              schema:
                type: object
                properties:
                  success: { type: boolean }
                  availableFeatures:
                    type: object
                    properties:
                      gpsTracking: { type: boolean, description: "Premium only" }
                      presetMessaging: { type: boolean, description: "Premium only" }
                      lastKnownLocation: { type: boolean, description: "Premium only" }
                      basicTracking: { type: boolean, description: "Always true" }

    get:
      summary: Get all family member locations
      parameters:
        - name: groupId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Current family locations
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FamilyLocationUpdate'

  /family-groups/{groupId}/messages:
    post:
      summary: Send family messages (Premium feature)
      security:
        - FirebaseAuth: []
        - PremiumRequired: []
      description: "Preset messaging system requires premium subscription"
      parameters:
        - name: groupId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FamilyMessage'
      responses:
        '200':
          description: Message sent successfully
        '403':
          description: Premium subscription required

  /sermons/extract-content:
    post:
      summary: Extract audio and captions from YouTube using youtube_explode_dart
      description: "Cloud Function processes @tubesermon channel with multi-step search algorithm"
      security:
        - FirebaseAuth: []
        - PremiumRequired: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                mosque: { type: string, enum: [mecca, medina] }
                language: { type: string, enum: [english, persian, turkish, urdu, malay, indonesian] }
                sermonType: { type: string, enum: [live, last_friday] }
                targetDate: { type: string, description: "Gregorian date for Hijri conversion" }
      responses:
        '200':
          description: Audio URL and caption text extracted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  audioUrl: { type: string, description: "64kbps AAC/Opus stream URL" }
                  captionText: { type: string, description: "Full VTT/SRT caption text" }
                  captionTimestamps:
                    type: array
                    items:
                      type: object
                      properties:
                        startTime: { type: number }
                        endTime: { type: number }
                        text: { type: string }
                  duration: { type: integer }
                  cacheExpiresAt: { type: string, description: "30-minute cache expiry" }
                  youtubeVideoId: { type: string }
                  title: { type: string }
        '404':
          description: "No matching sermon found with search algorithm"
        '403':
          description: "Premium subscription required"

  /historical-places:
    get:
      summary: Get Islamic historical places
      parameters:
        - name: city
          in: query
          schema:
            type: string
            enum: [mecca, medina, road_between]
        - name: category
          in: query
          schema:
            type: string
        - name: period
          in: query
          schema:
            type: string
        - name: search
          in: query
          schema:
            type: string
      responses:
        '200':
          description: List of historical places
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HistoricalPlace'

  /historical-places/{placeId}:
    get:
      summary: Get historical place details (features vary by tier)
      parameters:
        - name: placeId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Place information with tier-appropriate content
          content:
            application/json:
              schema:
                type: object
                properties:
                  # Free tier gets these fields
                  id: { type: string }
                  name: { type: string }
                  category: { type: string }
                  description: { type: string }
                  location:
                    type: object
                    properties:
                      latitude: { type: number }
                      longitude: { type: number }
                  city: { type: string }
                  accessInfo: { type: string }

                  # Premium tier gets additional fields
                  hadithReferences:
                    type: array
                    items: { type: string }
                    description: "Premium only - Authenticated Hadith citations"
                  quranReferences:
                    type: array
                    items: { type: string }
                    description: "Premium only - Relevant Quran verses"
                  canAddToItinerary:
                    type: boolean
                    description: "Premium only - MyItineraries integration"
                  isPremiumContent: { type: boolean }

  /itineraries:
    get:
      summary: Get user itineraries (Premium only)
      security:
        - FirebaseAuth: []
        - PremiumRequired: []
      description: "MyItineraries feature requires premium subscription"
      responses:
        '200':
          description: List of user itineraries
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Itinerary'
        '403':
          description: Premium subscription required

    post:
      summary: Create itinerary (Premium only)
      security:
        - FirebaseAuth: []
        - PremiumRequired: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Itinerary'
      responses:
        '201':
          description: Itinerary created successfully
        '403':
          description: Premium subscription required

  /crowd-insights:
    get:
      summary: Get crowd predictions for holy sites
      parameters:
        - name: placeId
          in: query
          schema:
            type: string
        - name: date
          in: query
          schema:
            type: string
            format: date
        - name: hour
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: Crowd insights data
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CrowdInsight'

  /news:
    get:
      summary: Get Hajj and Umrah news headlines
      parameters:
        - name: category
          in: query
          schema:
            type: string
            enum: [hajj, umrah, general]
        - name: language
          in: query
          schema:
            type: string
      responses:
        '200':
          description: News headlines with external links
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/NewsItem'

  /weather:
    get:
      summary: Get weather forecast for holy cities
      parameters:
        - name: city
          in: query
          required: true
          schema:
            type: string
            enum: [mecca, medina]
      responses:
        '200':
          description: Weather data for holy cities
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WeatherData'

  /ritual-progress:
    post:
      summary: Save ritual progress (GPS features premium only)
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                ritualType: { type: string, enum: [tawaf, sai] }
                isGpsGuided:
                  type: boolean
                  description: "Premium only - free users get manual counters only"
                currentRound: { type: integer }
                # GPS-specific fields (premium only)
                gpsAccuracy: { type: number, description: "Premium only" }
                location:
                  type: object
                  description: "Premium only"
                  properties:
                    latitude: { type: number }
                    longitude: { type: number }
      responses:
        '200':
          description: Progress saved with appropriate features
        '403':
          description: GPS features require premium subscription

    get:
      summary: Get user's ritual progress history
      responses:
        '200':
          description: Ritual progress records
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RitualProgress'

components:
  securitySchemes:
    FirebaseAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: Firebase Authentication ID token
    PremiumRequired:
      type: apiKey
      in: header
      name: X-Premium-Required
      description: Premium subscription validation

  schemas:
    FamilyGroup:
      type: object
      properties:
        id: { type: string }
        name: { type: string }
        creatorId: { type: string }
        qrCode: { type: string }
        qrExpiry: { type: string, format: date-time }
        isActive: { type: boolean }
        memberIds: { type: array, items: { type: string } }
        emergencyContactPhone: { type: string }
        hotelLocation:
          type: object
          properties:
            latitude: { type: number }
            longitude: { type: number }

    FamilyLocationUpdate:
      type: object
      properties:
        id: { type: string }
        groupId: { type: string }
        userId: { type: string }
        location:
          type: object
          properties:
            latitude: { type: number }
            longitude: { type: number }
        accuracy: { type: number }
        locationMethod: { type: string, enum: [gps, bluetooth, wifi_direct] }
        batteryLevel: { type: integer }
        isEmergency: { type: boolean }
        timestamp: { type: string, format: date-time }

    FamilyMessage:
      type: object
      properties:
        id: { type: string }
        groupId: { type: string }
        senderId: { type: string }
        messageType: { type: string, enum: [come_to_me, go_to_hotel, im_safe, need_help, wait_for_me, custom] }
        customMessage: { type: string }
        senderLocation:
          type: object
          properties:
            latitude: { type: number }
            longitude: { type: number }
        timestamp: { type: string, format: date-time }
        isEmergency: { type: boolean }

    HistoricalPlace:
      type: object
      properties:
        id: { type: string }
        name: { type: string }
        nameArabic: { type: string }
        category: { type: string, enum: [mosque, cave, battlefield, mountain, well, house] }
        historicalPeriod: { type: string, enum: [prophet, rashidun, umayyad, abbasid, modern] }
        description: { type: string }
        location:
          type: object
          properties:
            latitude: { type: number }
            longitude: { type: number }
        city: { type: string, enum: [mecca, medina, road_between] }
        accessInfo: { type: string }
        disclaimer: { type: string }
        hadithReferences: { type: array, items: { type: string } }
        quranReferences: { type: array, items: { type: string } }
        imageUrls: { type: array, items: { type: string } }
        isVerified: { type: boolean }
        isPremiumContent: { type: boolean }

    Itinerary:
      type: object
      properties:
        id: { type: string }
        userId: { type: string }
        name: { type: string }
        description: { type: string }
        type: { type: string, enum: [hajj, umrah, combined] }
        placeIds: { type: array, items: { type: string } }
        estimatedDuration: { type: integer }
        isShared: { type: boolean }
        crowdOptimized: { type: boolean }

    CrowdInsight:
      type: object
      properties:
        id: { type: string }
        placeId: { type: string }
        placeName: { type: string }
        hourOfDay: { type: integer }
        dayOfWeek: { type: integer }
        crowdLevel: { type: string, enum: [low, medium, high, extreme] }
        crowdScore: { type: integer }
        optimalVisitTime: { type: string }
        lastUpdated: { type: string, format: date-time }
        source: { type: string, enum: [kaggle_ziya07] }
        seasonType: { type: string, enum: [hajj, umrah, regular] }

    NewsItem:
      type: object
      properties:
        id: { type: string }
        title: { type: string }
        url: { type: string }
        source: { type: string }
        publishedAt: { type: string, format: date-time }
        category: { type: string, enum: [hajj, umrah, general] }
        isVerified: { type: boolean }
        language: { type: string }
        imageUrl: { type: string }

    WeatherData:
      type: object
      properties:
        id: { type: string }
        city: { type: string, enum: [mecca, medina] }
        currentTemp: { type: integer }
        feelsLike: { type: integer }
        humidity: { type: integer }
        condition: { type: string }
        icon: { type: string }
        forecast3Day:
          type: array
          items:
            type: object
            properties:
              date: { type: string, format: date }
              tempMax: { type: integer }
              tempMin: { type: integer }
              condition: { type: string }

    RitualProgress:
      type: object
      properties:
        id: { type: string }
        userId: { type: string }
        ritualType: { type: string, enum: [tawaf, sai] }
        isGpsGuided: { type: boolean }
        currentRound: { type: integer }
        totalRounds: { type: integer }
        gpsAccuracy: { type: number }
        batteryAtStart: { type: integer }
        startTime: { type: string, format: date-time }
        completedAt: { type: string, format: date-time }
        isCompleted: { type: boolean }
```
