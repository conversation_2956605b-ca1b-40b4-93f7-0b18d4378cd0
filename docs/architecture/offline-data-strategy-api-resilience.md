# Offline Data Strategy & API Resilience

## Critical Offline Data Requirements

Based on the external API failure strategies defined above, the following offline data must be bundled with the application:

### Embedded Static Data (App Bundle)
```yaml
holy_site_coordinates:
  kaaba_center: {lat: 21.422487, lng: 39.826206}
  safa_point: {lat: 21.423056, lng: 39.827778}
  marwah_point: {lat: 21.421944, lng: 39.828333}
  masjid_nabawi: {lat: 24.467775, lng: 39.611146}
  major_gates:
    - {name: "King <PERSON>", lat: 21.421389, lng: 39.826944}
    - {name: "Umrah Gate", lat: 21.423611, lng: 39.827500}
    - {name: "King Fahd Gate", lat: 21.424722, lng: 39.825556}

prayer_calculation_constants:
  mecca_coordinates: {lat: 21.4225, lng: 39.8262}
  medina_coordinates: {lat: 24.4677, lng: 39.6111}
  calculation_methods:
    - umm_al_qura: {fajr_angle: 18.5, isha_angle: 90, isha_interval: 120}
    - muslim_world_league: {fajr_angle: 18, isha_angle: 17}
    - egyptian: {fajr_angle: 19.5, isha_angle: 17.5}

hijri_calculation_data:
  epoch_date: "622-07-16"  # Gregorian date of Hijri epoch
  leap_year_cycle: [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29]
  month_lengths: [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29]

weather_fallback_data:
  mecca_seasonal_averages:
    winter: {temp_avg: 25, humidity: 45, advice: "Mild weather, light layers recommended"}
    spring: {temp_avg: 32, humidity: 35, advice: "Warm days, stay hydrated"}
    summer: {temp_avg: 42, humidity: 30, advice: "Extreme heat, seek shade frequently"}
    autumn: {temp_avg: 35, humidity: 40, advice: "Hot weather continuing, protect from sun"}
  medina_seasonal_averages:
    winter: {temp_avg: 20, humidity: 50, advice: "Cool weather, warmer clothes needed"}
    spring: {temp_avg: 28, humidity: 40, advice: "Pleasant weather for walking"}
    summer: {temp_avg: 38, humidity: 35, advice: "Very hot, early morning visits recommended"}
    autumn: {temp_avg: 30, humidity: 45, advice: "Comfortable temperatures returning"}

backup_islamic_content:
  essential_duas:
    - {name: "Tawaf Dua", arabic: "...", transliteration: "...", translation: "..."}
    - {name: "Sa'i Dua", arabic: "...", transliteration: "...", translation: "..."}
  basic_historical_places:
    - {name: "Cave of Hira", period: "Prophet", category: "Cave", basic_description: "..."}
    - {name: "Masjid Quba", period: "Prophet", category: "Mosque", basic_description: "..."}
```

### SQLite Offline Cache Schema
```sql
-- Prayer times cache (24-48 hour window)
CREATE TABLE prayer_times_cache (
    date TEXT PRIMARY KEY,
    location_lat REAL,
    location_lng REAL,
    fajr_time TEXT,
    sunrise_time TEXT,
    dhuhr_time TEXT,
    asr_time TEXT,
    maghrib_time TEXT,
    isha_time TEXT,
    calculation_method TEXT,
    cached_at TIMESTAMP,
    expires_at TIMESTAMP
);

-- Hijri date cache (30 day window)
CREATE TABLE hijri_cache (
    gregorian_date TEXT PRIMARY KEY,
    hijri_year INTEGER,
    hijri_month INTEGER,
    hijri_day INTEGER,
    month_name TEXT,
    cached_at TIMESTAMP
);

-- Sermon content cache (premium feature)
CREATE TABLE sermon_cache (
    video_id TEXT PRIMARY KEY,
    title TEXT,
    mosque TEXT,
    language TEXT,
    hijri_date TEXT,
    audio_file_path TEXT,
    captions_text TEXT,
    duration INTEGER,
    download_completed BOOLEAN,
    cached_at TIMESTAMP,
    expires_at TIMESTAMP
);

-- Weather data cache (6 hour refresh)
CREATE TABLE weather_cache (
    city TEXT PRIMARY KEY,
    current_temp INTEGER,
    feels_like INTEGER,
    humidity INTEGER,
    condition TEXT,
    forecast_json TEXT,
    cached_at TIMESTAMP,
    expires_at TIMESTAMP
);

-- Family location cache (for offline family finder)
CREATE TABLE family_location_cache (
    group_id TEXT,
    user_id TEXT,
    lat REAL,
    lng REAL,
    accuracy REAL,
    battery_level INTEGER,
    timestamp TIMESTAMP,
    PRIMARY KEY (group_id, user_id)
);
```

### API Failure Detection & Recovery Logic
```yaml
failure_detection:
  timeout_thresholds:
    prayer_times_api: 10_seconds
    hijri_conversion_api: 5_seconds
    youtube_api: 15_seconds
    google_maps_api: 8_seconds
    weather_api: 12_seconds

  retry_strategies:
    exponential_backoff: [1s, 2s, 4s, 8s, 16s]
    max_retries: 3
    circuit_breaker: "Open circuit after 5 consecutive failures"
    recovery_test_interval: 60_seconds

graceful_degradation_ui:
  prayer_times_offline:
    message: "Using calculated prayer times for your location"
    indicator: "📱 Offline Mode"
    accuracy_note: "Times calculated locally, may vary by ±2 minutes"

  sermon_unavailable:
    message: "Sermon streaming unavailable - browse downloaded content"
    alternative: "Access Quran recitations and saved sermons"
    upgrade_path: "Premium subscribers: Download sermons for offline access"

  maps_offline:
    message: "GPS guidance limited - manual counting available"
    fallback: "Use compass and manual counter for spiritual focus"
    accuracy_note: "Sacred coordinates available for direction assistance"

  weather_fallback:
    message: "Current weather unavailable - showing seasonal guidance"
    content: "Historical averages and pilgrimage weather advice"
    refresh_option: "Try refreshing when connection improves"
```

## Implementation Priority for Offline Features
1. **Phase 1**: Prayer time offline calculations (critical spiritual functionality)
2. **Phase 2**: Static holy site coordinates and manual counters
3. **Phase 3**: Family location caching and Bluetooth fallbacks
4. **Phase 4**: Sermon downloading and offline content library
5. **Phase 5**: Weather fallback data and seasonal guidance
