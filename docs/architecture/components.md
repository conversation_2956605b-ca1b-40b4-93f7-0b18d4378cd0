# Components

Based on the architectural patterns, tech stack, and data models, I've identified the major logical components/services across the fullstack:

## Home Tab Dashboard Component

**Responsibility:** Unified dashboard with three distinct sections and 2x2 feature grid

**Key Interfaces:**
- **Top Section**: News headlines (Hajj/Umrah) with external browser links + Weather forecast (Mecca & Medina)
- **Middle Section**: Arrow pointing to selected saved location from Quick Map
- **Bottom Section**: 2x2 tiled icons (Quick Map, Family Finder, MyItineraries, Crowd Insights)

**Dependencies:** News scraping service, OpenWeatherMap API, saved locations, all four feature services

**Technology Stack:** Web scraping (html package), OpenWeatherMap API, Firebase for saved locations, feature grid navigation

## Quick Map Component (Free Feature)

**Responsibility:** Location management with custom saved locations and arrow display on Home tab

**Key Interfaces:**
- Add/edit custom locations (Hotel, Gate, Meeting Points)
- Location selection for Home tab arrow display
- GPS coordinate saving and naming
- Integration with Google Maps for location picking

**Dependencies:** Google Maps API, device location services, local storage

**Technology Stack:** Google Maps API, geolocator, SQLite for saved locations, Material Design location picker

## Family Finder Component (Freemium - Basic vs Full)

**Responsibility:** Multi-technology family tracking with QR group creation and tiered feature access

**Key Interfaces:**
- **FREE TIER**: Basic tracking (Bluetooth LE + WiFi Direct only)
- **PREMIUM TIER**: Full features (adds Google Maps API + preset messaging + last known location)
- QR code group creation with preset expiry dates
- Family member map display (technology varies by tier)
- Directional guidance to family members (premium only)

**Dependencies:** QR code generation, Bluetooth LE, WiFi Direct, Google Maps API (premium), Firebase Realtime Database

**Technology Stack:** qr_flutter, flutter_blue_plus, wifi_direct_flutter, Google Maps API (premium), Firebase Realtime Database

## Prayer Times Component (Free Feature)

**Responsibility:** Complete prayer time functionality with Al Adhan API integration

**Key Interfaces:**
- Direct API calls to Al Adhan Prayer Times API (https://aladhan.com/prayer-times-api)
- Direct API calls to Al Adhan Islamic Calendar API (https://aladhan.com/islamic-calendar-api)
- Local daily prayer times display with azan notifications
- Gregorian to Hijri date conversion

**Dependencies:** Al Adhan APIs (direct Flutter app calls), device notification system

**Technology Stack:** Al Adhan APIs (direct integration), local_notifications, HTTP client (dio)

## Knowledge Hub Component (Mixed Freemium)

**Responsibility:** Ibadah guides and ritual assistance tools with freemium differentiation

**Key Interfaces:**
- **FREE**: Ibadah Guide for Hajj with tracker (full access)
- **FREE**: Ibadah Guide for Umrah with tracker (full access)
- **FREE**: MyIbadah manual counters for Tawaf and Sa'i (full access)
- **PREMIUM**: GPS-guided Tawaf counter with Google Maps API
- **PREMIUM**: GPS-guided Sa'i counter with Google Maps API

**Dependencies:** Google Maps API (premium features), device GPS, premium subscription validation

**Technology Stack:** Google Maps API (premium), geolocator (premium), manual counter widgets (free), premium feature gating

## Friday Sermon Component (Premium Only)

**Responsibility:** Complete sermon delivery system with sophisticated YouTube integration

**Key Interfaces:**
- Live Friday Sermon and Last Friday Sermon selection
- Location choice (Mecca/Medina) and language selection (6 languages)
- Sophisticated search algorithm implementation with exact matching
- Unified audio-caption streaming using youtube_explode_dart + just_audio
- Premium subscription gating (completely unavailable for free users)

**Dependencies:** YouTube Data API v3, @tubesermon channel, Al Adhan API for Hijri conversion, premium validation

**Technology Stack:** YouTube Data API, youtube_explode_dart, just_audio, sophisticated search algorithm, Firebase Cloud Functions, premium gating

## Historical Places Component (Mixed Freemium)

**Responsibility:** Islamic historical sites database with tiered feature access

**Key Interfaces:**
- **FREE**: Basic information (name, historical period, category, description, GPS locations, access information & disclaimer)
- **PREMIUM**: Advanced features (Hadith/Quran references + "Add to MyItinerary" button integration)
- "Get directions" button (available to all users)
- List filtering and search functionality

**Dependencies:** Historical places database, premium subscription validation, Google Maps API for directions

**Technology Stack:** Firebase Firestore for places database, Google Maps API for directions, premium feature gating for advanced content
