# Ziarah Fullstack Architecture Document

## Table of Contents

- [<PERSON><PERSON><PERSON>stack Architecture Document](#table-of-contents)
  - [Introduction](./introduction.md)
    - [Starter Template or Existing Project](./introduction.md#starter-template-or-existing-project)
    - [Change Log](./introduction.md#change-log)
  - [High Level Architecture](./high-level-architecture.md)
    - [Technical Summary](./high-level-architecture.md#technical-summary)
    - [Platform and Infrastructure Choice](./high-level-architecture.md#platform-and-infrastructure-choice)
    - [Repository Structure](./high-level-architecture.md#repository-structure)
    - [High Level Architecture Diagram](./high-level-architecture.md#high-level-architecture-diagram)
    - [Architectural Patterns](./high-level-architecture.md#architectural-patterns)
  - [Tech Stack](./tech-stack.md)
    - [Technology Stack Table](./tech-stack.md#technology-stack-table)
  - [Data Models](./data-models.md)
    - [User](./data-models.md#user)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [FamilyGroup](./data-models.md#familygroup)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [FamilyLocationUpdate](./data-models.md#familylocationupdate)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [FamilyMessage](./data-models.md#familymessage)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [HistoricalPlace](./data-models.md#historicalplace)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [Itinerary](./data-models.md#itinerary)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [SavedLocation](./data-models.md#savedlocation)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [RitualProgress](./data-models.md#ritualprogress)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [CrowdInsight](./data-models.md#crowdinsight)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [NewsItem](./data-models.md#newsitem)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [WeatherData](./data-models.md#weatherdata)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [SermonContent](./data-models.md#sermoncontent)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [PilgrimageGuideProgress](./data-models.md#pilgrimageguideprogress)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
  - [API Specification](./api-specification.md)
    - [REST API Specification](./api-specification.md#rest-api-specification)
  - [Components](./components.md)
    - [Home Tab Dashboard Component](./components.md#home-tab-dashboard-component)
    - [Quick Map Component (Free Feature)](./components.md#quick-map-component-free-feature)
    - [Family Finder Component (Freemium - Basic vs Full)](./components.md#family-finder-component-freemium-basic-vs-full)
    - [Prayer Times Component (Free Feature)](./components.md#prayer-times-component-free-feature)
    - [Knowledge Hub Component (Mixed Freemium)](./components.md#knowledge-hub-component-mixed-freemium)
    - [Friday Sermon Component (Premium Only)](./components.md#friday-sermon-component-premium-only)
    - [Historical Places Component (Mixed Freemium)](./components.md#historical-places-component-mixed-freemium)
  - [Offline Data Strategy & API Resilience](./offline-data-strategy-api-resilience.md)
    - [Critical Offline Data Requirements](./offline-data-strategy-api-resilience.md#critical-offline-data-requirements)
      - [Embedded Static Data (App Bundle)](./offline-data-strategy-api-resilience.md#embedded-static-data-app-bundle)
      - [SQLite Offline Cache Schema](./offline-data-strategy-api-resilience.md#sqlite-offline-cache-schema)
      - [API Failure Detection & Recovery Logic](./offline-data-strategy-api-resilience.md#api-failure-detection-recovery-logic)
    - [Implementation Priority for Offline Features](./offline-data-strategy-api-resilience.md#implementation-priority-for-offline-features)
  - [Unified Project Structure](./unified-project-structure.md)
  - [Coding Standards](./coding-standards.md)
    - [Critical Fullstack Rules](./coding-standards.md#critical-fullstack-rules)
  - [Performance Requirements & Testing Methodology](./performance-requirements-testing-methodology.md)
    - [12+ Hour Battery Life Validation](./performance-requirements-testing-methodology.md#12-hour-battery-life-validation)
      - [Battery Testing Protocol](./performance-requirements-testing-methodology.md#battery-testing-protocol)
        - [Test Environment Setup](./performance-requirements-testing-methodology.md#test-environment-setup)
        - [Performance Benchmarks](./performance-requirements-testing-methodology.md#performance-benchmarks)
        - [Battery Optimization Strategies](./performance-requirements-testing-methodology.md#battery-optimization-strategies)
        - [Testing Automation Scripts](./performance-requirements-testing-methodology.md#testing-automation-scripts)
        - [Performance Monitoring in Production](./performance-requirements-testing-methodology.md#performance-monitoring-in-production)
      - [Development Phase Testing Schedule](./performance-requirements-testing-methodology.md#development-phase-testing-schedule)
  - [Executive Summary](./executive-summary.md)
    - [Architecture Completeness: 100% READY FOR DEVELOPMENT](./executive-summary.md#architecture-completeness-100-ready-for-development)
