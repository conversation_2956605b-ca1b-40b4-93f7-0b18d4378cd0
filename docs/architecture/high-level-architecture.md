# High Level Architecture

## Technical Summary

<PERSON> employs a **hybrid mobile-first architecture** combining Flutter's cross-platform framework with Firebase's serverless backend ecosystem. The **offline-first, real-time synchronization** approach ensures core pilgrimage features (prayer times, manual counters, family tracking) operate reliably in challenging connectivity environments while leveraging cloud services for enhanced premium features. The architecture integrates **multi-technology location services** (GPS, Bluetooth Low Energy, WiFi Direct) through Firebase Realtime Database for family safety coordination, while **YouTube API integration** with sophisticated search algorithms delivers authentic Islamic content. **Google Cloud Platform hosting** provides scalable infrastructure supporting the projected 100,000 downloads with $10/year freemium sustainability, ensuring 99.5% uptime for critical spiritual guidance during pilgrimage.

## Platform and Infrastructure Choice

Based on PRD requirements and technical assumptions, I'm recommending:

**Recommended Platform: Firebase + Google Cloud Platform**

**Alternative Options Considered:**
1. **Firebase + GCP (Recommended)**: Rapid development, real-time capabilities, excellent Flutter integration, cost-effective for freemium model
2. **AWS Full Stack**: More enterprise features but higher complexity for mobile-first approach and steeper learning curve
3. **Supabase + Vercel**: Open-source alternative but limited real-time family tracking capabilities and less mature Flutter ecosystem

**Platform:** Google Cloud Platform with Firebase
**Key Services:** Firebase Auth, Realtime Database, Cloud Functions, Firebase Storage, Cloud Messaging, YouTube Data API, Google Maps API, Al Adhan API
**Deployment Host and Regions:** Global deployment with primary regions in Middle East (asia-west1), Europe (europe-west1), and North America (us-central1) for optimal pilgrimage user coverage

## Repository Structure

**Structure:** Monorepo with feature-based organization
**Monorepo Tool:** Flutter's built-in package system with melos for mono-repo management
**Package Organization:** Feature-driven modules (prayer_times, family_finder, gps_counter, sermon_player) with shared core utilities and Islamic-specific components

Rationale: Monorepo approach enables atomic commits across mobile app and cloud functions, simplifies dependency management for Islamic date calculations and prayer time algorithms, and supports efficient code sharing between features while maintaining clear separation of concerns for different pilgrimage functionalities.

## High Level Architecture Diagram

```mermaid
graph TD
    A[Mobile Users] --> B[Flutter Mobile App]
    B --> C[Firebase Authentication]
    B --> D[Google Maps API]
    B --> E[YouTube Data API]
    B --> F[Al Adhan Prayer API]

    B --> G[Firebase Realtime Database]
    B --> H[Firebase Cloud Functions]
    B --> I[Firebase Storage]

    G --> J[Family Location Data]
    G --> K[User Preferences]
    G --> L[Offline Cache]

    H --> M[Prayer Time Calculations]
    H --> N[Sermon Search Algorithm]
    H --> O[Family Safety Notifications]

    I --> P[Historical Places Images]
    I --> Q[Cached Audio Content]

    R[External APIs] --> F
    R --> E
    R --> S[Kaggle Crowd Data]

    T[Device Hardware] --> U[GPS/Location]
    T --> V[Bluetooth LE]
    T --> W[WiFi Direct]
    T --> X[Local SQLite]

    B --> T
```

## Architectural Patterns

- **Offline-First Architecture:** SQLite local storage with Firebase background sync ensures core functionality during poor connectivity - _Rationale:_ Pilgrimage environments often have unreliable network access, spiritual activities cannot depend on internet
- **Real-Time Observer Pattern:** Firebase Realtime Database for family location updates and emergency notifications - _Rationale:_ Family safety requires immediate location updates and emergency communication capabilities
- **Repository Pattern:** Abstract data access for prayer times, historical places, and user preferences with offline/online switching - _Rationale:_ Enables seamless offline/online data flow and simplifies testing of Islamic calendar calculations
- **BLoC State Management:** Business Logic Components for complex state management across GPS tracking, family coordination, and content streaming - _Rationale:_ Provides predictable state management for safety-critical features and complex pilgrimage workflows
- **Multi-Technology Integration Pattern:** Facade pattern for GPS/Bluetooth/WiFi location services with graceful degradation - _Rationale:_ Family safety requires redundant location technologies when GPS fails in crowded sacred spaces
- **Freemium Feature Gate Pattern:** Decorator pattern for premium feature access control with seamless upgrade flow - _Rationale:_ Enables sustainable business model while providing essential Islamic functions for free
- **Islamic Calendar Abstraction:** Strategy pattern for different Hijri calculation methods and prayer time schools - _Rationale:_ Accommodates diverse Islamic practices across global Muslim community
- **Content Streaming Pipeline:** Chain of responsibility for YouTube content extraction, audio processing, and caption synchronization - _Rationale:_ Delivers authentic Islamic content with efficient data usage and battery optimization
