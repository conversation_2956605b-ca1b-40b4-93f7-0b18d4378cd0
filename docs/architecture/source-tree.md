# Source Tree Structure

This document provides the definitive file and folder organization for the Ziarah Islamic pilgrimage companion application. All development must follow this structure to ensure consistency, maintainability, and effective team collaboration.

## Project Overview

Ziarah uses a **monorepo architecture** with **feature-based organization** for the Flutter mobile app and **domain-based organization** for Firebase Cloud Functions. The structure supports both Islamic cultural requirements and modern development practices.

## Root Directory Structure

```
ziarah/
├── .bmad-core/                 # BMAD Core configuration
│   ├── core-config.yaml       # Project configuration
│   ├── tasks/                 # Automation tasks
│   ├── templates/             # Code templates
│   └── checklists/            # Quality checklists
├── .github/                   # CI/CD workflows
│   └── workflows/
│       ├── ci.yaml            # Automated testing and linting
│       ├── deploy-functions.yaml # Firebase Functions deployment
│       └── deploy-android.yaml   # Play Store deployment
├── apps/                      # Application packages
│   ├── mobile/                # Flutter mobile application
│   └── cloud_functions/       # Firebase Cloud Functions (Dart)
├── packages/                  # Shared packages
│   ├── shared_models/         # Data models (Dart/TypeScript)
│   ├── islamic_utils/         # Islamic calculations and utilities
│   └── premium_core/          # Premium feature management
├── infrastructure/            # Firebase configuration
│   ├── firebase.json          # Firebase project configuration
│   ├── firestore.rules        # Database security rules
│   └── storage.rules          # Storage security rules
├── scripts/                   # Build and deployment scripts
│   ├── build.sh              # Build automation
│   ├── deploy.sh             # Deployment automation
│   └── test.sh               # Testing automation
├── docs/                     # Documentation (sharded)
│   ├── prd/                  # Product Requirements (sharded)
│   ├── architecture/         # Technical Architecture (sharded)
│   ├── front-end-spec/       # UI/UX Specifications (sharded)
│   └── knowledge-transfer-plan/ # Development handoff (sharded)
├── .gitignore                # Git ignore patterns
├── melos.yaml                # Monorepo configuration
└── README.md                 # Project overview
```

## Flutter Mobile App Structure

### Core Organization Principles
- **Feature-based organization**: Each major feature is self-contained
- **Islamic-first design**: Cultural considerations integrated throughout
- **Freemium support**: Clear separation of free vs premium features
- **Offline-first**: Local storage and caching prioritized

```
apps/mobile/
├── android/                   # Android-specific configuration
│   ├── app/
│   │   ├── build.gradle      # Android build configuration
│   │   └── src/main/
│   │       ├── AndroidManifest.xml # Permissions and app config
│   │       └── res/          # Android resources
│   └── gradle.properties     # Android build properties
├── lib/                      # Flutter source code
│   ├── main.dart            # App entry point
│   ├── features/            # Feature-based organization
│   │   ├── home/            # Home tab with 2x2 grid (FREE)
│   │   │   ├── presentation/ # UI layer
│   │   │   │   ├── pages/   # Screen widgets
│   │   │   │   ├── widgets/ # Reusable UI components
│   │   │   │   └── bloc/    # State management
│   │   │   ├── domain/      # Business logic
│   │   │   │   ├── entities/ # Core business objects
│   │   │   │   ├── repositories/ # Abstract data contracts
│   │   │   │   └── usecases/ # Business operations
│   │   │   └── data/        # Data layer
│   │   │       ├── models/  # Data transfer objects
│   │   │       ├── datasources/ # API and local data
│   │   │       └── repositories/ # Repository implementations
│   │   │
│   │   ├── prayer_times/    # Prayer Times (FREE - Al Adhan APIs)
│   │   │   ├── presentation/
│   │   │   │   ├── pages/
│   │   │   │   │   ├── prayer_times_page.dart
│   │   │   │   │   └── hijri_calendar_page.dart
│   │   │   │   ├── widgets/
│   │   │   │   │   ├── prayer_card_widget.dart
│   │   │   │   │   ├── qibla_compass_widget.dart
│   │   │   │   │   └── countdown_widget.dart
│   │   │   │   └── bloc/
│   │   │   │       ├── prayer_times_bloc.dart
│   │   │   │       └── hijri_calendar_bloc.dart
│   │   │   ├── domain/
│   │   │   │   ├── entities/
│   │   │   │   │   ├── prayer_times.dart
│   │   │   │   │   └── hijri_date.dart
│   │   │   │   ├── repositories/
│   │   │   │   │   └── prayer_repository.dart
│   │   │   │   └── usecases/
│   │   │   │       ├── get_prayer_times.dart
│   │   │   │       └── convert_to_hijri.dart
│   │   │   └── data/
│   │   │       ├── models/
│   │   │       │   ├── prayer_times_model.dart
│   │   │       │   └── hijri_date_model.dart
│   │   │       ├── datasources/
│   │   │       │   ├── al_adhan_api.dart
│   │   │       │   └── prayer_local_storage.dart
│   │   │       └── repositories/
│   │   │           └── prayer_repository_impl.dart
│   │   │
│   │   ├── knowledge_hub/   # Mixed freemium (GPS premium)
│   │   │   ├── presentation/
│   │   │   │   ├── pages/
│   │   │   │   │   ├── hajj_guide_page.dart
│   │   │   │   │   ├── umrah_guide_page.dart
│   │   │   │   │   ├── manual_counter_page.dart
│   │   │   │   │   └── gps_counter_page.dart (PREMIUM)
│   │   │   │   ├── widgets/
│   │   │   │   │   ├── ritual_counter_widget.dart
│   │   │   │   │   ├── guide_step_widget.dart
│   │   │   │   │   └── progress_tracker_widget.dart
│   │   │   │   └── bloc/
│   │   │   │       ├── guide_progress_bloc.dart
│   │   │   │       └── ritual_counter_bloc.dart
│   │   │   ├── domain/ # Business logic for ritual guidance
│   │   │   └── data/   # Local storage and premium validation
│   │   │
│   │   ├── friday_sermon/   # PREMIUM ONLY (YouTube integration)
│   │   │   ├── presentation/
│   │   │   │   ├── pages/
│   │   │   │   │   ├── sermon_player_page.dart
│   │   │   │   │   └── sermon_selection_page.dart
│   │   │   │   ├── widgets/
│   │   │   │   │   ├── audio_player_widget.dart
│   │   │   │   │   ├── caption_display_widget.dart
│   │   │   │   │   └── language_selector_widget.dart
│   │   │   │   └── bloc/
│   │   │   │       ├── sermon_player_bloc.dart
│   │   │   │       └── sermon_search_bloc.dart
│   │   │   ├── domain/ # Sermon search algorithm and audio processing
│   │   │   └── data/   # YouTube API integration and caching
│   │   │
│   │   ├── historical_places/ # Mixed freemium (Hadith premium)
│   │   │   ├── presentation/
│   │   │   │   ├── pages/
│   │   │   │   │   ├── places_list_page.dart
│   │   │   │   │   └── place_detail_page.dart
│   │   │   │   ├── widgets/
│   │   │   │   │   ├── place_card_widget.dart
│   │   │   │   │   ├── reference_widget.dart (PREMIUM)
│   │   │   │   │   └── image_gallery_widget.dart
│   │   │   │   └── bloc/
│   │   │   │       ├── places_list_bloc.dart
│   │   │   │       └── place_detail_bloc.dart
│   │   │   ├── domain/ # Historical places logic and filtering
│   │   │   └── data/   # Places database and premium content
│   │   │
│   │   ├── family_finder/   # Mixed freemium (GPS premium)
│   │   │   ├── presentation/
│   │   │   │   ├── pages/
│   │   │   │   │   ├── family_map_page.dart
│   │   │   │   │   ├── create_group_page.dart
│   │   │   │   │   └── emergency_page.dart
│   │   │   │   ├── widgets/
│   │   │   │   │   ├── family_map_widget.dart
│   │   │   │   │   ├── member_status_widget.dart
│   │   │   │   │   └── qr_scanner_widget.dart
│   │   │   │   └── bloc/
│   │   │   │       ├── family_tracker_bloc.dart
│   │   │   │       └── group_management_bloc.dart
│   │   │   ├── domain/ # Multi-technology tracking logic
│   │   │   └── data/   # GPS, Bluetooth, WiFi integration
│   │   │
│   │   ├── quick_map/       # FREE
│   │   │   ├── presentation/ # Location management UI
│   │   │   ├── domain/      # Saved locations logic
│   │   │   └── data/        # Local storage for locations
│   │   │
│   │   ├── my_itineraries/  # PREMIUM ONLY
│   │   │   ├── presentation/ # Trip planning UI
│   │   │   ├── domain/      # Itinerary optimization logic
│   │   │   └── data/        # Cloud sync and local storage
│   │   │
│   │   └── crowd_insights/  # FREE (Kaggle dataset)
│   │       ├── presentation/ # Crowd data visualization
│   │       ├── domain/      # Data analysis logic
│   │       └── data/        # Kaggle dataset processing
│   │
│   ├── shared/             # Shared UI components
│   │   ├── widgets/        # Reusable UI widgets
│   │   │   ├── islamic_themed/
│   │   │   │   ├── islamic_app_bar.dart
│   │   │   │   ├── islamic_bottom_nav.dart
│   │   │   │   ├── islamic_loading_spinner.dart
│   │   │   │   └── prayer_time_widget.dart
│   │   │   ├── premium/
│   │   │   │   ├── premium_badge_widget.dart
│   │   │   │   ├── upgrade_prompt_widget.dart
│   │   │   │   └── paywall_widget.dart
│   │   │   └── common/
│   │   │       ├── error_display_widget.dart
│   │   │       ├── empty_state_widget.dart
│   │   │       └── confirmation_dialog.dart
│   │   ├── themes/         # App theming
│   │   │   ├── islamic_theme.dart
│   │   │   ├── color_palette.dart
│   │   │   └── typography.dart
│   │   └── constants/      # Shared constants
│   │       ├── app_constants.dart
│   │       ├── islamic_constants.dart
│   │       └── api_endpoints.dart
│   │
│   └── core/              # Core utilities and Islamic helpers
│       ├── islamic/       # Islamic-specific utilities
│       │   ├── prayer_calculations.dart
│       │   ├── hijri_calendar.dart
│       │   ├── qibla_calculator.dart
│       │   └── islamic_validators.dart
│       ├── location/      # Location services
│       │   ├── gps_service.dart
│       │   ├── bluetooth_service.dart
│       │   └── wifi_direct_service.dart
│       ├── premium/       # Premium feature management
│       │   ├── subscription_manager.dart
│       │   ├── feature_gate.dart
│       │   └── premium_validator.dart
│       ├── database/      # Local database
│       │   ├── app_database.dart
│       │   ├── prayer_dao.dart
│       │   ├── family_dao.dart
│       │   └── cache_dao.dart
│       ├── network/       # Network utilities
│       │   ├── api_client.dart
│       │   ├── network_monitor.dart
│       │   └── retry_policy.dart
│       ├── error/         # Error handling
│       │   ├── exceptions.dart
│       │   ├── error_handler.dart
│       │   └── failure.dart
│       └── utils/         # General utilities
│           ├── date_utils.dart
│           ├── validation_utils.dart
│           ├── storage_utils.dart
│           └── performance_utils.dart
├── assets/               # Islamic content and static data
│   ├── images/          # App images and icons
│   │   ├── islamic/     # Islamic-themed imagery
│   │   ├── icons/       # App icons and navigation
│   │   └── historical/  # Historical places images
│   ├── data/           # Static data files
│   │   ├── holy_sites_coordinates.json
│   │   ├── prayer_calculation_constants.json
│   │   ├── hijri_calendar_data.json
│   │   ├── crowd_insights_dataset.csv
│   │   └── historical_places_base.json
│   ├── audio/          # Audio files
│   │   ├── azan/       # Prayer call sounds
│   │   └── duas/       # Islamic supplications
│   └── fonts/          # Custom fonts
│       ├── arabic/     # Arabic script fonts
│       └── latin/      # Latin script fonts
├── test/               # Test files
│   ├── unit/          # Unit tests
│   │   ├── features/  # Feature-specific tests
│   │   └── core/      # Core utility tests
│   ├── integration/   # Integration tests
│   │   ├── islamic/   # Islamic functionality tests
│   │   └── premium/   # Premium feature tests
│   └── widget/        # Widget tests
│       ├── islamic_widgets/
│       └── premium_widgets/
└── pubspec.yaml       # Flutter dependencies and configuration
```

## Firebase Cloud Functions Structure

### Organization Principles
- **Domain-based organization**: Functions grouped by business domain
- **Islamic compliance**: Religious content processing and validation
- **Premium enforcement**: Server-side subscription validation
- **Family safety**: Real-time location and emergency processing

```
apps/cloud_functions/
├── lib/                    # Dart Cloud Functions source
│   ├── sermon_processor/   # YouTube API + search algorithm
│   │   ├── sermon_search.dart      # Sophisticated search algorithm
│   │   ├── audio_extractor.dart    # youtube_explode_dart integration
│   │   ├── caption_processor.dart  # Caption sync and timing
│   │   └── content_validator.dart  # Islamic content validation
│   │
│   ├── premium_validator/  # Subscription validation
│   │   ├── subscription_checker.dart  # Premium status validation
│   │   ├── feature_gate_enforcer.dart # Server-side feature control
│   │   ├── payment_processor.dart     # Subscription updates
│   │   └── trial_manager.dart         # Free trial management
│   │
│   ├── family_safety/     # Real-time location processing
│   │   ├── location_processor.dart    # Family location updates
│   │   ├── emergency_handler.dart     # Emergency notifications
│   │   ├── group_manager.dart         # Family group operations
│   │   └── privacy_controller.dart    # Prayer time location pause
│   │
│   ├── content_aggregator/ # News and weather data
│   │   ├── news_scraper.dart         # Hajj/Umrah news collection
│   │   ├── weather_fetcher.dart      # Holy cities weather
│   │   └── crowd_processor.dart      # Kaggle dataset processing
│   │
│   └── shared/            # Shared function utilities
│       ├── firebase_admin.dart       # Firebase Admin SDK setup
│       ├── auth_middleware.dart      # Authentication validation
│       ├── islamic_validators.dart   # Islamic content validation
│       └── error_handlers.dart       # Centralized error handling
│
├── firebase.json          # Firebase configuration
├── pubspec.yaml          # Dart dependencies
└── README.md             # Function deployment guide
```

## Shared Packages Structure

### Package Organization
- **Domain separation**: Each package has a specific responsibility
- **Version management**: Independent versioning for shared code
- **Cross-platform**: Dart code shared between mobile and functions

```
packages/
├── shared_models/         # Data models (Dart/TypeScript)
│   ├── lib/
│   │   ├── src/
│   │   │   ├── user/
│   │   │   │   ├── user.dart
│   │   │   │   ├── user_preferences.dart
│   │   │   │   └── subscription.dart
│   │   │   ├── pilgrimage/
│   │   │   │   ├── prayer_times.dart
│   │   │   │   ├── ritual_progress.dart
│   │   │   │   └── historical_place.dart
│   │   │   ├── family/
│   │   │   │   ├── family_group.dart
│   │   │   │   ├── location_update.dart
│   │   │   │   └── family_message.dart
│   │   │   └── content/
│   │   │       ├── sermon_content.dart
│   │   │       ├── news_item.dart
│   │   │       └── weather_data.dart
│   │   └── shared_models.dart
│   ├── pubspec.yaml
│   └── README.md
│
├── islamic_utils/         # Islamic calculations and utilities
│   ├── lib/
│   │   ├── src/
│   │   │   ├── prayer_times/
│   │   │   │   ├── calculation_methods.dart
│   │   │   │   ├── prayer_calculator.dart
│   │   │   │   └── qibla_calculator.dart
│   │   │   ├── hijri_calendar/
│   │   │   │   ├── hijri_converter.dart
│   │   │   │   ├── calendar_methods.dart
│   │   │   │   └── islamic_dates.dart
│   │   │   ├── validation/
│   │   │   │   ├── islamic_content_validator.dart
│   │   │   │   ├── cultural_sensitivity_checker.dart
│   │   │   │   └── religious_accuracy_validator.dart
│   │   │   └── constants/
│   │   │       ├── holy_sites_coordinates.dart
│   │   │       ├── islamic_calendar_constants.dart
│   │   │       └── prayer_calculation_constants.dart
│   │   └── islamic_utils.dart
│   ├── pubspec.yaml
│   └── README.md
│
└── premium_core/          # Premium feature management
    ├── lib/
    │   ├── src/
    │   │   ├── subscription/
    │   │   │   ├── subscription_manager.dart
    │   │   │   ├── premium_validator.dart
    │   │   │   └── trial_manager.dart
    │   │   ├── feature_gates/
    │   │   │   ├── feature_gate.dart
    │   │   │   ├── gps_feature_gate.dart
    │   │   │   ├── sermon_feature_gate.dart
    │   │   │   └── itinerary_feature_gate.dart
    │   │   └── analytics/
    │   │       ├── conversion_tracker.dart
    │   │       ├── usage_analytics.dart
    │   │       └── retention_metrics.dart
    │   └── premium_core.dart
    ├── pubspec.yaml
    └── README.md
```

## File Naming Conventions

### General Rules
- **snake_case**: All Dart files use snake_case naming
- **Feature prefix**: Files within features include feature context
- **Layer suffix**: Indicate the architectural layer (page, widget, bloc, etc.)
- **Islamic context**: Include Islamic terminology where appropriate

### Specific Conventions

#### Pages (Screens)
```
{feature}_{page_type}_page.dart
Examples:
- prayer_times_page.dart
- hajj_guide_page.dart
- family_map_page.dart
- sermon_player_page.dart
```

#### Widgets (UI Components)
```
{component}_{widget_type}_widget.dart
Examples:
- prayer_card_widget.dart
- qibla_compass_widget.dart
- family_status_widget.dart
- islamic_loading_widget.dart
```

#### BLoC (State Management)
```
{feature}_{context}_bloc.dart
{feature}_{context}_event.dart
{feature}_{context}_state.dart
Examples:
- prayer_times_bloc.dart
- ritual_counter_bloc.dart
- family_tracker_bloc.dart
```

#### Models (Data Transfer Objects)
```
{entity}_model.dart
Examples:
- prayer_times_model.dart
- family_group_model.dart
- sermon_content_model.dart
- historical_place_model.dart
```

#### Repositories
```
{domain}_repository.dart (abstract)
{domain}_repository_impl.dart (implementation)
Examples:
- prayer_repository.dart
- family_repository.dart
- sermon_repository.dart
```

#### Services
```
{service_type}_service.dart
Examples:
- gps_service.dart
- islamic_calendar_service.dart
- premium_validation_service.dart
- family_safety_service.dart
```

## Development Workflow Integration

### Feature Development Workflow
1. **Create feature folder** following the structure above
2. **Implement domain layer** first (entities, repositories, use cases)
3. **Add data layer** (models, data sources, repository implementations)
4. **Build presentation layer** (pages, widgets, BLoC)
5. **Add tests** for each layer
6. **Update shared components** if needed

### Islamic Compliance Integration
- **Cultural review** required for all user-facing content
- **Prayer time accuracy** validation for location-based features
- **Premium feature validation** for subscription-gated functionality
- **Offline functionality** testing for spiritual continuity

### Code Review Integration
- **Feature completeness** check against architecture
- **Islamic compliance** validation
- **Performance impact** assessment
- **Battery optimization** verification

This source tree structure ensures consistent, maintainable, and culturally-sensitive development of the Ziarah Islamic pilgrimage companion application.