# Loading States & Transitions

## Loading State Design Philosophy

Loading states in ziarah should maintain the spiritual context and provide reassurance during potentially stressful pilgrimage moments. All loading indicators should use Islamic-appropriate design elements and respectful messaging.

### Primary Loading Patterns

```yaml
loading_patterns:
  geometric_spinner:
    visual: Islamic geometric pattern (8-pointed star) rotating
    usage: General app loading, API calls, content refresh
    animation: Smooth rotation, 1.2s duration, continuous
    colors: Primary blue (#1B365D) with gold accent (#D69E2E)
    accessibility: Reduced motion alternative using pulsing opacity

  prayer_time_loading:
    visual: Crescent moon with subtle breathing animation
    usage: Prayer time calculations, location-based updates
    animation: Gentle scale 0.9-1.1, 2s duration, continuous
    text: "Calculating prayer times for your location..."
    timeout: 10 seconds with fallback to manual entry

  gps_signal_acquisition:
    visual: Concentric circles expanding from center point (Kaaba-like)
    usage: GPS lock, location services, family tracking
    animation: Ripple effect, 1.5s intervals, fade out
    text: "Acquiring GPS signal..." / "Connecting to family..."
    progress: Signal strength indicator

  content_streaming:
    visual: Audio waveform with Islamic geometric background
    usage: Friday sermon loading, audio content
    animation: Waveform bars moving, 0.8s cycle
    text: "Loading sermon..." / "Preparing audio..."
    buffer_indicator: Progress bar with Islamic pattern fill

  family_safety_loading:
    visual: Connected dots forming family network pattern
    usage: Family Finder initialization, group creation
    animation: Dots connecting with flowing light paths
    text: "Connecting family members..." / "Creating secure group..."
    safety_emphasis: Emphasis on security and privacy
```

### Screen-Specific Loading States

```yaml
screen_loading_states:
  home_tab_initial_load:
    components:
      news_section:
        skeleton: Card-based skeleton with Islamic pattern placeholders
        timeout: 8 seconds
        fallback: "Latest news unavailable - using cached content"

      weather_widget:
        skeleton: Temperature placeholders with city names
        timeout: 5 seconds
        fallback: Generic weather advice for pilgrimage

      quick_map:
        skeleton: Map outline with loading location pin
        timeout: 10 seconds
        fallback: Manual location selection

      feature_grid:
        skeleton: 2x2 grid with icon placeholders
        timeout: 3 seconds (local data)
        fallback: Basic grid without status indicators

  prayer_times_loading:
    sequence:
      1. Location detection (0-3s): GPS acquiring animation
      2. API call (3-8s): Prayer calculation spinner
      3. Date conversion (8-10s): Hijri calendar loading
    complete_state: All prayer times displayed with next prayer highlighted
    error_fallback: Manual location entry with offline prayer calculations

  family_finder_initialization:
    sequence:
      1. Permission request (0-2s): Location/Bluetooth permission UI
      2. Service startup (2-5s): Multi-technology initialization
      3. Group connection (5-15s): Family member discovery
      4. Map rendering (15-20s): Real-time location display
    privacy_loading: "Securing your family's location data..."
    error_states: Progressive fallback through GPS->WiFi->Bluetooth

  sermon_player_loading:
    sequence:
      1. Search algorithm (0-5s): "Finding sermon..."
      2. Stream extraction (5-15s): "Preparing audio and captions..."
      3. Content buffering (15-25s): "Loading for offline listening..."
    progress_indicators:
      audio_extraction: Percentage with waveform animation
      caption_sync: "Synchronizing text with audio..."
    premium_loading: "Accessing premium sermon library..."

  historical_places_detail:
    sequence:
      1. Basic info (0-1s): Name, location, category
      2. Description (1-3s): Historical content with Islamic references
      3. Media loading (3-8s): Image gallery and additional content
      4. Interactive features (8-12s): Maps, directions, itinerary integration
    skeleton_design: Islamic manuscript-inspired placeholder layouts
    progressive_disclosure: Show content as it loads rather than all-or-nothing
```

### Transition Animations

```yaml
transition_animations:
  tab_navigation:
    animation: Slide transition with Islamic easing curve
    duration: 300ms
    direction: Horizontal for tab switches
    accessibility: Respects reduced motion preferences
    loading_state: Show destination tab skeleton during transition

  modal_presentation:
    premium_upgrade_screen:
      entrance: Scale up from center with gold accent glow
      duration: 400ms
      background: Subtle Islamic geometric pattern fade-in

    family_finder_emergency:
      entrance: Slide up from bottom with urgent animation
      duration: 200ms (faster for emergency)
      visual_priority: Red accent with pulsing attention

    settings_screen:
      entrance: Slide in from right (standard iOS pattern)
      duration: 350ms
      return_transition: Slide out to right with settings save confirmation

  content_refresh:
    pull_to_refresh:
      visual: Crescent moon that completes circle when pulled sufficient distance
      haptic_feedback: Light impact on refresh threshold reached
      animation: Smooth rotation during refresh process
      success: Green checkmark with "Updated" message

    auto_refresh:
      prayer_times: Subtle pulse on time updates every minute
      family_locations: Smooth position updates without jarring movement
      sermon_progress: Smooth caption highlighting without text jumping

  loading_to_content:
    skeleton_to_real:
      animation: Fade transition from skeleton to actual content
      duration: 200ms
      stagger: Different content sections appear with 50ms delays
      smooth_reveal: Content slides in from skeleton positions

    error_to_retry:
      animation: Error state slides out, loading state slides in
      duration: 300ms
      feedback: Subtle haptic feedback on retry button press
      hope_messaging: "Trying again..." with patient tone
```

### Progress Indicators

```yaml
progress_indicators:
  determinate_progress:
    sermon_download:
      visual: Circular progress with Islamic geometric pattern
      text: "Downloading sermon: 45% (2.3MB of 5.1MB)"
      time_estimate: "About 30 seconds remaining"
      offline_benefit: "Available for offline listening"

    itinerary_sync:
      visual: Linear progress bar with mosque milestone markers
      text: "Syncing itinerary: 3 of 7 places updated"
      step_indication: Clear indication of current step
      completion: Success animation with spiritual messaging

  indeterminate_progress:
    family_search:
      visual: Pulsing family network diagram
      text: "Searching for family members nearby..."
      timeout: 30 seconds with manual search option
      context: "This may take longer in crowded areas"

    authentication:
      visual: Secure connection animation with Islamic pattern
      text: "Signing you in securely..."
      privacy: "Your data is protected with encryption"
      timeout: 15 seconds with retry option

  micro_interactions:
    button_press_feedback:
      visual: Subtle scale down (0.95) with gold accent flash
      duration: 150ms
      haptic: Light impact for confirmation
      islamic_context: Respectful interaction without distraction

    successful_action:
      visual: Green checkmark with gentle expansion animation
      duration: 800ms
      text: Action-specific success message
      spiritual_touch: "Barakallahu feeki" (May Allah bless you) for major completions
```

## Performance Optimization

```yaml
loading_performance:
  skeleton_screens:
    implementation: Show immediate skeleton on screen entry
    benefit: Perceived performance improvement
    design: Islamic-themed placeholder content
    accessibility: Screen reader announces "Loading content"

  progressive_loading:
    priority_content: Prayer times and family safety features load first
    secondary_content: Historical places and sermons load after core features
    background_loading: Non-critical content loads in background
    cache_strategy: Prioritize frequently accessed content

  offline_first:
    cached_content: Show cached content immediately with refresh option
    update_strategy: Background updates with subtle notification
    offline_indicators: Clear indication when content is cached vs live
    graceful_degradation: Full functionality with cached data

  battery_consideration:
    efficient_animations: Optimize animations for battery life during long pilgrimage days
    loading_timeouts: Reasonable timeouts to prevent battery drain
    background_limits: Minimal background loading during low battery
    user_control: Options to reduce animations for battery saving
```

## Cultural Sensitivity in Loading States

**Islamic Design Elements:**
- Use geometric patterns instead of representational imagery
- Incorporate crescent moon and star motifs respectfully
- Apply gold and blue color schemes reminiscent of Islamic architecture
- Include Arabic calligraphy-inspired flourishes (non-textual)

**Messaging Guidelines:**
- Use patient, respectful language acknowledging the sacred context
- Include Islamic concepts like Sabr (patience) when appropriate
- Avoid anxiety-inducing urgency during spiritual activities
- Provide spiritual perspective on waiting and delays

**Examples of Culturally Appropriate Loading Messages:**
- "Preparing your spiritual companion..." (app startup)
- "Connecting you to the sacred..." (location/prayer services)
- "Gathering your family safely..." (family finder)
- "Accessing blessed content..." (sermon loading)
- "Your patience is rewarded..." (long loading processes)
