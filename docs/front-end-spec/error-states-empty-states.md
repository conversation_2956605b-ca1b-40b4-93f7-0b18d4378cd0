# Error States & Empty States

## Error State Management

Error states in ziarah must be handled with particular sensitivity, as users depend on the app during critical pilgrimage moments. All errors should provide clear guidance and alternative solutions.

### Network Connection Errors

```yaml
network_error_states:
  no_internet:
    visual: 📡❌ icon with Islamic geometric pattern background
    headline: "No Internet Connection"
    description: "Using offline features. Prayer times and manual counters are still available."
    actions:
      primary: "Try Again"
      secondary: "Use Offline Mode"
    offline_indicators: show_cached_data_timestamps

  api_timeout:
    visual: ⏱️ with loading spinner that stopped
    headline: "Service Temporarily Unavailable"
    description: "Our servers are busy. Your data is safe and we're working to restore service."
    actions:
      primary: "Retry"
      secondary: "Use Cached Data"
    islamic_messaging: "Sabr (patience) is a virtue"

  sermon_streaming_failed:
    visual: 🕌 with audio wave that stopped
    headline: "Sermon Unavailable"
    description: "The requested sermon couldn't be loaded. Try a different date or language."
    actions:
      primary: "Try Different Date"
      secondary: "Browse Available Sermons"
      tertiary: "Use Downloaded Content"
```

### GPS and Location Errors

```yaml
location_error_states:
  gps_unavailable:
    visual: 📍❌ with compass-like Islamic geometric design
    headline: "GPS Signal Not Available"
    description: "Manual counters are ready to use while we search for a signal."
    actions:
      primary: "Switch to Manual Counter"
      secondary: "Keep Trying GPS"
    context: "You're in a sacred space - manual counting is equally blessed"

  location_permission_denied:
    visual: 🔒📍 with respectful privacy-themed design
    headline: "Location Permission Needed"
    description: "We need location access for prayer times and family safety features."
    actions:
      primary: "Enable Location"
      secondary: "Set Location Manually"
    privacy_assurance: "Your location is encrypted and never shared"

  gps_accuracy_poor:
    visual: 📶 with 1-2 bars and Islamic pattern
    headline: "GPS Accuracy Limited"
    description: "Signal is weak in this area. Manual override recommended."
    actions:
      primary: "Use Manual Counter"
      secondary: "Continue with GPS"
    guidance: "Manual counting allows better spiritual focus"
```

### Family Safety Errors

```yaml
family_safety_errors:
  family_member_offline:
    visual: 👤⭕ with last known location indicator
    headline: "Family Member Offline"
    description: "Ahmed was last seen 15 minutes ago near Grand Mosque entrance."
    actions:
      primary: "Send Emergency Message"
      secondary: "Call Emergency Contact"
      tertiary: "Get Directions to Last Location"
    emergency_escalation: "Automatic emergency protocol in 30 minutes"

  group_creation_failed:
    visual: 👨‍👩‍👧‍👦❌ with QR code broken
    headline: "Group Creation Failed"
    description: "Couldn't create family group. Check internet connection."
    actions:
      primary: "Try Again"
      secondary: "Create Manual Group"
    alternative: "Use Bluetooth-only tracking until online"

  bluetooth_unavailable:
    visual: 📶❌ with Bluetooth symbol
    headline: "Bluetooth Not Available"
    description: "Family tracking limited to GPS only."
    actions:
      primary: "Enable Bluetooth"
      secondary: "Continue with GPS Only"
    impact: "Close-range family detection disabled"
```

### Authentication and Premium Errors

```yaml
auth_premium_errors:
  subscription_expired:
    visual: 💳⏰ with respectful gold accent
    headline: "Premium Subscription Expired"
    description: "Your premium features are no longer available."
    actions:
      primary: "Renew Subscription"
      secondary: "Continue with Basic Features"
    grace_period: "Premium features available for 3 more days"

  payment_failed:
    visual: 💳❌ with secure payment icons
    headline: "Payment Could Not Be Processed"
    description: "Please update your payment method to continue premium access."
    actions:
      primary: "Update Payment Method"
      secondary: "Try Different Payment"
    support: "Contact support if issues persist"

  account_sync_failed:
    visual: 🔄❌ with cloud sync icon
    headline: "Sync Failed"
    description: "Your settings couldn't be synced across devices."
    actions:
      primary: "Try Sync Again"
      secondary: "Use Local Settings"
    reassurance: "All data is safe on this device"
```

## Empty State Design

Empty states should encourage engagement while maintaining the app's spiritual context.

### Empty Content States

```yaml
empty_states:
  no_family_groups:
    visual: 👨‍👩‍👧‍👦➕ with Islamic family illustration
    headline: "No Family Groups Yet"
    description: "Create a group to keep your family safe during pilgrimage."
    actions:
      primary: "Create Family Group"
      secondary: "Join Existing Group"
    benefit: "Stay connected in crowded holy spaces"

  no_saved_locations:
    visual: 📍➕ with map and Islamic geometric points
    headline: "No Saved Locations"
    description: "Save important places like your hotel or meeting points."
    actions:
      primary: "Add Current Location"
      secondary: "Search for Places"
    suggestions: "Popular: Hotel, Gate 1, Prayer Area"

  no_itinerary_items:
    visual: 📋✨ with historical places icons
    headline: "Your Itinerary is Empty"
    description: "Add historical places to plan your spiritual journey."
    actions:
      primary: "Explore Historical Places"
      secondary: "Create Custom Itinerary"
    inspiration: "Discover places from the Prophet's (PBUH) time"

  no_downloaded_sermons:
    visual: 🕌📱 with download arrow
    headline: "No Offline Sermons"
    description: "Download sermons for offline listening during pilgrimage."
    actions:
      primary: "Download Recent Sermons"
      secondary: "Browse Sermon Library"
    premium_note: "Premium feature - Upgrade to access"

  search_no_results:
    visual: 🔍❌ with respectful "not found" design
    headline: "No Results Found"
    description: "Try different keywords or check spelling."
    actions:
      primary: "Clear Search"
      secondary: "Browse All Items"
    suggestions: "Popular searches: Kaaba, Prophet's Mosque, Safa"

  first_time_prayer_times:
    visual: 🕐✨ with prayer times layout preview
    headline: "Welcome to Prayer Times"
    description: "Your personalized prayer schedule is ready."
    actions:
      primary: "View Today's Times"
      secondary: "Customize Notifications"
    location_note: "Based on your location: {city}"
```

### Offline Mode Empty States

```yaml
offline_empty_states:
  sermon_offline:
    visual: 🕌📱❌ with offline indicator
    headline: "No Offline Sermons Available"
    description: "Connect to internet to download sermons for offline listening."
    actions:
      primary: "Connect to WiFi"
      secondary: "Use Manual Prayer Guide"
    offline_alternatives: "Prayer guides and counters still available"

  news_offline:
    visual: 📰❌ with last updated timestamp
    headline: "Latest News Unavailable"
    description: "Last updated 2 hours ago. Connect to see latest updates."
    actions:
      primary: "Try Connection"
      secondary: "View Cached Articles"
    cached_count: "5 articles available offline"

  historical_places_offline:
    visual: 🏛️📱 with basic info icon
    headline: "Limited Information Available"
    description: "Full place details require internet connection."
    actions:
      primary: "Connect for Full Details"
      secondary: "View Basic Information"
    available_offline: "Name, location, and basic description available"
```

## Error Message Tone and Language

**Islamic Context Considerations:**
- Use respectful, patient language reflecting Islamic values
- Include concept of Sabr (patience) during technical difficulties
- Acknowledge that difficulties are part of the journey
- Provide spiritual perspective on challenges

**Examples of Appropriate Error Messaging:**
- "Sabr (patience) is rewarded - we're working to restore service"
- "Even in difficulties, your spiritual journey continues"
- "Manual methods are equally blessed when technology fails"
- "Your safety remains our priority while we reconnect"

**Avoid:**
- Frustrating or blame-oriented language
- Technical jargon that may confuse users
- Urgent language that creates anxiety during sacred activities
- Messages that interrupt spiritual focus unnecessarily
