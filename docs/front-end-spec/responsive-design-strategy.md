# Responsive Design Strategy

## Device Size Considerations

**Target Android Devices** (API 21+ per PRD)
- **Compact**: 5.0" screens (1080x1920) - Minimum viable size
- **Standard**: 5.5"-6.1" screens (1080x2340) - Primary target
- **Large**: 6.2"-6.7" screens (1440x3200) - Premium experience
- **Foldable**: Future consideration for expanding market

**Adaptive Layout Principles**
- **Bottom Navigation**: Fixed height across all screen sizes (72dp)
- **Content Areas**: Flexible with minimum touch target maintenance
- **Typography**: Responsive scaling while maintaining readability
- **Spacing**: Consistent 16dp base unit with proportional scaling

## Orientation Handling

**Portrait Primary** (Pilgrimage Context)
- All core features optimized for portrait usage
- Landscape support for map views and sermon playback
- Orientation lock options for ritual counter usage

**Critical Feature Considerations**
- **GPS Counters**: Portrait-only to prevent accidental rotation during rituals
- **Family Map**: Landscape support for better spatial awareness
- **Sermon Player**: Landscape support for caption readability
- **Prayer Times**: Portrait primary with landscape adaptation
