# Component Library & Design System

## Core Component Requirements

Based on PRD technical requirements and Epic specifications:

### Islamic-Appropriate Design Language

**Color Palette** (Reverent, Sacred Space Appropriate)
- **Primary**: Deep Blue (#1B365D) - Reminiscent of holy mosque architecture
- **Secondary**: Gold Accent (#D69E2E) - Islamic geometric patterns
- **Sacred**: White (#FFFFFF) - Purity, spiritual focus
- **Success**: Green (#38A169) - Completion, Islamic symbolism
- **Warning**: Orange (#DD6B20) - Caution, crowd alerts
- **Error**: Red (#E53E3E) - Emergency, safety alerts
- **Neutral Grays**: (#2D3748, #4A5568, #718096) - Text, borders, backgrounds

**Typography System** (Multi-Script Support)
- **Primary Font**: Roboto/System Default for Latin scripts
- **Arabic Support**: Noto Sans Arabic for Arabic text and numerals
- **Sizing Scale**: 12sp, 14sp, 16sp, 18sp, 20sp, 24sp, 28sp, 32sp
- **Weight Scale**: Regular (400), Medium (500), Semi-Bold (600), Bold (700)
- **Line Height**: 1.5x for readability in multiple languages

### Core UI Components

**1. Navigation Components**

**Bottom Tab Navigation**
```yaml
component: BottomTabNavigation
requirements:
  - 5 tabs: Home, Prayer Times, Knowledge Hub, Friday Sermon, Historical Places
  - Always visible, fixed positioning
  - Tab badges for notifications (prayer alerts, family emergencies)
  - Islamic iconography with text labels
  - Touch targets: minimum 44dp height for accessibility
  - Active state: Primary color background + icon color change
  - Premium indicators: Gold accent for premium-only tabs
```

**Feature Grid (2x2 Home Layout)**
```yaml
component: FeatureGrid
requirements:
  - Consistent 2x2 layout with equal spacing
  - Large touch targets (minimum 88dp) for crowded conditions
  - Icon + text label format for clear communication
  - Status indicators (active family groups, current itinerary)
  - Premium differentiation with visual indicators
  - Accessibility: high contrast, screen reader support
```

**2. Pilgrimage-Specific Components**

**GPS Ritual Counter Interface**
```yaml
component: GPSRitualCounter
requirements:
  - Large, prominent count display (32sp minimum)
  - GPS accuracy status indicator (visual + text)
  - Manual override button always visible
  - Circuit/round progress visualization (circular progress bar)
  - Completion celebration animation (Islamic geometric patterns)
  - Battery status indicator during extended usage
  - Offline functionality with cached progress
```

**Prayer Times Widget**
```yaml
component: PrayerTimesWidget
requirements:
  - Next prayer countdown with visual progress
  - All 5 daily prayers with times in large, readable format
  - Hijri/Gregorian date display with conversion
  - Qibla direction indicator with compass
  - Notification settings quick access
  - Arabic/local numeral support
  - Accessibility: voice feedback for times
```

**Family Safety Map Component**
```yaml
component: FamilyMap
requirements:
  - Real-time family member location indicators
  - User identification (names, photos, custom icons)
  - Location accuracy indicators (GPS/WiFi/Bluetooth status)
  - Last known location display when current unavailable
  - Emergency alert visual indicators (red pulsing)
  - Privacy controls (location sharing on/off)
  - Offline map with cached family locations
```

**3. Content Display Components**

**Sermon Player Interface**
```yaml
component: SermonPlayer
requirements:
  - Audio-first design with large play/pause controls (88dp minimum)
  - Background playback with persistent notification controls
  - Caption overlay with adjustable text size (14sp-24sp range)
  - Synchronized caption highlighting with audio position
  - Language selection with flag indicators
  - Caption search functionality within transcript
  - Buffering indicators and offline capability status
```

**Historical Place Card**
```yaml
component: HistoricalPlaceCard
requirements:
  - Place name, category, and historical period display
  - GPS coordinates and distance from current location
  - Hadith/Quran reference indicators
  - "Add to Itinerary" and "Get Directions" action buttons
  - Image gallery support with historical photos
  - Expandable description with respectful Islamic content
  - Premium feature indicators for extended content
```

## Accessibility Requirements (WCAG AA Compliance)

**Visual Accessibility**
- **Color Contrast**: Minimum 4.5:1 for normal text, 3:1 for large text
- **Text Scaling**: Support up to 200% text size increase
- **Focus Indicators**: Clear visual focus for keyboard/switch navigation
- **Color Independence**: Information not conveyed by color alone

**Motor Accessibility**
- **Touch Targets**: Minimum 44dp with 8dp spacing between interactive elements
- **Large Button Mode**: 88dp touch targets for elderly users and crowded conditions
- **Gesture Alternatives**: Tap alternatives for complex gestures
- **Timeout Extensions**: Extended timeouts for users who need more time

**Cognitive Accessibility**
- **Simple Navigation**: Consistent navigation patterns throughout app
- **Clear Language**: Simple, respectful language avoiding technical jargon
- **Error Prevention**: Confirmation dialogs for destructive actions
- **Help Integration**: Contextual help and tutorials for complex features

## Premium vs Free Visual Differentiation

**Free Tier Visual Indicators**
- Grayed-out premium features with "Upgrade" labels
- Feature cards with subtle premium indicators (gold corner badge)
- Limited functionality hints within free features

**Premium Tier Visual Enhancements**
- Gold accent colors for premium-enabled features
- Enhanced visual feedback and animations
- Advanced progress indicators and detailed statistics
- Exclusive premium content badges and visual elements

## Premium Feature Comparison Screen (Paywall)

**Component: PremiumUpgradeScreen**

This critical conversion screen appears when free users attempt to access premium features or from the upgrade prompts throughout the app.

```yaml
component: PremiumUpgradeScreen
trigger_points:
  - GPS-guided ritual counter access attempt
  - Friday Sermon tab access attempt
  - Advanced Family Finder features (Google Maps integration)
  - MyItineraries creation attempt
  - Advanced Historical Places features

layout_structure:
  header:
    - App logo with premium gold accent
    - "Unlock Your Complete Pilgrimage Experience" headline
    - "$10/year - Less than $1 per month" pricing emphasis

  feature_comparison:
    format: side-by-side_comparison_table
    visual_style: Islamic_geometric_divider_lines

    comparison_categories:
      - family_safety: "👨‍👩‍👧‍👦 Family Safety & Coordination"
      - spiritual_guidance: "🕋 GPS-Guided Spiritual Assistance"
      - authentic_content: "🕌 Authentic Islamic Content"
      - trip_planning: "📋 Advanced Trip Planning"

  call_to_action:
    primary_button: "Start Your Sacred Journey - $10/year"
    secondary_button: "Continue with Basic Features"
    trust_indicators: "✓ 30-day money back guarantee"

feature_comparison_table:
  family_safety:
    free: "Basic Bluetooth tracking only"
    premium: "✓ GPS tracking with Google Maps\n✓ Preset emergency messages\n✓ Last known location storage"

  spiritual_guidance:
    free: "Manual Tawaf & Sa'i counters"
    premium: "✓ GPS-guided automatic counting\n✓ Circuit completion detection\n✓ Spiritual focus without distraction"

  authentic_content:
    free: "❌ No Friday Sermon access"
    premium: "✓ Live & archived sermons from holy mosques\n✓ 6 languages with synchronized captions\n✓ Audio + text search functionality"

  trip_planning:
    free: "Basic historical place information"
    premium: "✓ Create & manage custom itineraries\n✓ Verified Hadith & Quran references\n✓ Optimal timing with crowd insights"

visual_design_requirements:
  color_scheme:
    - Premium features: Gold accent (#D69E2E) backgrounds
    - Free features: Neutral gray (#718096) with limitations text
    - CTA button: Primary blue (#1B365D) with gold border

  islamic_visual_elements:
    - Subtle geometric patterns as section dividers
    - Respectful iconography (no human/animal imagery)
    - Mosque architecture-inspired borders

  accessibility:
    - High contrast for readability
    - Large touch targets (minimum 44dp)
    - Screen reader compatibility
    - Text scaling support up to 200%

conversion_optimization:
  value_propositions:
    - "Focus on your spiritual journey, not manual counting"
    - "Keep your family safe in crowded holy spaces"
    - "Access authentic content directly from Mecca & Medina"
    - "Plan your pilgrimage like never before"

  social_proof:
    - "Join thousands of pilgrims who've enhanced their journey"
    - "Trusted by families worldwide for pilgrimage safety"

  urgency_elements:
    - "Limited time: Start your pilgrimage with confidence"
    - "Don't let manual counting distract from your spiritual focus"

emotional_triggers:
  - Spiritual enhancement: "Deepen your connection during Tawaf"
  - Family safety: "Never lose track of loved ones again"
  - Authenticity: "Access the same sermons heard in the holy mosques"
  - Convenience: "Everything you need in one blessed app"
```

**Screen Flow Integration:**
- Appears as full-screen modal with Islamic-appropriate backdrop
- Can be dismissed but returns strategically when premium features accessed
- Post-subscription success screen with gratitude message and feature activation
- Smooth transition back to the premium feature that triggered the upgrade

## App Onboarding Flow (Welcome Screens)

**Component: OnboardingFlow**

The first-time user experience introducing ziarah's core value propositions through swipeable screens, designed to build excitement and establish the app's spiritual context.

```yaml
component: OnboardingFlow
trigger_condition: first_app_launch
total_screens: 5
navigation: horizontal_swipe_with_skip_option

screen_structure:
  - Welcome screen (brand introduction)
  - GPS Spiritual Assistance (core value prop)
  - Family Safety System (safety value prop)
  - Authentic Islamic Content (content value prop)
  - Get Started (final CTA with account creation)

screen_1_welcome:
  visual:
    background: gradient_blue_to_white_islamic_pattern
    illustration: stylized_kaaba_silhouette_with_geometric_patterns
    no_human_animal_imagery: true

  content:
    headline: "Welcome to Your Sacred Journey"
    subheadline: "ziarah - Your complete pilgrimage companion"
    description: "Designed specifically for Hajj and Umrah pilgrims, combining spiritual guidance with modern technology"

  ui_elements:
    skip_button: "Skip" (top right, subtle)
    next_button: "Begin Journey" (primary CTA)
    page_indicators: dots_with_islamic_geometric_styling

screen_2_gps_assistance:
  visual:
    background: deep_blue_with_gold_accents
    illustration: circular_progress_indicator_with_kaaba_center
    animation: subtle_rotation_suggesting_tawaf_movement

  content:
    headline: "GPS-Guided Spiritual Focus"
    key_benefit: "Never lose count during Tawaf or Sa'i again"
    description: "Automatic circuit counting lets you focus entirely on your spiritual connection"
    premium_hint: "Premium feature - Free trial available"

  ui_elements:
    try_now_button: "Try GPS Counter" (leads to premium trial)
    next_button: "Continue"

screen_3_family_safety:
  visual:
    background: warm_gradient_with_family_iconography
    illustration: connected_dots_representing_family_network
    color_scheme: safety_greens_and_blues

  content:
    headline: "Keep Your Family Safe & Connected"
    key_benefit: "Multi-technology tracking in crowded holy spaces"
    description: "GPS, WiFi Direct, and Bluetooth ensure you never lose track of loved ones"
    features_list:
      - "Real-time location sharing"
      - "Emergency preset messages"
      - "Works offline when needed"

  ui_elements:
    demo_button: "See Family Finder" (interactive demo)
    next_button: "Continue"

screen_4_authentic_content:
  visual:
    background: mosque_architecture_inspired_gradient
    illustration: audio_waveform_with_islamic_calligraphy_elements
    color_scheme: gold_and_deep_blue

  content:
    headline: "Authentic Islamic Content"
    key_benefit: "Direct access to sermons from holy mosques"
    description: "Live and archived Friday sermons from Mecca and Medina in 6 languages with synchronized captions"
    premium_highlight: "Premium exclusive feature"

  ui_elements:
    preview_button: "Preview Sermon" (audio sample)
    next_button: "Almost Done"

screen_5_get_started:
  visual:
    background: celebratory_gold_and_white_pattern
    illustration: app_interface_mockup_showing_home_tab
    style: clean_modern_islamic_aesthetic

  content:
    headline: "Your Pilgrimage Companion Awaits"
    description: "Join thousands of pilgrims who've enhanced their spiritual journey with ziarah"
    value_reminder: "Prayer times, GPS guidance, family safety, and authentic content - all in one app"

  ui_elements:
    primary_cta: "Create Your Account"
    secondary_cta: "Continue as Guest"
    trust_indicators: "✓ 100% Halal ✓ Privacy Protected ✓ Scholar Approved"

interaction_design:
  swipe_behavior:
    - Horizontal swipe between screens
    - Smooth transitions with Islamic-inspired easing
    - Automatic progression option (5 seconds per screen)
    - Swipe indicators show progress with geometric patterns

  skip_functionality:
    - "Skip" button available on all screens except final
    - Skip leads directly to account creation screen
    - Skip tracking for onboarding optimization

  accessibility:
    - Voice-over support for screen reader users
    - High contrast mode compatibility
    - Large touch targets (minimum 44dp)
    - Alternative text for all visual elements

premium_conversion_strategy:
  trial_offers:
    - GPS counter: "Try 3 free GPS-guided circuits"
    - Family Finder: "Create one free family group"
    - Sermons: "Listen to one complete sermon with captions"

  conversion_triggers:
    - Trial expiration notifications
    - Feature limitation reminders during peak usage
    - Special onboarding pricing (if applicable)

cultural_sensitivity:
  visual_guidelines:
    - No human or animal representations
    - Islamic geometric patterns and calligraphy-inspired elements
    - Colors evocative of holy mosque architecture
    - Respectful imagery focusing on spiritual themes

  language_considerations:
    - Simple, respectful language
    - Arabic terms properly transliterated
    - Cultural context appropriate for global Muslim audience
    - Avoid assumptions about pilgrimage experience level

analytics_tracking:
  onboarding_metrics:
    - Completion rate per screen
    - Skip rate analysis
    - Time spent on each screen
    - CTA engagement rates
    - Trial-to-premium conversion from onboarding

  optimization_goals:
    - >80% complete onboarding flow
    - >15% trial signup rate from onboarding
    - >25% premium conversion from onboarding trials
```

**Implementation Notes:**
- Onboarding appears only on first app launch (stored in local preferences)
- Can be manually accessed later through Settings > "App Introduction"
- Integrates with analytics for conversion optimization
- Respects user preference to skip and doesn't re-appear unless explicitly requested

## Settings Menu Screen

**Component: SettingsScreen**

Comprehensive settings menu accessible from user profile/menu, allowing users to configure location, content preferences, notifications, and app behavior after initial setup.

```yaml
component: SettingsScreen
access_points:
  - User profile menu
  - Home tab menu button
  - Bottom navigation overflow menu
  - Quick settings from notification panel

screen_layout:
  navigation: grouped_list_with_icons_and_descriptions
  search: settings_search_functionality
  visual_style: islamic_themed_with_geometric_dividers

settings_groups:
  location_and_prayer:
    icon: "📍"
    title: "Location & Prayer Times"
    items:
      - current_location:
          label: "Current Location"
          value: "{city}, {country}"
          action: location_selection_screen
          description: "Affects prayer times and family finder"

      - prayer_calculation:
          label: "Prayer Calculation Method"
          value: "{method_name}"
          action: prayer_method_selection
          description: "Choose calculation used by your local mosque"

      - madhab_selection:
          label: "Islamic School (Madhab)"
          value: "{madhab}"
          options: ["Hanafi", "Shafi/Maliki/Hanbali"]
          description: "Affects Asr prayer calculation"

      - hijri_calendar:
          label: "Hijri Calendar Method"
          value: "{hijri_method}"
          options: ["Umm al-Qura", "ISNA", "Muslim World League"]

  notifications:
    icon: "🔔"
    title: "Notifications"
    items:
      - prayer_notifications:
          label: "Prayer Time Notifications"
          toggle: enabled_by_default
          sub_settings:
            - timing: ["At time", "5 min before", "10 min before", "15 min before"]
            - prayers: individual_prayer_toggles
            - azan_sound: ["Traditional Mecca", "Traditional Medina", "Soft", "Silent"]

      - family_safety:
          label: "Family Safety Alerts"
          toggle: enabled_by_default
          description: "High priority notifications that bypass Do Not Disturb"

      - sermon_reminders:
          label: "Friday Sermon Reminders"
          toggle: disabled_by_default
          premium_required: true
          frequency: ["Weekly", "Monthly", "Never"]

  language_and_content:
    icon: "🌐"
    title: "Language & Content"
    items:
      - interface_language:
          label: "App Language"
          value: "{current_language}"
          options: ["English", "العربية (Arabic)"]

      - sermon_languages:
          label: "Sermon Languages"
          premium_required: true
          multi_select: true
          options:
            - "🇺🇸 English"
            - "🇲🇾 Bahasa Melayu"
            - "🇮🇩 Bahasa Indonesia"
            - "🇹🇷 Türkçe"
            - "🇮🇷 فارسی (Persian)"
            - "🇵🇰 اردو (Urdu)"

      - numeral_system:
          label: "Number Display"
          options: ["Western (1,2,3)", "Arabic-Indic (١,٢,٣)"]

  premium_and_account:
    icon: "👤"
    title: "Account & Subscription"
    items:
      - subscription_status:
          label: "Premium Subscription"
          value: "{subscription_status}"
          action: premium_management_screen
          upgrade_button: visible_for_free_users

      - account_info:
          label: "Account Information"
          value: "{email}"
          action: account_details_screen

      - data_sync:
          label: "Sync Preferences"
          description: "Sync settings across devices"
          toggle: enabled_by_default

  family_and_safety:
    icon: "👨‍👩‍👧‍👦"
    title: "Family & Safety"
    items:
      - family_groups:
          label: "My Family Groups"
          value: "{active_groups_count} active"
          action: family_groups_management

      - location_sharing:
          label: "Location Sharing"
          toggle: user_controlled
          description: "Share location with family members"

      - emergency_contacts:
          label: "Emergency Contacts"
          action: emergency_contacts_screen
          description: "Contacts for emergency situations"

  app_preferences:
    icon: "⚙️"
    title: "App Preferences"
    items:
      - theme:
          label: "App Theme"
          options: ["System Default", "Light", "Dark"]
          description: "Visual appearance of the app"

      - battery_optimization:
          label: "Battery Optimization"
          action: battery_settings_guide
          description: "Optimize for 12+ hour pilgrimage usage"

      - data_usage:
          label: "Data Usage"
          sub_settings:
            - offline_mode: toggle
            - auto_download: ["WiFi only", "WiFi + Mobile", "Never"]
            - cache_size: slider_control

  help_and_support:
    icon: "❓"
    title: "Help & Support"
    items:
      - app_tutorial:
          label: "App Tutorial"
          action: replay_onboarding_flow
          description: "Replay the welcome screens"

      - help_center:
          label: "Help Center"
          action: help_documentation
          description: "Guides and frequently asked questions"

      - contact_support:
          label: "Contact Support"
          action: support_contact_options
          description: "Get help with technical issues"

      - feedback:
          label: "Send Feedback"
          action: feedback_form
          description: "Help us improve ziarah"

  privacy_and_legal:
    icon: "🔒"
    title: "Privacy & Legal"
    items:
      - privacy_settings:
          label: "Privacy Settings"
          action: privacy_controls_screen
          description: "Control data collection and sharing"

      - data_export:
          label: "Export My Data"
          action: data_export_screen
          description: "Download your data (GDPR compliance)"

      - privacy_policy:
          label: "Privacy Policy"
          action: privacy_policy_document

      - terms_of_service:
          label: "Terms of Service"
          action: terms_document

  about:
    icon: "ℹ️"
    title: "About"
    items:
      - app_version:
          label: "App Version"
          value: "{version} ({build})"
          action: version_details

      - islamic_compliance:
          label: "Islamic Compliance"
          description: "Content verified by Islamic scholars"
          action: compliance_information

      - credits:
          label: "Credits & Acknowledgments"
          action: credits_screen

interaction_design:
  search_functionality:
    placeholder: "Search settings..."
    searchable_fields: [labels, descriptions, group_names]
    instant_results: highlight_matching_settings

  premium_differentiation:
    visual_indicators: gold_accent_for_premium_settings
    upgrade_prompts: contextual_upgrade_buttons
    feature_locks: clear_premium_required_messaging

  accessibility:
    screen_reader: full_voiceover_support
    text_scaling: supports_up_to_200_percent
    high_contrast: compatible_with_system_settings
    keyboard_navigation: full_keyboard_accessibility

error_handling:
  network_issues:
    offline_mode: settings_cached_and_sync_when_connected
    api_failures: graceful_degradation_with_retry_options

  permission_conflicts:
    location_denied: clear_explanation_and_manual_alternatives
    notification_blocked: guide_to_system_settings

  data_corruption:
    settings_reset: backup_and_restore_functionality
    factory_reset: complete_app_data_reset_option
```

**Navigation Integration:**
- Accessible from profile menu in any tab
- Settings search for quick access to specific preferences
- Deep linking support for direct access to specific setting screens
- Contextual settings access from relevant features (e.g., notification settings from Prayer Times tab)
