# Information Architecture (IA)

## Primary Navigation Structure (5-Tab Bottom Navigation)

Based on PRD functional requirements and Epic structure:

```mermaid
graph TD
    A[Bottom Navigation - Always Visible] --> B[🏠 Home]
    A --> C[🕐 Prayer Times]
    A --> D[📚 Knowledge Hub]
    A --> E[🕌 Friday Sermon]
    A --> F[🏛️ Historical Places]

    %% Home Tab (Epic 6)
    B --> B1[📰 News Feed - External Links]
    B --> B2[🌤️ Weather - Mecca/Medina]
    B --> B3[🗺️ Quick Map Preview - Selected Location]
    B --> B4[2x2 Feature Grid]

    B4 --> B4A[🗺️ Quick Map - Location Management]
    B4 --> B4B[👨‍👩‍👧‍👦 Family Finder - Safety System]
    B4 --> B4C[📋 MyItineraries - Trip Planning]
    B4 --> B4D[👥 Crowd Insights - Optimal Timing]

    %% Prayer Times Tab (Epic 1)
    C --> C1[🕐 Daily Prayer Times - Al Adhan API]
    C --> C2[🔔 Azan Notifications - Customizable]
    C --> C3[📅 Gregorian/Hijri Converter - Date System]
    C --> C4[🧭 Qibla Direction - Compass]

    %% Knowledge Hub Tab (Epic 2)
    D --> D1[🕋 Hajj Guide + Tracker - Complete Ritual]
    D --> D2[🕋 Umrah Guide + Tracker - Focused Journey]
    D --> D3[📱 MyIbadah Tools - Ritual Assistance]

    D3 --> D3A[Manual Tawaf Counter - 7 Circuits]
    D3 --> D3B[Manual Sa'i Counter - 7 Rounds]
    D3 --> D3C[GPS Tawaf Counter - Premium Feature]
    D3 --> D3D[GPS Sa'i Counter - Premium Feature]

    %% Friday Sermon Tab (Epic 3) - Premium Only
    E --> E1[📡 Live Sermon - Current Friday]
    E --> E2[📻 Last Friday Sermon - Previous Week]
    E --> E3[🌍 Location Selection - Mecca/Medina]
    E --> E4[🗣️ Language Selection - 6 Languages]
    E --> E5[🎵 Audio Player - just_audio Integration]
    E --> E6[📝 Synchronized Captions - Real-time Text]

    %% Historical Places Tab (Epic 4)
    F --> F1[🔍 Search & Filter - Category/Period]
    F --> F2[📍 Place Categories - Mosque/Battlefield/Cave]
    F --> F3[⏳ Historical Periods - Prophet/Caliphate]
    F --> F4[📖 Place Details - GPS/References]
    F --> F5[📋 Itinerary Integration - Add Button]
```

## Navigation Patterns & Hierarchy

**Primary Navigation (Bottom Tabs)**
- **Always visible** for instant access to essential pilgrimage functions
- **Tab badges** for time-sensitive notifications (prayer times, family alerts)
- **Islamic iconography** with text labels for cultural clarity
- **Touch-optimized** for gloved hands and crowded conditions

**Secondary Navigation (Within Tabs)**
- **Feature grids** (2x2) for organized tool access on Home tab
- **Modal overlays** for focused tasks (Family Finder setup, Sermon selection)
- **Stack navigation** for detailed content (Place details, Guide steps)
- **Floating action buttons** for emergency features (Family Finder alerts)

**Information Hierarchy Priorities**
1. **Safety/Emergency** - Family Finder emergency access from any screen
2. **Time-Critical** - Prayer times, live sermons, crowd insights
3. **Core Pilgrimage** - GPS ritual assistance, guides, historical places
4. **Planning & Coordination** - Itineraries, saved locations, preferences
