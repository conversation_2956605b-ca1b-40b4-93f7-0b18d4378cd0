# User Flows

## Core Pilgrimage Flow: GPS-Guided Ritual Assistance

**User Goal**: Complete Tawaf or Sa'i with GPS assistance while maintaining spiritual focus

**Entry Points**: Knowledge Hub → MyIbadah → GPS Counter (Premium), or Emergency manual override

**Success Criteria**: 7 complete circuits tracked accurately with minimal interface interaction

### GPS Ritual Flow Diagram

```mermaid
graph TD
    A[Pilgrim Approaches Ritual Site] --> B[Opens Knowledge Hub]

    B --> C{Premium Subscriber?}
    C -->|Yes| D[Access GPS-Guided Counters]
    C -->|No| E[Manual Counters + Upgrade Prompt]

    D --> F{Select Ritual Type}
    F -->|Tawaf| G[GPS Tawaf Counter - Kaaba Circuit]
    F -->|Sa'i| H[GPS Sa'i Counter - Safa-Marwah]

    %% GPS Tawaf Flow
    G --> G1[GPS Location Verification]
    G1 --> G2{GPS Accuracy OK?}
    G2 -->|Yes| G3[Automatic Circuit Tracking]
    G2 -->|No| G4[Fallback to Manual Override]

    G3 --> G5[Round Completion Detection]
    G5 --> G6[Visual/Audio Feedback]
    G6 --> G7{7 Rounds Complete?}
    G7 -->|No| G3
    G7 -->|Yes| I[Ritual Completion Celebration]

    %% GPS Sa'i Flow
    H --> H1[Start Point Detection - Safa/Marwah]
    H1 --> H2[Directional Movement Tracking]
    H2 --> H3[End Point Detection + Round Count]
    H3 --> H4{7 Rounds Complete?}
    H4 -->|No| H2
    H4 -->|Yes| I

    %% Manual Override Path
    G4 --> J[Large Manual Counter Interface]
    E --> J
    J --> K[Manual Round Increment]
    K --> L{7 Rounds Complete?}
    L -->|No| K
    L -->|Yes| I

    I --> M[Progress Update to Guide]
    M --> N[Return to Pilgrimage Journey]
```

### Critical Edge Cases

**GPS Accuracy Issues**:
- Automatic fallback to manual counters when GPS signal poor
- Clear user notification about accuracy status
- Manual override button always visible during GPS operation
- Battery optimization prevents GPS drain in extended usage

**Crowded Environment Challenges**:
- Alternative positioning using nearby landmarks when GPS fails
- Simplified interface with minimal interaction requirements
- Large touch targets suitable for crowded conditions and gloved hands
- Offline functionality when network connectivity compromised

## Family Safety Flow: Emergency Coordination

**User Goal**: Locate and communicate with family members during emergency or separation

**Entry Points**: Home tab Family Finder tile, Emergency button, Push notification

**Success Criteria**: Family contact established and location shared within 30 seconds

### Emergency Family Safety Flow

```mermaid
graph TD
    A[Emergency/Separation Situation] --> B{Family Finder Access}
    B -->|Home Tile| C[Family Finder Dashboard]
    B -->|Emergency Button| D[Direct Emergency Mode]
    B -->|Push Notification| E[Respond to Family Alert]

    C --> F[View Family Map - Real-time Locations]
    F --> G{Technology Available?}
    G -->|GPS + Internet| H[Google Maps Integration]
    G -->|WiFi Direct| I[Local Network Tracking]
    G -->|Bluetooth Only| J[Proximity Detection]

    H --> K[Precise Location Display]
    I --> L[Medium-Range Tracking]
    J --> M[Close-Range Detection]

    K --> N[Select Family Member]
    L --> N
    M --> N

    N --> O{Communication Action}
    O -->|Get Directions| P[Navigation to Family Member]
    O -->|Send Message| Q[Preset Message Selection]
    O -->|Emergency Alert| R[Emergency Broadcast to All]

    Q --> Q1[Message Options]
    Q1 --> Q1A["Come to me" + Location]
    Q1 --> Q1B["Go to hotel" + Hotel Location]
    Q1 --> Q1C["I'm safe" + Status Update]
    Q1 --> Q1D["Need help" + Emergency Alert]

    D --> R
    R --> S[Enhanced Delivery Attempts]
    S --> T[All Available Technologies]
    T --> U[Family Notification + Location]

    P --> V[Turn-by-Turn Guidance]
    V --> W[Arrival Confirmation]
    W --> X[Family Reunification]
```

## Friday Sermon Discovery Flow (Premium Feature)

**User Goal**: Access Friday sermon in preferred language from chosen mosque with synchronized captions

**Entry Points**: Friday Sermon tab, Home tab sermon notifications, scheduled reminders

**Success Criteria**: Exact sermon located and playing with synchronized captions within 10 seconds

### Advanced Sermon Selection Algorithm

```mermaid
graph TD
    A[User Opens Friday Sermon Tab] --> B[Premium Verification]
    B -->|Premium Active| C[Sermon Parameter Selection]
    B -->|Free User| D[Premium Upgrade Prompt]

    C --> E[Location Selection - Mecca/Medina]
    C --> F[Language Selection - 6 Options]
    C --> G[Type Selection - Live/Last Friday]

    E --> H[Hijri Date Calculation]
    F --> H
    G --> H

    H --> I[YouTube API Search - @tubesermon Channel]
    I --> J[Multi-Step Filtering Algorithm]

    J --> K[Step 1: Date Filter]
    K --> K1["Find titles with Hijri date: 'YYYY-MM-DD'"]

    K1 --> L[Step 2: Language Filter]
    L --> L1[Apply Language Keywords]
    L1 --> L1A[English: 'english']
    L1 --> L1B[Malay: 'melayu']
    L1 --> L1C[Indonesian: 'indonesia']
    L1 --> L1D[Turkish: 'türkçe']
    L1 --> L1E[Persian: 'فارسی']
    L1 --> L1F[Urdu: 'اردو']

    L1 --> M[Step 3: Mosque Filter]
    M --> M1{Language-Specific Mosque Keywords}
    M1 -->|Mecca| M1A[English: 'makkah', Malay/Indonesian: 'haram', etc.]
    M1 -->|Medina| M1B[English: 'prophet', Malay: 'nabawi', Turkish: 'nebevi', etc.]

    M1 --> N[Step 4: Exact Match Validation]
    N --> O{Single Result Found?}

    O -->|Yes| P[Load Audio + Caption Streams]
    O -->|No| Q[Alternative Suggestions]

    P --> R[youtube_explode_dart Extraction]
    R --> S[Unified Audio-Caption Streaming]

    S --> T[just_audio Playback Engine]
    S --> U[Synchronized Caption Display]

    T --> V[Background Playback Support]
    U --> W[Real-time Text Synchronization]

    V --> X[30-Second Predictive Buffering]
    W --> Y[Caption Search & Navigation]

    Q --> Z[Fallback Options]
    Z --> Z1[Different Date Suggestions]
    Z --> Z2[Different Language Options]
    Z --> Z3[Web Scraping Fallback]
```
