# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Flutter/Dart/Pub related - Mobile app
apps/mobile/.dart_tool/
apps/mobile/.flutter-plugins
apps/mobile/.flutter-plugins-dependencies
apps/mobile/.packages
apps/mobile/.pub-cache/
apps/mobile/.pub/
apps/mobile/build/

# Android related
apps/mobile/android/**/gradle-wrapper.jar
apps/mobile/android/.gradle
apps/mobile/android/captures/
apps/mobile/android/gradlew
apps/mobile/android/gradlew.bat
apps/mobile/android/local.properties
apps/mobile/android/**/GeneratedPluginRegistrant.java
apps/mobile/android/key.properties
*.jks

# iOS related (for future)
apps/mobile/ios/**/*.mode1v3
apps/mobile/ios/**/*.mode2v3
apps/mobile/ios/**/*.moved-aside
apps/mobile/ios/**/*.pbxuser
apps/mobile/ios/**/*.perspectivev3
apps/mobile/ios/**/*sync/
apps/mobile/ios/**/.sconsign.dblite
apps/mobile/ios/**/.tags*
apps/mobile/ios/**/.vagrant/
apps/mobile/ios/**/DerivedData/
apps/mobile/ios/**/Icon?
apps/mobile/ios/**/Pods/
apps/mobile/ios/**/.symlinks/
apps/mobile/ios/**/profile
apps/mobile/ios/**/xcuserdata
apps/mobile/ios/.generated/
apps/mobile/ios/Flutter/App.framework
apps/mobile/ios/Flutter/Flutter.framework
apps/mobile/ios/Flutter/Flutter.podspec
apps/mobile/ios/Flutter/Generated.xcconfig
apps/mobile/ios/Flutter/ephemeral/
apps/mobile/ios/Flutter/app.flx
apps/mobile/ios/Flutter/app.zip
apps/mobile/ios/Flutter/flutter_assets/
apps/mobile/ios/Flutter/flutter_export_environment.sh
apps/mobile/ios/ServiceDefinitions.json
apps/mobile/ios/Runner/GeneratedPluginRegistrant.*

# Firebase related
.firebase/
firebase-debug.log
firebase-debug.*.log
.firebaserc
firebase.json
firestore.rules
storage.rules
firestore.indexes.json

# Firebase config files (keep templates, ignore actual config)
apps/mobile/android/app/google-services.json
apps/mobile/ios/Runner/GoogleService-Info.plist
apps/mobile/lib/firebase_options.dart

# Cloud Functions
apps/cloud_functions/.dart_tool/
apps/cloud_functions/build/
apps/cloud_functions/node_modules/

# Environment and secrets
.env
.env.local
.env.*.local
*.env
secrets/
config/secrets/

# IDE related
.vscode/
.idea/
*.swp
*.swo
*~

# OS related
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
*.log.*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Build artifacts
dist/
out/

# Generated files
*.g.dart
*.freezed.dart
*.gr.dart

# Test related
test_driver/screenshots/

# Melos
.melos_tool/

# FVM
.fvm/

# Local development scripts
create_structure.ps1
