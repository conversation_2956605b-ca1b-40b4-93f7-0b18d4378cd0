// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:mobile/main.dart';

void main() {
  testWidgets('Ziarah app loads with bottom navigation', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ZiarahApp());
    await tester.pumpAndSettle();

    // Verify that the app loads with the home page
    expect(find.text('As<PERSON><PERSON>u <PERSON>ku<PERSON>'), findsOneWidget);
    expect(find.text('Welcome to your Islamic pilgrimage companion'), findsOneWidget);

    // Verify that bottom navigation is present with all 5 tabs
    expect(find.text('Home'), findsOneWidget);
    expect(find.text('Prayer Times'), findsOneWidget);
    expect(find.text('Knowledge Hub'), findsOneWidget);
    expect(find.text('Friday Sermon'), findsOneWidget);
    expect(find.text('Historical Places'), findsOneWidget);

    // Test navigation to Prayer Times tab
    await tester.tap(find.text('Prayer Times'));
    await tester.pumpAndSettle();

    // Verify that Prayer Times page is displayed
    expect(find.text('Next Prayer'), findsOneWidget);
    expect(find.text('Today\'s Prayer Times'), findsOneWidget);
  });
}
