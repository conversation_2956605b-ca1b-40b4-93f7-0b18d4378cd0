import 'package:flutter/material.dart';
import '../../../../shared/themes/color_palette.dart';
import '../../../../shared/themes/typography.dart';

/// Prayer Times page (FREE tier)
/// Features: Prayer schedule, Qibla direction, Hijri calendar
class PrayerTimesPage extends StatelessWidget {
  const PrayerTimesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Prayer Times'),
        centerTitle: true,
        backgroundColor: IslamicColorPalette.primaryBlue,
        foregroundColor: IslamicColorPalette.textOnPrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current prayer info
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: IslamicColorPalette.primaryGradient,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  Text(
                    'Next Prayer',
                    style: IslamicTypography.latinBodyMedium.copyWith(
                      color: IslamicColorPalette.textOnPrimary.withOpacity(0.8),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Dhuhr',
                    style: IslamicTypography.prayerNameLatin.copyWith(
                      color: IslamicColorPalette.textOnPrimary,
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    'الظهر',
                    style: IslamicTypography.prayerNameArabic.copyWith(
                      color: IslamicColorPalette.textOnPrimary,
                      fontSize: 20,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '12:30 PM',
                    style: IslamicTypography.prayerTimeDisplay.copyWith(
                      color: IslamicColorPalette.textOnPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'in 2 hours 15 minutes',
                    style: IslamicTypography.latinBodyMedium.copyWith(
                      color: IslamicColorPalette.textOnPrimary.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Today's prayer times
            Text(
              'Today\'s Prayer Times',
              style: IslamicTypography.latinTitleLarge.copyWith(
                color: IslamicColorPalette.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            Expanded(
              child: ListView(
                children: [
                  _buildPrayerTimeCard('Fajr', 'الفجر', '5:30 AM', IslamicColorPalette.fajrBlue),
                  _buildPrayerTimeCard('Sunrise', 'الشروق', '6:45 AM', IslamicColorPalette.sunriseOrange),
                  _buildPrayerTimeCard('Dhuhr', 'الظهر', '12:30 PM', IslamicColorPalette.dhuhrYellow),
                  _buildPrayerTimeCard('Asr', 'العصر', '3:45 PM', IslamicColorPalette.asrAmber),
                  _buildPrayerTimeCard('Maghrib', 'المغرب', '6:15 PM', IslamicColorPalette.maghribPurple),
                  _buildPrayerTimeCard('Isha', 'العشاء', '7:30 PM', IslamicColorPalette.ishaIndigo),
                ],
              ),
            ),
            
            // Quick actions
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showComingSoon(context, 'Qibla Direction'),
                    icon: const Icon(Icons.explore),
                    label: const Text('Qibla Direction'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showComingSoon(context, 'Hijri Calendar'),
                    icon: const Icon(Icons.calendar_today),
                    label: const Text('Hijri Calendar'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrayerTimeCard(String nameEn, String nameAr, String time, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.access_time,
            color: color,
            size: 20,
          ),
        ),
        title: Row(
          children: [
            Text(
              nameEn,
              style: IslamicTypography.latinBodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              nameAr,
              style: IslamicTypography.arabicBodyMedium.copyWith(
                color: IslamicColorPalette.textSecondary,
              ),
            ),
          ],
        ),
        trailing: Text(
          time,
          style: IslamicTypography.latinBodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: IslamicColorPalette.primaryBlue,
          ),
        ),
      ),
    );
  }

  void _showComingSoon(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$feature Coming Soon'),
        content: Text('This feature will be implemented in upcoming stories.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
