import 'package:flutter/material.dart';
import '../../../../shared/themes/color_palette.dart';
import '../../../../shared/themes/typography.dart';

/// Home page with 2x2 grid layout (FREE tier)
/// Features: Weather for holy cities, Hajj/Umrah news, Quick Map, Crowd Insights
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ziarah'),
        centerTitle: true,
        backgroundColor: IslamicColorPalette.primaryBlue,
        foregroundColor: IslamicColorPalette.textOnPrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome section
            Text(
              'Assalamu Alaiku<PERSON>',
              style: IslamicTypography.arabicTitleLarge.copyWith(
                color: IslamicColorPalette.primaryBlue,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Welcome to your Islamic pilgrimage companion',
              style: IslamicTypography.latinBodyLarge.copyWith(
                color: IslamicColorPalette.textSecondary,
              ),
            ),
            const SizedBox(height: 24),
            
            // 2x2 Grid of main features
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildFeatureCard(
                    context,
                    icon: Icons.wb_sunny,
                    title: 'Holy Cities Weather',
                    subtitle: 'Mecca & Medina forecast',
                    color: IslamicColorPalette.sunriseOrange,
                    onTap: () => _showComingSoon(context, 'Weather'),
                  ),
                  _buildFeatureCard(
                    context,
                    icon: Icons.article,
                    title: 'Hajj & Umrah News',
                    subtitle: 'Latest updates',
                    color: IslamicColorPalette.successGreen,
                    onTap: () => _showComingSoon(context, 'News'),
                  ),
                  _buildFeatureCard(
                    context,
                    icon: Icons.map,
                    title: 'Quick Map',
                    subtitle: 'Saved locations',
                    color: IslamicColorPalette.primaryBlue,
                    onTap: () => _showComingSoon(context, 'Quick Map'),
                  ),
                  _buildFeatureCard(
                    context,
                    icon: Icons.insights,
                    title: 'Crowd Insights',
                    subtitle: 'Real-time data',
                    color: IslamicColorPalette.secondaryGold,
                    onTap: () => _showComingSoon(context, 'Crowd Insights'),
                  ),
                ],
              ),
            ),
            
            // Bottom info section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: IslamicColorPalette.surfaceGray,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: IslamicColorPalette.primaryBlue,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Explore all features through the navigation tabs below',
                      style: IslamicTypography.latinBodyMedium.copyWith(
                        color: IslamicColorPalette.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: IslamicTypography.latinTitleMedium.copyWith(
                  color: IslamicColorPalette.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: IslamicTypography.latinBodySmall.copyWith(
                  color: IslamicColorPalette.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showComingSoon(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$feature Coming Soon'),
        content: Text('This feature will be implemented in upcoming stories.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
