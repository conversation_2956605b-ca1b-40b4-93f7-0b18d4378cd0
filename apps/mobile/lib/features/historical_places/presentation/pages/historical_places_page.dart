import 'package:flutter/material.dart';
import '../../../../shared/themes/color_palette.dart';
import '../../../../shared/themes/typography.dart';

/// Historical Places page (Mixed freemium - Hadith premium)
/// Features: Islamic heritage sites, basic info (FREE), detailed Hadith references (PREMIUM)
class HistoricalPlacesPage extends StatelessWidget {
  const HistoricalPlacesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Historical Places'),
        centerTitle: true,
        backgroundColor: IslamicColorPalette.primaryBlue,
        foregroundColor: IslamicColorPalette.textOnPrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section
            Text(
              'Islamic Heritage Sites',
              style: IslamicTypography.latinTitleLarge.copyWith(
                color: IslamicColorPalette.primaryBlue,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Explore sacred places with historical significance',
              style: IslamicTypography.latinBodyLarge.copyWith(
                color: IslamicColorPalette.textSecondary,
              ),
            ),
            const SizedBox(height: 24),
            
            // Search bar
            TextField(
              decoration: InputDecoration(
                hintText: 'Search historical places...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onChanged: (value) {
                // TODO: Implement search functionality
              },
            ),
            
            const SizedBox(height: 24),
            
            // Places list
            Expanded(
              child: ListView(
                children: [
                  _buildPlaceCard(
                    context,
                    title: 'Masjid al-Haram',
                    subtitle: 'The Great Mosque of Mecca',
                    location: 'Mecca, Saudi Arabia',
                    imageIcon: Icons.mosque,
                    color: IslamicColorPalette.secondaryGold,
                    hasHadithReferences: true,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildPlaceCard(
                    context,
                    title: 'Masjid an-Nabawi',
                    subtitle: 'The Prophet\'s Mosque',
                    location: 'Medina, Saudi Arabia',
                    imageIcon: Icons.account_balance,
                    color: IslamicColorPalette.successGreen,
                    hasHadithReferences: true,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildPlaceCard(
                    context,
                    title: 'Mount Arafat',
                    subtitle: 'The Mount of Mercy',
                    location: 'Mecca Province, Saudi Arabia',
                    imageIcon: Icons.landscape,
                    color: IslamicColorPalette.tertiaryBrown,
                    hasHadithReferences: true,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildPlaceCard(
                    context,
                    title: 'Mina',
                    subtitle: 'The Tent City',
                    location: 'Mecca Province, Saudi Arabia',
                    imageIcon: Icons.home,
                    color: IslamicColorPalette.warningAmber,
                    hasHadithReferences: false,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildPlaceCard(
                    context,
                    title: 'Muzdalifah',
                    subtitle: 'The Sacred Grove',
                    location: 'Mecca Province, Saudi Arabia',
                    imageIcon: Icons.park,
                    color: IslamicColorPalette.primaryBlue,
                    hasHadithReferences: false,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required String location,
    required IconData imageIcon,
    required Color color,
    required bool hasHadithReferences,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _showPlaceDetails(context, title, hasHadithReferences),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Image placeholder
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  imageIcon,
                  size: 40,
                  color: color,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Place info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: IslamicTypography.latinTitleMedium.copyWith(
                        color: IslamicColorPalette.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: IslamicTypography.latinBodyMedium.copyWith(
                        color: IslamicColorPalette.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 16,
                          color: IslamicColorPalette.textTertiary,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            location,
                            style: IslamicTypography.latinBodySmall.copyWith(
                              color: IslamicColorPalette.textTertiary,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: IslamicColorPalette.successGreen,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'FREE',
                            style: IslamicTypography.latinBodySmall.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                              fontSize: 10,
                            ),
                          ),
                        ),
                        if (hasHadithReferences) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: IslamicColorPalette.secondaryGold,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              'HADITH PREMIUM',
                              style: IslamicTypography.latinBodySmall.copyWith(
                                color: IslamicColorPalette.textOnSecondary,
                                fontWeight: FontWeight.w600,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
              
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: IslamicColorPalette.textTertiary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showPlaceDetails(BuildContext context, String placeName, bool hasHadithReferences) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(placeName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Basic information about $placeName is available for free.'),
            const SizedBox(height: 12),
            if (hasHadithReferences) ...[
              Row(
                children: [
                  Icon(
                    Icons.star,
                    size: 16,
                    color: IslamicColorPalette.secondaryGold,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Premium Hadith References Available',
                    style: TextStyle(
                      color: IslamicColorPalette.secondaryGold,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Upgrade to premium to access detailed Hadith references and scholarly commentary.',
                style: TextStyle(
                  color: IslamicColorPalette.textSecondary,
                  fontSize: 14,
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          if (hasHadithReferences)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _showComingSoon(context, 'Premium Hadith References');
              },
              child: const Text('Upgrade'),
            ),
        ],
      ),
    );
  }

  void _showComingSoon(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$feature Coming Soon'),
        content: Text('This feature will be implemented in upcoming stories.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
