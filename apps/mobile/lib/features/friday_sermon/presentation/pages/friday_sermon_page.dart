import 'package:flutter/material.dart';
import '../../../../shared/themes/color_palette.dart';
import '../../../../shared/themes/typography.dart';

/// Friday Sermon page (PREMIUM ONLY)
/// Features: Audio sermons with synchronized captions, language selection
class FridaySermonPage extends StatelessWidget {
  const FridaySermonPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Friday Sermon'),
        centerTitle: true,
        backgroundColor: IslamicColorPalette.primaryBlue,
        foregroundColor: IslamicColorPalette.textOnPrimary,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: IslamicColorPalette.secondaryGold,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              'PREMIUM',
              style: IslamicTypography.latinBodySmall.copyWith(
                color: IslamicColorPalette.textOnSecondary,
                fontWeight: FontWeight.w600,
                fontSize: 10,
              ),
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Premium feature banner
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: IslamicColorPalette.secondaryGradient,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.play_circle,
                    size: 48,
                    color: IslamicColorPalette.textOnSecondary,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Premium Audio Sermons',
                    style: IslamicTypography.latinTitleLarge.copyWith(
                      color: IslamicColorPalette.textOnSecondary,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Access high-quality Friday sermons with synchronized captions in multiple languages',
                    style: IslamicTypography.latinBodyMedium.copyWith(
                      color: IslamicColorPalette.textOnSecondary.withOpacity(0.9),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Features list
            Text(
              'Premium Features',
              style: IslamicTypography.latinTitleMedium.copyWith(
                color: IslamicColorPalette.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            Expanded(
              child: ListView(
                children: [
                  _buildFeatureItem(
                    icon: Icons.high_quality,
                    title: 'High-Quality Audio',
                    subtitle: 'Crystal clear sermon audio from trusted sources',
                  ),
                  _buildFeatureItem(
                    icon: Icons.closed_caption,
                    title: 'Synchronized Captions',
                    subtitle: 'Real-time captions synchronized with audio',
                  ),
                  _buildFeatureItem(
                    icon: Icons.language,
                    title: 'Multiple Languages',
                    subtitle: 'Captions available in Arabic, English, and more',
                  ),
                  _buildFeatureItem(
                    icon: Icons.search,
                    title: 'Smart Search',
                    subtitle: 'Find sermons by topic, date, or speaker',
                  ),
                  _buildFeatureItem(
                    icon: Icons.download,
                    title: 'Offline Access',
                    subtitle: 'Download sermons for offline listening',
                  ),
                  _buildFeatureItem(
                    icon: Icons.bookmark,
                    title: 'Bookmarks',
                    subtitle: 'Save your favorite sermon moments',
                  ),
                ],
              ),
            ),
            
            // Upgrade button
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: IslamicColorPalette.surfaceGray,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Text(
                    'Upgrade to Premium',
                    style: IslamicTypography.latinTitleMedium.copyWith(
                      color: IslamicColorPalette.primaryBlue,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Get access to all premium features including Friday sermons, GPS counters, and more',
                    style: IslamicTypography.latinBodyMedium.copyWith(
                      color: IslamicColorPalette.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _showSubscriptionOptions(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: IslamicColorPalette.secondaryGold,
                      foregroundColor: IslamicColorPalette.textOnSecondary,
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                    ),
                    child: const Text('Upgrade Now'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: IslamicColorPalette.primaryBlue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 24,
              color: IslamicColorPalette.primaryBlue,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: IslamicTypography.latinBodyLarge.copyWith(
                    color: IslamicColorPalette.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: IslamicTypography.latinBodyMedium.copyWith(
                    color: IslamicColorPalette.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showSubscriptionOptions(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Your Plan'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Monthly Premium'),
              subtitle: const Text('\$4.99/month'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.of(context).pop();
                _showComingSoon(context, 'Monthly Subscription');
              },
            ),
            ListTile(
              title: const Text('Yearly Premium'),
              subtitle: const Text('\$49.99/year (Save 17%)'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.of(context).pop();
                _showComingSoon(context, 'Yearly Subscription');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showComingSoon(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$feature Coming Soon'),
        content: Text('This feature will be implemented in upcoming stories.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
