import 'package:flutter/material.dart';
import '../../../../shared/themes/color_palette.dart';
import '../../../../shared/themes/typography.dart';

/// Knowledge Hub page (Mixed freemium - GPS premium)
/// Features: Hajj guide, Umrah guide, Manual counter (FREE), GPS counter (PREMIUM)
class KnowledgeHubPage extends StatelessWidget {
  const KnowledgeHubPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Knowledge Hub'),
        centerTitle: true,
        backgroundColor: IslamicColorPalette.primaryBlue,
        foregroundColor: IslamicColorPalette.textOnPrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section
            Text(
              'Islamic Pilgrimage Guidance',
              style: IslamicTypography.latinTitleLarge.copyWith(
                color: IslamicColorPalette.primaryBlue,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Complete guides for Hajj and Umrah rituals',
              style: IslamicTypography.latinBodyLarge.copyWith(
                color: IslamicColorPalette.textSecondary,
              ),
            ),
            const SizedBox(height: 24),
            
            // Main guides
            Expanded(
              child: ListView(
                children: [
                  _buildGuideCard(
                    context,
                    title: 'Hajj Guide',
                    subtitle: 'Complete step-by-step Hajj guidance',
                    icon: Icons.location_city,
                    color: IslamicColorPalette.secondaryGold,
                    isFree: true,
                    onTap: () => _showComingSoon(context, 'Hajj Guide'),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildGuideCard(
                    context,
                    title: 'Umrah Guide',
                    subtitle: 'Essential Umrah ritual instructions',
                    icon: Icons.mosque,
                    color: IslamicColorPalette.successGreen,
                    isFree: true,
                    onTap: () => _showComingSoon(context, 'Umrah Guide'),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Ritual counters section
                  Text(
                    'Ritual Counters',
                    style: IslamicTypography.latinTitleMedium.copyWith(
                      color: IslamicColorPalette.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  _buildCounterCard(
                    context,
                    title: 'Manual Counter',
                    subtitle: 'Tap to count Tawaf and Sa\'i',
                    icon: Icons.touch_app,
                    color: IslamicColorPalette.primaryBlue,
                    isFree: true,
                    onTap: () => _showComingSoon(context, 'Manual Counter'),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildCounterCard(
                    context,
                    title: 'GPS Counter',
                    subtitle: 'Automatic GPS-based counting',
                    icon: Icons.gps_fixed,
                    color: IslamicColorPalette.tertiaryBrown,
                    isFree: false,
                    onTap: () => _showPremiumFeature(context, 'GPS Counter'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGuideCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required bool isFree,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: color,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          title,
                          style: IslamicTypography.latinTitleMedium.copyWith(
                            color: IslamicColorPalette.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (isFree) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: IslamicColorPalette.successGreen,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              'FREE',
                              style: IslamicTypography.latinBodySmall.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: IslamicTypography.latinBodyMedium.copyWith(
                        color: IslamicColorPalette.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: IslamicColorPalette.textTertiary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCounterCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required bool isFree,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 24,
                  color: color,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          title,
                          style: IslamicTypography.latinBodyLarge.copyWith(
                            color: IslamicColorPalette.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: isFree ? IslamicColorPalette.successGreen : IslamicColorPalette.secondaryGold,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            isFree ? 'FREE' : 'PREMIUM',
                            style: IslamicTypography.latinBodySmall.copyWith(
                              color: isFree ? Colors.white : IslamicColorPalette.textOnSecondary,
                              fontWeight: FontWeight.w600,
                              fontSize: 10,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: IslamicTypography.latinBodyMedium.copyWith(
                        color: IslamicColorPalette.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: IslamicColorPalette.textTertiary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showComingSoon(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$feature Coming Soon'),
        content: Text('This feature will be implemented in upcoming stories.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showPremiumFeature(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$feature - Premium Feature'),
        content: const Text('This feature requires a premium subscription. Upgrade to access GPS-based ritual counting.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Maybe Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Navigate to subscription page
            },
            child: const Text('Upgrade'),
          ),
        ],
      ),
    );
  }
}
