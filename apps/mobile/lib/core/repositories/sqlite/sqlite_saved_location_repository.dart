import 'package:sqflite/sqflite.dart';
import '../interfaces/saved_location_repository.dart';
import '../../database/database_helper.dart';
import '../../database/models/saved_location_model.dart';
import 'dart:math' as math;

/// SQLite implementation of SavedLocationRepository
/// Handles local saved location data storage and offline functionality
class SQLiteSavedLocationRepository implements SavedLocationRepository {
  final DatabaseHelper _databaseHelper;
  
  SQLiteSavedLocationRepository(this._databaseHelper);
  
  @override
  Future<List<SavedLocationModel>> getUserLocations(String userFirebaseUid) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        DatabaseHelper.savedLocationsTable,
        where: 'user_firebase_uid = ?',
        whereArgs: [userFirebaseUid],
        orderBy: 'created_at DESC',
      );
      
      return result.map((map) => SavedLocationModel.fromMap(map)).toList();
    } catch (e) {
      print('Error getting user locations: $e');
      rethrow;
    }
  }
  
  @override
  Future<SavedLocationModel?> getLocationById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        DatabaseHelper.savedLocationsTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );
      
      if (result.isEmpty) return null;
      return SavedLocationModel.fromMap(result.first);
    } catch (e) {
      print('Error getting location by ID: $e');
      rethrow;
    }
  }
  
  @override
  Future<SavedLocationModel?> getLocationByFirebaseId(String firebaseId) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        DatabaseHelper.savedLocationsTable,
        where: 'firebase_id = ?',
        whereArgs: [firebaseId],
        limit: 1,
      );
      
      if (result.isEmpty) return null;
      return SavedLocationModel.fromMap(result.first);
    } catch (e) {
      print('Error getting location by Firebase ID: $e');
      rethrow;
    }
  }
  
  @override
  Future<SavedLocationModel> createLocation(SavedLocationModel location) async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();
      
      final locationWithTimestamps = location.copyWith(
        createdAt: now,
        updatedAt: now,
      );
      
      final id = await db.insert(
        DatabaseHelper.savedLocationsTable,
        locationWithTimestamps.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      return locationWithTimestamps.copyWith(id: id);
    } catch (e) {
      print('Error creating location: $e');
      rethrow;
    }
  }
  
  @override
  Future<SavedLocationModel> updateLocation(SavedLocationModel location) async {
    try {
      final db = await _databaseHelper.database;
      final locationWithUpdatedTime = location.copyWith(updatedAt: DateTime.now());
      
      await db.update(
        DatabaseHelper.savedLocationsTable,
        locationWithUpdatedTime.toMap(),
        where: 'id = ?',
        whereArgs: [location.id],
      );
      
      return locationWithUpdatedTime;
    } catch (e) {
      print('Error updating location: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> deleteLocation(int id) async {
    try {
      final db = await _databaseHelper.database;
      await db.delete(
        DatabaseHelper.savedLocationsTable,
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      print('Error deleting location: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> deleteLocationByFirebaseId(String firebaseId) async {
    try {
      final db = await _databaseHelper.database;
      await db.delete(
        DatabaseHelper.savedLocationsTable,
        where: 'firebase_id = ?',
        whereArgs: [firebaseId],
      );
    } catch (e) {
      print('Error deleting location by Firebase ID: $e');
      rethrow;
    }
  }
  
  @override
  Future<List<SavedLocationModel>> getFavoriteLocations(String userFirebaseUid) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        DatabaseHelper.savedLocationsTable,
        where: 'user_firebase_uid = ? AND is_favorite = 1',
        whereArgs: [userFirebaseUid],
        orderBy: 'name ASC',
      );
      
      return result.map((map) => SavedLocationModel.fromMap(map)).toList();
    } catch (e) {
      print('Error getting favorite locations: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> toggleFavorite(int id) async {
    try {
      final db = await _databaseHelper.database;
      final location = await getLocationById(id);
      if (location == null) return;
      
      await db.update(
        DatabaseHelper.savedLocationsTable,
        {
          'is_favorite': location.isFavorite ? 0 : 1,
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      print('Error toggling favorite: $e');
      rethrow;
    }
  }
  
  @override
  Future<List<SavedLocationModel>> getLocationsByType(
    String userFirebaseUid,
    LocationType type,
  ) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        DatabaseHelper.savedLocationsTable,
        where: 'user_firebase_uid = ? AND type = ?',
        whereArgs: [userFirebaseUid, type.value],
        orderBy: 'name ASC',
      );
      
      return result.map((map) => SavedLocationModel.fromMap(map)).toList();
    } catch (e) {
      print('Error getting locations by type: $e');
      rethrow;
    }
  }
  
  @override
  Future<List<SavedLocationModel>> searchLocationsByName(
    String userFirebaseUid,
    String query,
  ) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        DatabaseHelper.savedLocationsTable,
        where: 'user_firebase_uid = ? AND (name LIKE ? OR notes LIKE ? OR address LIKE ?)',
        whereArgs: [userFirebaseUid, '%$query%', '%$query%', '%$query%'],
        orderBy: 'name ASC',
      );
      
      return result.map((map) => SavedLocationModel.fromMap(map)).toList();
    } catch (e) {
      print('Error searching locations by name: $e');
      rethrow;
    }
  }
  
  @override
  Future<List<SavedLocationModel>> getNearbyLocations(
    String userFirebaseUid,
    double latitude,
    double longitude,
    double radiusKm,
  ) async {
    try {
      // Get all user locations and filter by distance
      final allLocations = await getUserLocations(userFirebaseUid);
      
      return allLocations.where((location) {
        final distance = location.distanceTo(latitude, longitude);
        return distance <= radiusKm;
      }).toList()
        ..sort((a, b) => a.distanceTo(latitude, longitude)
            .compareTo(b.distanceTo(latitude, longitude)));
    } catch (e) {
      print('Error getting nearby locations: $e');
      rethrow;
    }
  }
  
  // Implement remaining methods with basic functionality
  @override
  Future<List<SavedLocationModel>> getLocationsInBounds(
    String userFirebaseUid,
    double northLatitude,
    double southLatitude,
    double eastLongitude,
    double westLongitude,
  ) async {
    final allLocations = await getUserLocations(userFirebaseUid);
    return allLocations.where((location) {
      return location.latitude >= southLatitude &&
          location.latitude <= northLatitude &&
          location.longitude >= westLongitude &&
          location.longitude <= eastLongitude;
    }).toList();
  }
  
  @override
  Future<List<SavedLocationModel>> createLocations(List<SavedLocationModel> locations) async {
    final results = <SavedLocationModel>[];
    for (final location in locations) {
      results.add(await createLocation(location));
    }
    return results;
  }
  
  @override
  Future<List<SavedLocationModel>> updateLocations(List<SavedLocationModel> locations) async {
    final results = <SavedLocationModel>[];
    for (final location in locations) {
      results.add(await updateLocation(location));
    }
    return results;
  }
  
  @override
  Future<void> deleteLocations(List<int> ids) async {
    for (final id in ids) {
      await deleteLocation(id);
    }
  }
  
  @override
  Future<Map<String, dynamic>> getLocationStats(String userFirebaseUid) async {
    final locations = await getUserLocations(userFirebaseUid);
    final favorites = await getFavoriteLocations(userFirebaseUid);
    
    final typeCount = <String, int>{};
    for (final location in locations) {
      typeCount[location.type.value] = (typeCount[location.type.value] ?? 0) + 1;
    }
    
    return {
      'total_count': locations.length,
      'favorite_count': favorites.length,
      'type_breakdown': typeCount,
    };
  }
  
  // Placeholder implementations for remaining methods
  @override
  Future<void> syncLocationData(String userFirebaseUid) async {
    // TODO: Implement sync logic
  }
  
  @override
  Future<List<SavedLocationModel>> getLocationsNeedingSync(String userFirebaseUid) async {
    return [];
  }
  
  @override
  Future<void> markLocationAsSynced(int id) async {
    // TODO: Implement sync marking
  }
  
  @override
  Future<List<SavedLocationModel>> importLocations(
    String userFirebaseUid,
    List<Map<String, dynamic>> locationData,
  ) async {
    return [];
  }
  
  @override
  Future<List<Map<String, dynamic>>> exportLocations(String userFirebaseUid) async {
    final locations = await getUserLocations(userFirebaseUid);
    return locations.map((location) => location.toMap()).toList();
  }
  
  @override
  Future<void> clearUserLocations(String userFirebaseUid) async {
    final db = await _databaseHelper.database;
    await db.delete(
      DatabaseHelper.savedLocationsTable,
      where: 'user_firebase_uid = ?',
      whereArgs: [userFirebaseUid],
    );
  }
  
  @override
  Future<List<SavedLocationModel>> getMostVisitedLocations(
    String userFirebaseUid, {
    int limit = 10,
  }) async {
    // TODO: Implement visit tracking
    return [];
  }
  
  @override
  Future<List<SavedLocationModel>> getRecentlyAddedLocations(
    String userFirebaseUid, {
    int limit = 10,
  }) async {
    final db = await _databaseHelper.database;
    final result = await db.query(
      DatabaseHelper.savedLocationsTable,
      where: 'user_firebase_uid = ?',
      whereArgs: [userFirebaseUid],
      orderBy: 'created_at DESC',
      limit: limit,
    );
    
    return result.map((map) => SavedLocationModel.fromMap(map)).toList();
  }
  
  @override
  Future<List<SavedLocationModel>> getLocationsByDistance(
    String userFirebaseUid,
    double latitude,
    double longitude, {
    int? limit,
  }) async {
    final locations = await getUserLocations(userFirebaseUid);
    locations.sort((a, b) => a.distanceTo(latitude, longitude)
        .compareTo(b.distanceTo(latitude, longitude)));
    
    if (limit != null && locations.length > limit) {
      return locations.take(limit).toList();
    }
    return locations;
  }
  
  @override
  Future<bool> locationExistsAtCoordinates(
    String userFirebaseUid,
    double latitude,
    double longitude, {
    double toleranceMeters = 100,
  }) async {
    final locations = await getUserLocations(userFirebaseUid);
    final toleranceKm = toleranceMeters / 1000;
    
    return locations.any((location) =>
        location.distanceTo(latitude, longitude) <= toleranceKm);
  }
  
  @override
  Future<Map<String, int>> getLocationDensity(
    String userFirebaseUid,
    double centerLatitude,
    double centerLongitude,
    double radiusKm,
    int gridSize,
  ) async {
    // TODO: Implement location density calculation
    return {};
  }
}
