import 'package:sqflite/sqflite.dart';
import '../interfaces/user_repository.dart';
import '../../database/database_helper.dart';
import '../../database/models/user_model.dart';

/// SQLite implementation of UserRepository
/// Handles local user data storage and offline functionality
class SQLiteUserRepository implements UserRepository {
  final DatabaseHelper _databaseHelper;
  
  SQLiteUserRepository(this._databaseHelper);
  
  @override
  Future<UserModel?> getUserByFirebaseUid(String firebaseUid) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        DatabaseHelper.usersTable,
        where: 'firebase_uid = ?',
        whereArgs: [firebaseUid],
        limit: 1,
      );
      
      if (result.isEmpty) return null;
      return UserModel.fromMap(result.first);
    } catch (e) {
      print('Error getting user by Firebase UID: $e');
      rethrow;
    }
  }
  
  @override
  Future<UserModel?> getUserByEmail(String email) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        DatabaseHelper.usersTable,
        where: 'email = ?',
        whereArgs: [email],
        limit: 1,
      );
      
      if (result.isEmpty) return null;
      return UserModel.fromMap(result.first);
    } catch (e) {
      print('Error getting user by email: $e');
      rethrow;
    }
  }
  
  @override
  Future<UserModel> createUser(UserModel user) async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();
      
      final userWithTimestamps = user.copyWith(
        createdAt: now,
        updatedAt: now,
      );
      
      final id = await db.insert(
        DatabaseHelper.usersTable,
        userWithTimestamps.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      return userWithTimestamps.copyWith(id: id);
    } catch (e) {
      print('Error creating user: $e');
      rethrow;
    }
  }
  
  @override
  Future<UserModel> updateUser(UserModel user) async {
    try {
      final db = await _databaseHelper.database;
      final userWithUpdatedTime = user.copyWith(updatedAt: DateTime.now());
      
      await db.update(
        DatabaseHelper.usersTable,
        userWithUpdatedTime.toMap(),
        where: 'firebase_uid = ?',
        whereArgs: [user.firebaseUid],
      );
      
      return userWithUpdatedTime;
    } catch (e) {
      print('Error updating user: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> deleteUser(String firebaseUid) async {
    try {
      final db = await _databaseHelper.database;
      await db.delete(
        DatabaseHelper.usersTable,
        where: 'firebase_uid = ?',
        whereArgs: [firebaseUid],
      );
    } catch (e) {
      print('Error deleting user: $e');
      rethrow;
    }
  }
  
  @override
  Future<bool> userExists(String firebaseUid) async {
    try {
      final user = await getUserByFirebaseUid(firebaseUid);
      return user != null;
    } catch (e) {
      print('Error checking if user exists: $e');
      return false;
    }
  }
  
  @override
  Future<void> updateUserPreferences({
    required String firebaseUid,
    String? language,
    bool? prayerNotifications,
    bool? locationSharing,
    bool? highContrast,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final updates = <String, dynamic>{
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      };
      
      if (language != null) updates['language'] = language;
      if (prayerNotifications != null) updates['prayer_notifications'] = prayerNotifications ? 1 : 0;
      if (locationSharing != null) updates['location_sharing'] = locationSharing ? 1 : 0;
      if (highContrast != null) updates['high_contrast'] = highContrast ? 1 : 0;
      
      await db.update(
        DatabaseHelper.usersTable,
        updates,
        where: 'firebase_uid = ?',
        whereArgs: [firebaseUid],
      );
    } catch (e) {
      print('Error updating user preferences: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> updateSubscription({
    required String firebaseUid,
    required bool isPremium,
    String? subscriptionType,
    DateTime? subscriptionExpiry,
  }) async {
    try {
      final db = await _databaseHelper.database;
      await db.update(
        DatabaseHelper.usersTable,
        {
          'is_premium': isPremium ? 1 : 0,
          'subscription_type': subscriptionType,
          'subscription_expiry': subscriptionExpiry?.millisecondsSinceEpoch,
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'firebase_uid = ?',
        whereArgs: [firebaseUid],
      );
    } catch (e) {
      print('Error updating subscription: $e');
      rethrow;
    }
  }
  
  @override
  Future<List<UserModel>> getAllUsers() async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        DatabaseHelper.usersTable,
        orderBy: 'created_at DESC',
      );
      
      return result.map((map) => UserModel.fromMap(map)).toList();
    } catch (e) {
      print('Error getting all users: $e');
      rethrow;
    }
  }
  
  @override
  Future<List<UserModel>> searchUsersByName(String query) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        DatabaseHelper.usersTable,
        where: 'display_name LIKE ?',
        whereArgs: ['%$query%'],
        orderBy: 'display_name ASC',
      );
      
      return result.map((map) => UserModel.fromMap(map)).toList();
    } catch (e) {
      print('Error searching users by name: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> syncUserData(String firebaseUid) async {
    try {
      final db = await _databaseHelper.database;
      await db.update(
        DatabaseHelper.usersTable,
        {'last_sync': DateTime.now().millisecondsSinceEpoch},
        where: 'firebase_uid = ?',
        whereArgs: [firebaseUid],
      );
    } catch (e) {
      print('Error syncing user data: $e');
      rethrow;
    }
  }
  
  @override
  Future<List<UserModel>> getUsersNeedingSync() async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        DatabaseHelper.usersTable,
        where: 'last_sync IS NULL OR updated_at > last_sync',
        orderBy: 'updated_at DESC',
      );
      
      return result.map((map) => UserModel.fromMap(map)).toList();
    } catch (e) {
      print('Error getting users needing sync: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> markUserAsSynced(String firebaseUid) async {
    await syncUserData(firebaseUid);
  }
  
  @override
  Future<Map<String, dynamic>> getUserStats(String firebaseUid) async {
    try {
      final db = await _databaseHelper.database;
      
      // Get user info
      final user = await getUserByFirebaseUid(firebaseUid);
      if (user == null) return {};
      
      // Get location count
      final locationCount = Sqflite.firstIntValue(
        await db.rawQuery(
          'SELECT COUNT(*) FROM ${DatabaseHelper.savedLocationsTable} WHERE user_firebase_uid = ?',
          [firebaseUid],
        ),
      ) ?? 0;
      
      // Get favorite location count
      final favoriteCount = Sqflite.firstIntValue(
        await db.rawQuery(
          'SELECT COUNT(*) FROM ${DatabaseHelper.savedLocationsTable} WHERE user_firebase_uid = ? AND is_favorite = 1',
          [firebaseUid],
        ),
      ) ?? 0;
      
      return {
        'user_id': user.firebaseUid,
        'display_name': user.displayName,
        'is_premium': user.isPremium,
        'created_at': user.createdAt.toIso8601String(),
        'location_count': locationCount,
        'favorite_count': favoriteCount,
        'language': user.language,
        'last_sync': user.lastSync?.toIso8601String(),
      };
    } catch (e) {
      print('Error getting user stats: $e');
      return {};
    }
  }
  
  @override
  Future<Map<String, dynamic>> backupUserData(String firebaseUid) async {
    try {
      final user = await getUserByFirebaseUid(firebaseUid);
      if (user == null) return {};
      
      return {
        'user': user.toMap(),
        'backup_timestamp': DateTime.now().toIso8601String(),
        'version': '1.0',
      };
    } catch (e) {
      print('Error backing up user data: $e');
      return {};
    }
  }
  
  @override
  Future<void> restoreUserData(String firebaseUid, Map<String, dynamic> backup) async {
    try {
      if (backup['user'] != null) {
        final userData = backup['user'] as Map<String, dynamic>;
        final user = UserModel.fromMap(userData);
        await updateUser(user);
      }
    } catch (e) {
      print('Error restoring user data: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> clearAllUserData(String firebaseUid) async {
    try {
      final db = await _databaseHelper.database;
      
      // Delete user and all related data (cascade delete should handle this)
      await db.delete(
        DatabaseHelper.usersTable,
        where: 'firebase_uid = ?',
        whereArgs: [firebaseUid],
      );
    } catch (e) {
      print('Error clearing user data: $e');
      rethrow;
    }
  }
}
