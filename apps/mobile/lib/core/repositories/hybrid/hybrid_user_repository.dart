import '../interfaces/user_repository.dart';
import '../sqlite/sqlite_user_repository.dart';
import '../firebase/firebase_user_repository.dart';
import '../../database/models/user_model.dart';
import '../../services/firebase_service.dart';

/// Hybrid repository that combines local SQLite and remote Firebase storage
/// Provides offline-first functionality with cloud synchronization
class HybridUserRepository implements UserRepository {
  final SQLiteUserRepository _localRepository;
  final FirebaseUserRepository _remoteRepository;
  final FirebaseService _firebaseService;
  
  HybridUserRepository({
    required SQLiteUserRepository localRepository,
    required FirebaseUserRepository remoteRepository,
    required FirebaseService firebaseService,
  }) : _localRepository = localRepository,
       _remoteRepository = remoteRepository,
       _firebaseService = firebaseService;
  
  /// Check if user is online and authenticated
  bool get _isOnline => _firebaseService.isSignedIn;
  
  @override
  Future<UserModel?> getUserByFirebaseUid(String firebaseUid) async {
    try {
      // Always try local first for better performance
      final localUser = await _localRepository.getUserByFirebaseUid(firebaseUid);
      
      if (_isOnline) {
        // If online, try to get from remote and sync if needed
        try {
          final remoteUser = await _remoteRepository.getUserByFirebaseUid(firebaseUid);
          if (remoteUser != null) {
            // Update local cache with remote data
            await _localRepository.updateUser(remoteUser);
            return remoteUser;
          }
        } catch (e) {
          print('Failed to get user from remote, using local: $e');
        }
      }
      
      return localUser;
    } catch (e) {
      print('Error getting user by Firebase UID: $e');
      rethrow;
    }
  }
  
  @override
  Future<UserModel?> getUserByEmail(String email) async {
    try {
      // Try local first
      final localUser = await _localRepository.getUserByEmail(email);
      
      if (_isOnline && localUser == null) {
        // If not found locally and online, try remote
        try {
          final remoteUser = await _remoteRepository.getUserByEmail(email);
          if (remoteUser != null) {
            // Cache in local storage
            await _localRepository.createUser(remoteUser);
            return remoteUser;
          }
        } catch (e) {
          print('Failed to get user from remote by email: $e');
        }
      }
      
      return localUser;
    } catch (e) {
      print('Error getting user by email: $e');
      rethrow;
    }
  }
  
  @override
  Future<UserModel> createUser(UserModel user) async {
    try {
      // Always create locally first
      final localUser = await _localRepository.createUser(user);
      
      if (_isOnline) {
        // Try to create remotely
        try {
          final remoteUser = await _remoteRepository.createUser(localUser);
          // Update local with any remote changes (like server timestamps)
          await _localRepository.updateUser(remoteUser);
          return remoteUser;
        } catch (e) {
          print('Failed to create user remotely, keeping local: $e');
          // Mark as needing sync
        }
      }
      
      return localUser;
    } catch (e) {
      print('Error creating user: $e');
      rethrow;
    }
  }
  
  @override
  Future<UserModel> updateUser(UserModel user) async {
    try {
      // Always update locally first
      final localUser = await _localRepository.updateUser(user);
      
      if (_isOnline) {
        // Try to update remotely
        try {
          final remoteUser = await _remoteRepository.updateUser(localUser);
          // Update local with any remote changes
          await _localRepository.updateUser(remoteUser);
          return remoteUser;
        } catch (e) {
          print('Failed to update user remotely, keeping local: $e');
          // Mark as needing sync
        }
      }
      
      return localUser;
    } catch (e) {
      print('Error updating user: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> deleteUser(String firebaseUid) async {
    try {
      // Delete locally first
      await _localRepository.deleteUser(firebaseUid);
      
      if (_isOnline) {
        // Try to delete remotely
        try {
          await _remoteRepository.deleteUser(firebaseUid);
        } catch (e) {
          print('Failed to delete user remotely: $e');
          // In this case, we might want to mark for deletion sync
        }
      }
    } catch (e) {
      print('Error deleting user: $e');
      rethrow;
    }
  }
  
  @override
  Future<bool> userExists(String firebaseUid) async {
    try {
      // Check locally first
      final localExists = await _localRepository.userExists(firebaseUid);
      
      if (localExists) return true;
      
      if (_isOnline) {
        // Check remotely if not found locally
        try {
          return await _remoteRepository.userExists(firebaseUid);
        } catch (e) {
          print('Failed to check user existence remotely: $e');
        }
      }
      
      return false;
    } catch (e) {
      print('Error checking if user exists: $e');
      return false;
    }
  }
  
  @override
  Future<void> updateUserPreferences({
    required String firebaseUid,
    String? language,
    bool? prayerNotifications,
    bool? locationSharing,
    bool? highContrast,
  }) async {
    try {
      // Update locally first
      await _localRepository.updateUserPreferences(
        firebaseUid: firebaseUid,
        language: language,
        prayerNotifications: prayerNotifications,
        locationSharing: locationSharing,
        highContrast: highContrast,
      );
      
      if (_isOnline) {
        // Try to update remotely
        try {
          await _remoteRepository.updateUserPreferences(
            firebaseUid: firebaseUid,
            language: language,
            prayerNotifications: prayerNotifications,
            locationSharing: locationSharing,
            highContrast: highContrast,
          );
        } catch (e) {
          print('Failed to update user preferences remotely: $e');
        }
      }
    } catch (e) {
      print('Error updating user preferences: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> updateSubscription({
    required String firebaseUid,
    required bool isPremium,
    String? subscriptionType,
    DateTime? subscriptionExpiry,
  }) async {
    try {
      // Update locally first
      await _localRepository.updateSubscription(
        firebaseUid: firebaseUid,
        isPremium: isPremium,
        subscriptionType: subscriptionType,
        subscriptionExpiry: subscriptionExpiry,
      );
      
      if (_isOnline) {
        // Try to update remotely
        try {
          await _remoteRepository.updateSubscription(
            firebaseUid: firebaseUid,
            isPremium: isPremium,
            subscriptionType: subscriptionType,
            subscriptionExpiry: subscriptionExpiry,
          );
        } catch (e) {
          print('Failed to update subscription remotely: $e');
        }
      }
    } catch (e) {
      print('Error updating subscription: $e');
      rethrow;
    }
  }
  
  @override
  Future<List<UserModel>> getAllUsers() async {
    try {
      if (_isOnline) {
        // If online, get from remote and cache locally
        try {
          final remoteUsers = await _remoteRepository.getAllUsers();
          // Cache users locally (admin function, so might be many users)
          return remoteUsers;
        } catch (e) {
          print('Failed to get all users remotely, using local: $e');
        }
      }
      
      // Fallback to local
      return await _localRepository.getAllUsers();
    } catch (e) {
      print('Error getting all users: $e');
      rethrow;
    }
  }
  
  @override
  Future<List<UserModel>> searchUsersByName(String query) async {
    try {
      // Always search locally first for better performance
      final localResults = await _localRepository.searchUsersByName(query);
      
      if (_isOnline) {
        // If online, also search remotely and merge results
        try {
          final remoteResults = await _remoteRepository.searchUsersByName(query);
          // Merge and deduplicate results
          final allResults = [...localResults, ...remoteResults];
          final uniqueResults = <String, UserModel>{};
          for (final user in allResults) {
            uniqueResults[user.firebaseUid] = user;
          }
          return uniqueResults.values.toList();
        } catch (e) {
          print('Failed to search users remotely, using local: $e');
        }
      }
      
      return localResults;
    } catch (e) {
      print('Error searching users by name: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> syncUserData(String firebaseUid) async {
    try {
      if (!_isOnline) {
        print('Cannot sync user data: offline');
        return;
      }
      
      // Get local user
      final localUser = await _localRepository.getUserByFirebaseUid(firebaseUid);
      if (localUser == null) return;
      
      // Sync with remote
      try {
        final remoteUser = await _remoteRepository.getUserByFirebaseUid(firebaseUid);
        if (remoteUser != null) {
          // Compare timestamps and sync accordingly
          if (localUser.updatedAt.isAfter(remoteUser.updatedAt)) {
            // Local is newer, update remote
            await _remoteRepository.updateUser(localUser);
          } else if (remoteUser.updatedAt.isAfter(localUser.updatedAt)) {
            // Remote is newer, update local
            await _localRepository.updateUser(remoteUser);
          }
        } else {
          // User doesn't exist remotely, create it
          await _remoteRepository.createUser(localUser);
        }
        
        // Mark as synced
        await _localRepository.markUserAsSynced(firebaseUid);
      } catch (e) {
        print('Failed to sync user data: $e');
      }
    } catch (e) {
      print('Error syncing user data: $e');
    }
  }
  
  // Delegate remaining methods to local repository for now
  @override
  Future<List<UserModel>> getUsersNeedingSync() async {
    return await _localRepository.getUsersNeedingSync();
  }
  
  @override
  Future<void> markUserAsSynced(String firebaseUid) async {
    await _localRepository.markUserAsSynced(firebaseUid);
  }
  
  @override
  Future<Map<String, dynamic>> getUserStats(String firebaseUid) async {
    return await _localRepository.getUserStats(firebaseUid);
  }
  
  @override
  Future<Map<String, dynamic>> backupUserData(String firebaseUid) async {
    return await _localRepository.backupUserData(firebaseUid);
  }
  
  @override
  Future<void> restoreUserData(String firebaseUid, Map<String, dynamic> backup) async {
    await _localRepository.restoreUserData(firebaseUid, backup);
  }
  
  @override
  Future<void> clearAllUserData(String firebaseUid) async {
    await _localRepository.clearAllUserData(firebaseUid);
    if (_isOnline) {
      try {
        await _remoteRepository.clearAllUserData(firebaseUid);
      } catch (e) {
        print('Failed to clear user data remotely: $e');
      }
    }
  }
}
