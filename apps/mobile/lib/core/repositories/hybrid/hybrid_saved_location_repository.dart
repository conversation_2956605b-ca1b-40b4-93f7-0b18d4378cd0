import '../interfaces/saved_location_repository.dart';
import '../sqlite/sqlite_saved_location_repository.dart';
import '../firebase/firebase_saved_location_repository.dart';
import '../../database/models/saved_location_model.dart';
import '../../services/firebase_service.dart';

/// Hybrid repository that combines local SQLite and remote Firebase storage
/// Provides offline-first functionality with cloud synchronization for saved locations
class HybridSavedLocationRepository implements SavedLocationRepository {
  final SQLiteSavedLocationRepository _localRepository;
  final FirebaseSavedLocationRepository _remoteRepository;
  final FirebaseService _firebaseService;
  
  HybridSavedLocationRepository({
    required SQLiteSavedLocationRepository localRepository,
    required FirebaseSavedLocationRepository remoteRepository,
    required FirebaseService firebaseService,
  }) : _localRepository = localRepository,
       _remoteRepository = remoteRepository,
       _firebaseService = firebaseService;
  
  /// Check if user is online and authenticated
  bool get _isOnline => _firebaseService.isSignedIn;
  
  @override
  Future<List<SavedLocationModel>> getUserLocations(String userFirebaseUid) async {
    try {
      // Always get from local first for better performance
      final localLocations = await _localRepository.getUserLocations(userFirebaseUid);
      
      if (_isOnline) {
        // If online, try to sync with remote
        try {
          final remoteLocations = await _remoteRepository.getUserLocations(userFirebaseUid);
          // TODO: Implement proper sync logic
          // For now, return local locations
        } catch (e) {
          print('Failed to get locations from remote, using local: $e');
        }
      }
      
      return localLocations;
    } catch (e) {
      print('Error getting user locations: $e');
      rethrow;
    }
  }
  
  @override
  Future<SavedLocationModel?> getLocationById(int id) async {
    return await _localRepository.getLocationById(id);
  }
  
  @override
  Future<SavedLocationModel?> getLocationByFirebaseId(String firebaseId) async {
    // Try local first
    final localLocation = await _localRepository.getLocationByFirebaseId(firebaseId);
    
    if (localLocation != null) return localLocation;
    
    if (_isOnline) {
      // Try remote if not found locally
      try {
        final remoteLocation = await _remoteRepository.getLocationByFirebaseId(firebaseId);
        if (remoteLocation != null) {
          // Cache locally
          await _localRepository.createLocation(remoteLocation);
          return remoteLocation;
        }
      } catch (e) {
        print('Failed to get location from remote: $e');
      }
    }
    
    return null;
  }
  
  @override
  Future<SavedLocationModel> createLocation(SavedLocationModel location) async {
    try {
      // Always create locally first
      final localLocation = await _localRepository.createLocation(location);
      
      if (_isOnline) {
        // Try to create remotely
        try {
          final remoteLocation = await _remoteRepository.createLocation(localLocation);
          // Update local with remote data (like Firebase ID)
          await _localRepository.updateLocation(remoteLocation);
          return remoteLocation;
        } catch (e) {
          print('Failed to create location remotely, keeping local: $e');
        }
      }
      
      return localLocation;
    } catch (e) {
      print('Error creating location: $e');
      rethrow;
    }
  }
  
  @override
  Future<SavedLocationModel> updateLocation(SavedLocationModel location) async {
    try {
      // Always update locally first
      final localLocation = await _localRepository.updateLocation(location);
      
      if (_isOnline) {
        // Try to update remotely
        try {
          final remoteLocation = await _remoteRepository.updateLocation(localLocation);
          // Update local with any remote changes
          await _localRepository.updateLocation(remoteLocation);
          return remoteLocation;
        } catch (e) {
          print('Failed to update location remotely, keeping local: $e');
        }
      }
      
      return localLocation;
    } catch (e) {
      print('Error updating location: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> deleteLocation(int id) async {
    try {
      // Get location first to get Firebase ID if needed
      final location = await _localRepository.getLocationById(id);
      
      // Delete locally first
      await _localRepository.deleteLocation(id);
      
      if (_isOnline && location?.firebaseId != null) {
        // Try to delete remotely
        try {
          await _remoteRepository.deleteLocationByFirebaseId(location!.firebaseId!);
        } catch (e) {
          print('Failed to delete location remotely: $e');
        }
      }
    } catch (e) {
      print('Error deleting location: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> deleteLocationByFirebaseId(String firebaseId) async {
    try {
      // Delete locally first
      await _localRepository.deleteLocationByFirebaseId(firebaseId);
      
      if (_isOnline) {
        // Try to delete remotely
        try {
          await _remoteRepository.deleteLocationByFirebaseId(firebaseId);
        } catch (e) {
          print('Failed to delete location remotely: $e');
        }
      }
    } catch (e) {
      print('Error deleting location by Firebase ID: $e');
      rethrow;
    }
  }
  
  // For the remaining methods, delegate to local repository for now
  // In a full implementation, these would also have hybrid logic
  
  @override
  Future<List<SavedLocationModel>> getFavoriteLocations(String userFirebaseUid) async {
    return await _localRepository.getFavoriteLocations(userFirebaseUid);
  }
  
  @override
  Future<void> toggleFavorite(int id) async {
    await _localRepository.toggleFavorite(id);
    // TODO: Sync with remote
  }
  
  @override
  Future<List<SavedLocationModel>> getLocationsByType(
    String userFirebaseUid,
    LocationType type,
  ) async {
    return await _localRepository.getLocationsByType(userFirebaseUid, type);
  }
  
  @override
  Future<List<SavedLocationModel>> searchLocationsByName(
    String userFirebaseUid,
    String query,
  ) async {
    return await _localRepository.searchLocationsByName(userFirebaseUid, query);
  }
  
  @override
  Future<List<SavedLocationModel>> getNearbyLocations(
    String userFirebaseUid,
    double latitude,
    double longitude,
    double radiusKm,
  ) async {
    return await _localRepository.getNearbyLocations(
      userFirebaseUid,
      latitude,
      longitude,
      radiusKm,
    );
  }
  
  @override
  Future<List<SavedLocationModel>> getLocationsInBounds(
    String userFirebaseUid,
    double northLatitude,
    double southLatitude,
    double eastLongitude,
    double westLongitude,
  ) async {
    return await _localRepository.getLocationsInBounds(
      userFirebaseUid,
      northLatitude,
      southLatitude,
      eastLongitude,
      westLongitude,
    );
  }
  
  @override
  Future<List<SavedLocationModel>> createLocations(List<SavedLocationModel> locations) async {
    return await _localRepository.createLocations(locations);
  }
  
  @override
  Future<List<SavedLocationModel>> updateLocations(List<SavedLocationModel> locations) async {
    return await _localRepository.updateLocations(locations);
  }
  
  @override
  Future<void> deleteLocations(List<int> ids) async {
    await _localRepository.deleteLocations(ids);
  }
  
  @override
  Future<Map<String, dynamic>> getLocationStats(String userFirebaseUid) async {
    return await _localRepository.getLocationStats(userFirebaseUid);
  }
  
  @override
  Future<void> syncLocationData(String userFirebaseUid) async {
    // TODO: Implement proper sync logic
    await _localRepository.syncLocationData(userFirebaseUid);
  }
  
  @override
  Future<List<SavedLocationModel>> getLocationsNeedingSync(String userFirebaseUid) async {
    return await _localRepository.getLocationsNeedingSync(userFirebaseUid);
  }
  
  @override
  Future<void> markLocationAsSynced(int id) async {
    await _localRepository.markLocationAsSynced(id);
  }
  
  @override
  Future<List<SavedLocationModel>> importLocations(
    String userFirebaseUid,
    List<Map<String, dynamic>> locationData,
  ) async {
    return await _localRepository.importLocations(userFirebaseUid, locationData);
  }
  
  @override
  Future<List<Map<String, dynamic>>> exportLocations(String userFirebaseUid) async {
    return await _localRepository.exportLocations(userFirebaseUid);
  }
  
  @override
  Future<void> clearUserLocations(String userFirebaseUid) async {
    await _localRepository.clearUserLocations(userFirebaseUid);
    if (_isOnline) {
      try {
        await _remoteRepository.clearUserLocations(userFirebaseUid);
      } catch (e) {
        print('Failed to clear user locations remotely: $e');
      }
    }
  }
  
  @override
  Future<List<SavedLocationModel>> getMostVisitedLocations(
    String userFirebaseUid, {
    int limit = 10,
  }) async {
    return await _localRepository.getMostVisitedLocations(userFirebaseUid, limit: limit);
  }
  
  @override
  Future<List<SavedLocationModel>> getRecentlyAddedLocations(
    String userFirebaseUid, {
    int limit = 10,
  }) async {
    return await _localRepository.getRecentlyAddedLocations(userFirebaseUid, limit: limit);
  }
  
  @override
  Future<List<SavedLocationModel>> getLocationsByDistance(
    String userFirebaseUid,
    double latitude,
    double longitude, {
    int? limit,
  }) async {
    return await _localRepository.getLocationsByDistance(
      userFirebaseUid,
      latitude,
      longitude,
      limit: limit,
    );
  }
  
  @override
  Future<bool> locationExistsAtCoordinates(
    String userFirebaseUid,
    double latitude,
    double longitude, {
    double toleranceMeters = 100,
  }) async {
    return await _localRepository.locationExistsAtCoordinates(
      userFirebaseUid,
      latitude,
      longitude,
      toleranceMeters: toleranceMeters,
    );
  }
  
  @override
  Future<Map<String, int>> getLocationDensity(
    String userFirebaseUid,
    double centerLatitude,
    double centerLongitude,
    double radiusKm,
    int gridSize,
  ) async {
    return await _localRepository.getLocationDensity(
      userFirebaseUid,
      centerLatitude,
      centerLongitude,
      radiusKm,
      gridSize,
    );
  }
}
