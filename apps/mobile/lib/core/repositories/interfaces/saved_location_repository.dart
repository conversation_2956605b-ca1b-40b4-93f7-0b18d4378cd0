import '../../database/models/saved_location_model.dart';

/// Abstract repository interface for saved location data operations
/// Provides a contract for both Firebase and SQLite implementations
abstract class SavedLocationRepository {
  /// Get all saved locations for a user
  Future<List<SavedLocationModel>> getUserLocations(String userFirebaseUid);
  
  /// Get saved location by ID
  Future<SavedLocationModel?> getLocationById(int id);
  
  /// Get saved location by Firebase ID
  Future<SavedLocationModel?> getLocationByFirebaseId(String firebaseId);
  
  /// Create new saved location
  Future<SavedLocationModel> createLocation(SavedLocationModel location);
  
  /// Update existing saved location
  Future<SavedLocationModel> updateLocation(SavedLocationModel location);
  
  /// Delete saved location
  Future<void> deleteLocation(int id);
  
  /// Delete location by Firebase ID
  Future<void> deleteLocationByFirebaseId(String firebaseId);
  
  /// Get favorite locations for a user
  Future<List<SavedLocationModel>> getFavoriteLocations(String userFirebaseUid);
  
  /// Toggle favorite status of a location
  Future<void> toggleFavorite(int id);
  
  /// Get locations by type
  Future<List<SavedLocationModel>> getLocationsByType(
    String userFirebaseUid,
    LocationType type,
  );
  
  /// Search locations by name
  Future<List<SavedLocationModel>> searchLocationsByName(
    String userFirebaseUid,
    String query,
  );
  
  /// Get nearby locations within radius (in kilometers)
  Future<List<SavedLocationModel>> getNearbyLocations(
    String userFirebaseUid,
    double latitude,
    double longitude,
    double radiusKm,
  );
  
  /// Get locations within bounding box
  Future<List<SavedLocationModel>> getLocationsInBounds(
    String userFirebaseUid,
    double northLatitude,
    double southLatitude,
    double eastLongitude,
    double westLongitude,
  );
  
  /// Bulk create locations
  Future<List<SavedLocationModel>> createLocations(List<SavedLocationModel> locations);
  
  /// Bulk update locations
  Future<List<SavedLocationModel>> updateLocations(List<SavedLocationModel> locations);
  
  /// Bulk delete locations
  Future<void> deleteLocations(List<int> ids);
  
  /// Get location statistics for a user
  Future<Map<String, dynamic>> getLocationStats(String userFirebaseUid);
  
  /// Sync location data between local and remote
  Future<void> syncLocationData(String userFirebaseUid);
  
  /// Get locations that need sync (local changes not synced)
  Future<List<SavedLocationModel>> getLocationsNeedingSync(String userFirebaseUid);
  
  /// Mark location as synced
  Future<void> markLocationAsSynced(int id);
  
  /// Import locations from external source
  Future<List<SavedLocationModel>> importLocations(
    String userFirebaseUid,
    List<Map<String, dynamic>> locationData,
  );
  
  /// Export locations to external format
  Future<List<Map<String, dynamic>>> exportLocations(String userFirebaseUid);
  
  /// Clear all locations for a user
  Future<void> clearUserLocations(String userFirebaseUid);
  
  /// Get most visited locations
  Future<List<SavedLocationModel>> getMostVisitedLocations(
    String userFirebaseUid, {
    int limit = 10,
  });
  
  /// Get recently added locations
  Future<List<SavedLocationModel>> getRecentlyAddedLocations(
    String userFirebaseUid, {
    int limit = 10,
  });
  
  /// Get locations by distance from a point (sorted by distance)
  Future<List<SavedLocationModel>> getLocationsByDistance(
    String userFirebaseUid,
    double latitude,
    double longitude, {
    int? limit,
  });
  
  /// Check if location exists at coordinates (within tolerance)
  Future<bool> locationExistsAtCoordinates(
    String userFirebaseUid,
    double latitude,
    double longitude, {
    double toleranceMeters = 100,
  });
  
  /// Get location density in an area (for heatmap)
  Future<Map<String, int>> getLocationDensity(
    String userFirebaseUid,
    double centerLatitude,
    double centerLongitude,
    double radiusKm,
    int gridSize,
  );
}
