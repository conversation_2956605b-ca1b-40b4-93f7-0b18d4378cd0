import '../../database/models/user_model.dart';

/// Abstract repository interface for user data operations
/// Provides a contract for both Firebase and SQLite implementations
abstract class UserRepository {
  /// Get user by Firebase UID
  Future<UserModel?> getUserByFirebaseUid(String firebaseUid);
  
  /// Get user by email
  Future<UserModel?> getUserByEmail(String email);
  
  /// Create new user
  Future<UserModel> createUser(UserModel user);
  
  /// Update existing user
  Future<UserModel> updateUser(UserModel user);
  
  /// Delete user
  Future<void> deleteUser(String firebaseUid);
  
  /// Check if user exists
  Future<bool> userExists(String firebaseUid);
  
  /// Update user preferences
  Future<void> updateUserPreferences({
    required String firebaseUid,
    String? language,
    bool? prayerNotifications,
    bool? locationSharing,
    bool? highContrast,
  });
  
  /// Update user subscription status
  Future<void> updateSubscription({
    required String firebaseUid,
    required bool isPremium,
    String? subscriptionType,
    DateTime? subscriptionExpiry,
  });
  
  /// Get all users (admin function)
  Future<List<UserModel>> getAllUsers();
  
  /// Search users by display name
  Future<List<UserModel>> searchUsersByName(String query);
  
  /// Sync user data between local and remote
  Future<void> syncUserData(String firebaseUid);
  
  /// Get users who need sync (local changes not synced)
  Future<List<UserModel>> getUsersNeedingSync();
  
  /// Mark user as synced
  Future<void> markUserAsSynced(String firebaseUid);
  
  /// Get user statistics
  Future<Map<String, dynamic>> getUserStats(String firebaseUid);
  
  /// Backup user data
  Future<Map<String, dynamic>> backupUserData(String firebaseUid);
  
  /// Restore user data from backup
  Future<void> restoreUserData(String firebaseUid, Map<String, dynamic> backup);
  
  /// Clear all user data (for account deletion)
  Future<void> clearAllUserData(String firebaseUid);
}
