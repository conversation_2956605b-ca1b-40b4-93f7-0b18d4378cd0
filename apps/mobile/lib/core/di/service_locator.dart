import 'package:get_it/get_it.dart';
import '../database/database_helper.dart';
import '../services/firebase_service.dart';
import '../repositories/interfaces/user_repository.dart';
import '../repositories/interfaces/saved_location_repository.dart';
import '../repositories/sqlite/sqlite_user_repository.dart';
import '../repositories/sqlite/sqlite_saved_location_repository.dart';
import '../repositories/firebase/firebase_user_repository.dart';
import '../repositories/firebase/firebase_saved_location_repository.dart';
import '../repositories/hybrid/hybrid_user_repository.dart';
import '../repositories/hybrid/hybrid_saved_location_repository.dart';

/// Service locator for dependency injection
/// Manages all app dependencies and their lifecycles
final GetIt serviceLocator = GetIt.instance;

/// Initialize all dependencies
Future<void> setupServiceLocator() async {
  print('Setting up service locator...');
  
  // Core services
  await _setupCoreServices();
  
  // Database services
  await _setupDatabaseServices();
  
  // Repository services
  await _setupRepositoryServices();
  
  print('Service locator setup completed');
}

/// Setup core services (Firebase, etc.)
Future<void> _setupCoreServices() async {
  // Firebase service (singleton)
  serviceLocator.registerSingleton<FirebaseService>(
    FirebaseService.instance,
  );
}

/// Setup database services
Future<void> _setupDatabaseServices() async {
  // Database helper (singleton)
  serviceLocator.registerSingleton<DatabaseHelper>(
    DatabaseHelper.instance,
  );
}

/// Setup repository services
Future<void> _setupRepositoryServices() async {
  // SQLite repositories
  serviceLocator.registerLazySingleton<SQLiteUserRepository>(
    () => SQLiteUserRepository(serviceLocator<DatabaseHelper>()),
  );
  
  serviceLocator.registerLazySingleton<SQLiteSavedLocationRepository>(
    () => SQLiteSavedLocationRepository(serviceLocator<DatabaseHelper>()),
  );
  
  // Firebase repositories (when implemented)
  serviceLocator.registerLazySingleton<FirebaseUserRepository>(
    () => FirebaseUserRepository(serviceLocator<FirebaseService>()),
  );
  
  serviceLocator.registerLazySingleton<FirebaseSavedLocationRepository>(
    () => FirebaseSavedLocationRepository(serviceLocator<FirebaseService>()),
  );
  
  // Hybrid repositories (combines local and remote)
  serviceLocator.registerLazySingleton<UserRepository>(
    () => HybridUserRepository(
      localRepository: serviceLocator<SQLiteUserRepository>(),
      remoteRepository: serviceLocator<FirebaseUserRepository>(),
      firebaseService: serviceLocator<FirebaseService>(),
    ),
  );
  
  serviceLocator.registerLazySingleton<SavedLocationRepository>(
    () => HybridSavedLocationRepository(
      localRepository: serviceLocator<SQLiteSavedLocationRepository>(),
      remoteRepository: serviceLocator<FirebaseSavedLocationRepository>(),
      firebaseService: serviceLocator<FirebaseService>(),
    ),
  );
}

/// Reset service locator (for testing)
Future<void> resetServiceLocator() async {
  await serviceLocator.reset();
  print('Service locator reset');
}

/// Get service instance
T getService<T extends Object>() {
  return serviceLocator<T>();
}

/// Check if service is registered
bool isServiceRegistered<T extends Object>() {
  return serviceLocator.isRegistered<T>();
}

/// Repository factory for different data sources
class RepositoryFactory {
  /// Get user repository based on configuration
  static UserRepository getUserRepository({
    DataSource dataSource = DataSource.hybrid,
  }) {
    switch (dataSource) {
      case DataSource.local:
        return serviceLocator<SQLiteUserRepository>();
      case DataSource.remote:
        return serviceLocator<FirebaseUserRepository>();
      case DataSource.hybrid:
        return serviceLocator<UserRepository>();
    }
  }
  
  /// Get saved location repository based on configuration
  static SavedLocationRepository getSavedLocationRepository({
    DataSource dataSource = DataSource.hybrid,
  }) {
    switch (dataSource) {
      case DataSource.local:
        return serviceLocator<SQLiteSavedLocationRepository>();
      case DataSource.remote:
        return serviceLocator<FirebaseSavedLocationRepository>();
      case DataSource.hybrid:
        return serviceLocator<SavedLocationRepository>();
    }
  }
}

/// Data source options
enum DataSource {
  local,   // SQLite only
  remote,  // Firebase only
  hybrid,  // Both (default)
}

/// Service locator configuration
class ServiceLocatorConfig {
  static bool _isInitialized = false;
  
  /// Check if service locator is initialized
  static bool get isInitialized => _isInitialized;
  
  /// Initialize service locator with configuration
  static Future<void> initialize({
    bool enableFirebase = true,
    bool enableSQLite = true,
    bool enableHybridMode = true,
  }) async {
    if (_isInitialized) {
      print('Service locator already initialized');
      return;
    }
    
    try {
      await setupServiceLocator();
      _isInitialized = true;
      print('Service locator initialized successfully');
    } catch (e) {
      print('Error initializing service locator: $e');
      rethrow;
    }
  }
  
  /// Cleanup service locator
  static Future<void> cleanup() async {
    if (!_isInitialized) return;
    
    try {
      await resetServiceLocator();
      _isInitialized = false;
      print('Service locator cleaned up');
    } catch (e) {
      print('Error cleaning up service locator: $e');
    }
  }
}

/// Service health checker
class ServiceHealthChecker {
  /// Check if all critical services are healthy
  static Future<Map<String, bool>> checkServiceHealth() async {
    final health = <String, bool>{};
    
    try {
      // Check database health
      final dbHelper = serviceLocator<DatabaseHelper>();
      final dbInfo = await dbHelper.getDatabaseInfo();
      health['database'] = dbInfo.isNotEmpty;
      
      // Check Firebase health
      final firebaseService = serviceLocator<FirebaseService>();
      health['firebase'] = firebaseService.isSignedIn || true; // Allow offline
      
      // Check repositories
      health['user_repository'] = serviceLocator.isRegistered<UserRepository>();
      health['location_repository'] = serviceLocator.isRegistered<SavedLocationRepository>();
      
    } catch (e) {
      print('Error checking service health: $e');
      health['error'] = false;
    }
    
    return health;
  }
  
  /// Get service statistics
  static Map<String, dynamic> getServiceStats() {
    try {
      return {
        'registered_services': 'N/A', // GetIt doesn't provide easy count access
        'is_initialized': ServiceLocatorConfig.isInitialized,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}

/// Extension methods for easier service access
extension ServiceLocatorExtension on GetIt {
  /// Get user repository
  UserRepository get userRepository => get<UserRepository>();
  
  /// Get saved location repository
  SavedLocationRepository get savedLocationRepository => get<SavedLocationRepository>();
  
  /// Get database helper
  DatabaseHelper get databaseHelper => get<DatabaseHelper>();
  
  /// Get Firebase service
  FirebaseService get firebaseService => get<FirebaseService>();
}
