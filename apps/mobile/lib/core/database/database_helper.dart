import 'dart:async';
import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

/// SQLite database helper for local data storage
/// Handles offline data for Islamic features like prayer times, saved locations, etc.
class DatabaseHelper {
  static DatabaseHelper? _instance;
  static Database? _database;
  
  static DatabaseHelper get instance => _instance ??= DatabaseHelper._();
  
  DatabaseHelper._();
  
  /// Database configuration
  static const String _databaseName = 'ziarah_islamic_companion.db';
  static const int _databaseVersion = 1;
  
  /// Table names
  static const String usersTable = 'users';
  static const String savedLocationsTable = 'saved_locations';
  static const String prayerTimesTable = 'prayer_times';
  static const String offlineContentTable = 'offline_content';
  static const String familyGroupsTable = 'family_groups';
  static const String familyMembersTable = 'family_members';
  
  /// Get database instance
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }
  
  /// Initialize database
  Future<Database> _initDatabase() async {
    try {
      // Get the documents directory path
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String path = join(documentsDirectory.path, _databaseName);
      
      // Open the database
      return await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        onConfigure: _onConfigure,
      );
    } catch (e) {
      print('Error initializing database: $e');
      rethrow;
    }
  }
  
  /// Configure database settings
  Future<void> _onConfigure(Database db) async {
    // Enable foreign key constraints
    await db.execute('PRAGMA foreign_keys = ON');
    
    // Enable WAL mode for better performance
    await db.execute('PRAGMA journal_mode = WAL');
    
    // Set cache size for better performance
    await db.execute('PRAGMA cache_size = 10000');
  }
  
  /// Create database tables
  Future<void> _onCreate(Database db, int version) async {
    await _createUsersTable(db);
    await _createSavedLocationsTable(db);
    await _createPrayerTimesTable(db);
    await _createOfflineContentTable(db);
    await _createFamilyGroupsTable(db);
    await _createFamilyMembersTable(db);
    
    // Create indexes for better query performance
    await _createIndexes(db);
    
    print('Database tables created successfully');
  }
  
  /// Handle database upgrades
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database migrations here
    print('Upgrading database from version $oldVersion to $newVersion');
    
    // Example migration logic:
    // if (oldVersion < 2) {
    //   await db.execute('ALTER TABLE users ADD COLUMN new_field TEXT');
    // }
  }
  
  /// Create users table for local user data caching
  Future<void> _createUsersTable(Database db) async {
    await db.execute('''
      CREATE TABLE $usersTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        firebase_uid TEXT UNIQUE NOT NULL,
        email TEXT,
        display_name TEXT,
        photo_url TEXT,
        is_premium INTEGER DEFAULT 0,
        subscription_type TEXT,
        subscription_expiry INTEGER,
        language TEXT DEFAULT 'en',
        prayer_notifications INTEGER DEFAULT 1,
        location_sharing INTEGER DEFAULT 0,
        high_contrast INTEGER DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        last_sync INTEGER
      )
    ''');
  }
  
  /// Create saved locations table for offline access
  Future<void> _createSavedLocationsTable(Database db) async {
    await db.execute('''
      CREATE TABLE $savedLocationsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        firebase_id TEXT UNIQUE,
        user_firebase_uid TEXT NOT NULL,
        name TEXT NOT NULL,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        type TEXT NOT NULL,
        notes TEXT,
        address TEXT,
        is_favorite INTEGER DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        last_sync INTEGER,
        FOREIGN KEY (user_firebase_uid) REFERENCES $usersTable (firebase_uid) ON DELETE CASCADE
      )
    ''');
  }
  
  /// Create prayer times table for offline prayer time data
  Future<void> _createPrayerTimesTable(Database db) async {
    await db.execute('''
      CREATE TABLE $prayerTimesTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT NOT NULL,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        fajr TEXT NOT NULL,
        sunrise TEXT NOT NULL,
        dhuhr TEXT NOT NULL,
        asr TEXT NOT NULL,
        maghrib TEXT NOT NULL,
        isha TEXT NOT NULL,
        qibla_direction REAL,
        calculation_method TEXT DEFAULT 'MWL',
        created_at INTEGER NOT NULL,
        UNIQUE(date, latitude, longitude)
      )
    ''');
  }
  
  /// Create offline content table for Islamic content caching
  Future<void> _createOfflineContentTable(Database db) async {
    await db.execute('''
      CREATE TABLE $offlineContentTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        content_type TEXT NOT NULL,
        content_id TEXT NOT NULL,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        language TEXT DEFAULT 'en',
        is_premium INTEGER DEFAULT 0,
        file_path TEXT,
        file_size INTEGER,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        expires_at INTEGER,
        UNIQUE(content_type, content_id, language)
      )
    ''');
  }
  
  /// Create family groups table for family sharing features
  Future<void> _createFamilyGroupsTable(Database db) async {
    await db.execute('''
      CREATE TABLE $familyGroupsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        firebase_id TEXT UNIQUE,
        name TEXT NOT NULL,
        created_by_uid TEXT NOT NULL,
        invite_code TEXT UNIQUE,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        last_sync INTEGER,
        FOREIGN KEY (created_by_uid) REFERENCES $usersTable (firebase_uid) ON DELETE CASCADE
      )
    ''');
  }
  
  /// Create family members table for group membership
  Future<void> _createFamilyMembersTable(Database db) async {
    await db.execute('''
      CREATE TABLE $familyMembersTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        group_id INTEGER NOT NULL,
        user_firebase_uid TEXT NOT NULL,
        role TEXT DEFAULT 'member',
        joined_at INTEGER NOT NULL,
        FOREIGN KEY (group_id) REFERENCES $familyGroupsTable (id) ON DELETE CASCADE,
        FOREIGN KEY (user_firebase_uid) REFERENCES $usersTable (firebase_uid) ON DELETE CASCADE,
        UNIQUE(group_id, user_firebase_uid)
      )
    ''');
  }
  
  /// Create database indexes for better performance
  Future<void> _createIndexes(Database db) async {
    // Users table indexes
    await db.execute('CREATE INDEX idx_users_firebase_uid ON $usersTable (firebase_uid)');
    await db.execute('CREATE INDEX idx_users_email ON $usersTable (email)');
    
    // Saved locations indexes
    await db.execute('CREATE INDEX idx_locations_user_uid ON $savedLocationsTable (user_firebase_uid)');
    await db.execute('CREATE INDEX idx_locations_type ON $savedLocationsTable (type)');
    await db.execute('CREATE INDEX idx_locations_favorite ON $savedLocationsTable (is_favorite)');
    await db.execute('CREATE INDEX idx_locations_coords ON $savedLocationsTable (latitude, longitude)');
    
    // Prayer times indexes
    await db.execute('CREATE INDEX idx_prayer_times_date ON $prayerTimesTable (date)');
    await db.execute('CREATE INDEX idx_prayer_times_coords ON $prayerTimesTable (latitude, longitude)');
    
    // Offline content indexes
    await db.execute('CREATE INDEX idx_content_type_id ON $offlineContentTable (content_type, content_id)');
    await db.execute('CREATE INDEX idx_content_premium ON $offlineContentTable (is_premium)');
    await db.execute('CREATE INDEX idx_content_expires ON $offlineContentTable (expires_at)');
    
    // Family groups indexes
    await db.execute('CREATE INDEX idx_groups_created_by ON $familyGroupsTable (created_by_uid)');
    await db.execute('CREATE INDEX idx_groups_invite_code ON $familyGroupsTable (invite_code)');
    
    // Family members indexes
    await db.execute('CREATE INDEX idx_members_group_id ON $familyMembersTable (group_id)');
    await db.execute('CREATE INDEX idx_members_user_uid ON $familyMembersTable (user_firebase_uid)');
  }
  
  /// Close database connection
  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }
  
  /// Delete database (for testing or reset)
  Future<void> deleteDatabase() async {
    try {
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String path = join(documentsDirectory.path, _databaseName);
      
      if (await File(path).exists()) {
        await File(path).delete();
        _database = null;
        print('Database deleted successfully');
      }
    } catch (e) {
      print('Error deleting database: $e');
      rethrow;
    }
  }
  
  /// Get database file size
  Future<int> getDatabaseSize() async {
    try {
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String path = join(documentsDirectory.path, _databaseName);
      
      if (await File(path).exists()) {
        return await File(path).length();
      }
      return 0;
    } catch (e) {
      print('Error getting database size: $e');
      return 0;
    }
  }
  
  /// Vacuum database to reclaim space
  Future<void> vacuum() async {
    try {
      final db = await database;
      await db.execute('VACUUM');
      print('Database vacuumed successfully');
    } catch (e) {
      print('Error vacuuming database: $e');
    }
  }
  
  /// Get database info for debugging
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    try {
      final db = await database;
      final size = await getDatabaseSize();
      
      // Get table counts
      final userCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM $usersTable')
      ) ?? 0;
      
      final locationCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM $savedLocationsTable')
      ) ?? 0;
      
      final prayerTimesCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM $prayerTimesTable')
      ) ?? 0;
      
      return {
        'database_name': _databaseName,
        'database_version': _databaseVersion,
        'database_size_bytes': size,
        'user_count': userCount,
        'location_count': locationCount,
        'prayer_times_count': prayerTimesCount,
      };
    } catch (e) {
      print('Error getting database info: $e');
      return {};
    }
  }
}
