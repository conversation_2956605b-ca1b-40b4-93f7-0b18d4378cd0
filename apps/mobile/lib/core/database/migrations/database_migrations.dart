import 'package:sqflite/sqflite.dart';

/// Database migration manager for handling schema changes
class DatabaseMigrations {
  /// Execute all migrations from oldVersion to newVersion
  static Future<void> migrate(Database db, int oldVersion, int newVersion) async {
    print('Migrating database from version $oldVersion to $newVersion');
    
    // Execute migrations sequentially
    for (int version = oldVersion + 1; version <= newVersion; version++) {
      await _executeMigration(db, version);
    }
    
    print('Database migration completed successfully');
  }
  
  /// Execute specific migration for a version
  static Future<void> _executeMigration(Database db, int version) async {
    switch (version) {
      case 2:
        await _migrateTo2(db);
        break;
      case 3:
        await _migrateTo3(db);
        break;
      // Add more migrations as needed
      default:
        print('No migration defined for version $version');
    }
  }
  
  /// Migration to version 2 - Example: Add new columns
  static Future<void> _migrateTo2(Database db) async {
    print('Executing migration to version 2');
    
    try {
      // Example: Add new columns to users table
      await db.execute('''
        ALTER TABLE users ADD COLUMN theme_preference TEXT DEFAULT 'system'
      ''');
      
      await db.execute('''
        ALTER TABLE users ADD COLUMN notification_sound TEXT DEFAULT 'default'
      ''');
      
      // Example: Add new table for prayer time notifications
      await db.execute('''
        CREATE TABLE prayer_notifications (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_firebase_uid TEXT NOT NULL,
          prayer_name TEXT NOT NULL,
          notification_time INTEGER NOT NULL,
          is_enabled INTEGER DEFAULT 1,
          sound_file TEXT,
          vibration_pattern TEXT,
          created_at INTEGER NOT NULL,
          FOREIGN KEY (user_firebase_uid) REFERENCES users (firebase_uid) ON DELETE CASCADE
        )
      ''');
      
      // Create index for the new table
      await db.execute('''
        CREATE INDEX idx_prayer_notifications_user ON prayer_notifications (user_firebase_uid)
      ''');
      
      print('Migration to version 2 completed');
    } catch (e) {
      print('Error in migration to version 2: $e');
      rethrow;
    }
  }
  
  /// Migration to version 3 - Example: Add family sharing features
  static Future<void> _migrateTo3(Database db) async {
    print('Executing migration to version 3');
    
    try {
      // Add family sharing preferences to users table
      await db.execute('''
        ALTER TABLE users ADD COLUMN family_sharing_enabled INTEGER DEFAULT 0
      ''');
      
      await db.execute('''
        ALTER TABLE users ADD COLUMN location_sharing_radius REAL DEFAULT 1000.0
      ''');
      
      // Add shared location tracking table
      await db.execute('''
        CREATE TABLE shared_locations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          group_id INTEGER NOT NULL,
          user_firebase_uid TEXT NOT NULL,
          latitude REAL NOT NULL,
          longitude REAL NOT NULL,
          accuracy REAL,
          timestamp INTEGER NOT NULL,
          is_prayer_time INTEGER DEFAULT 0,
          FOREIGN KEY (group_id) REFERENCES family_groups (id) ON DELETE CASCADE,
          FOREIGN KEY (user_firebase_uid) REFERENCES users (firebase_uid) ON DELETE CASCADE
        )
      ''');
      
      // Create indexes for shared locations
      await db.execute('''
        CREATE INDEX idx_shared_locations_group ON shared_locations (group_id)
      ''');
      
      await db.execute('''
        CREATE INDEX idx_shared_locations_timestamp ON shared_locations (timestamp)
      ''');
      
      // Add emergency contact table
      await db.execute('''
        CREATE TABLE emergency_contacts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_firebase_uid TEXT NOT NULL,
          contact_name TEXT NOT NULL,
          contact_phone TEXT NOT NULL,
          relationship TEXT,
          is_primary INTEGER DEFAULT 0,
          created_at INTEGER NOT NULL,
          FOREIGN KEY (user_firebase_uid) REFERENCES users (firebase_uid) ON DELETE CASCADE
        )
      ''');
      
      await db.execute('''
        CREATE INDEX idx_emergency_contacts_user ON emergency_contacts (user_firebase_uid)
      ''');
      
      print('Migration to version 3 completed');
    } catch (e) {
      print('Error in migration to version 3: $e');
      rethrow;
    }
  }
  
  /// Rollback migration (for development/testing)
  static Future<void> rollback(Database db, int fromVersion, int toVersion) async {
    print('Rolling back database from version $fromVersion to $toVersion');
    
    // Note: SQLite doesn't support DROP COLUMN, so rollbacks are limited
    // In production, consider creating a new table and copying data
    
    try {
      if (fromVersion >= 3 && toVersion < 3) {
        await _rollbackFrom3(db);
      }
      
      if (fromVersion >= 2 && toVersion < 2) {
        await _rollbackFrom2(db);
      }
      
      print('Database rollback completed');
    } catch (e) {
      print('Error during rollback: $e');
      rethrow;
    }
  }
  
  /// Rollback from version 3
  static Future<void> _rollbackFrom3(Database db) async {
    print('Rolling back from version 3');
    
    try {
      // Drop tables added in version 3
      await db.execute('DROP TABLE IF EXISTS shared_locations');
      await db.execute('DROP TABLE IF EXISTS emergency_contacts');
      
      // Note: Cannot drop columns in SQLite, they will remain but unused
      print('Rollback from version 3 completed');
    } catch (e) {
      print('Error rolling back from version 3: $e');
      rethrow;
    }
  }
  
  /// Rollback from version 2
  static Future<void> _rollbackFrom2(Database db) async {
    print('Rolling back from version 2');
    
    try {
      // Drop tables added in version 2
      await db.execute('DROP TABLE IF EXISTS prayer_notifications');
      
      // Note: Cannot drop columns in SQLite, they will remain but unused
      print('Rollback from version 2 completed');
    } catch (e) {
      print('Error rolling back from version 2: $e');
      rethrow;
    }
  }
  
  /// Check if migration is needed
  static bool isMigrationNeeded(int currentVersion, int targetVersion) {
    return currentVersion < targetVersion;
  }
  
  /// Get list of available migrations
  static List<int> getAvailableMigrations() {
    return [2, 3]; // Add new migration versions here
  }
  
  /// Validate database schema after migration
  static Future<bool> validateSchema(Database db, int expectedVersion) async {
    try {
      // Check if all expected tables exist
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
      );
      
      final tableNames = tables.map((table) => table['name'] as String).toSet();
      
      // Base tables (version 1)
      final requiredTables = {
        'users',
        'saved_locations',
        'prayer_times',
        'offline_content',
        'family_groups',
        'family_members',
      };
      
      // Additional tables for version 2
      if (expectedVersion >= 2) {
        requiredTables.add('prayer_notifications');
      }
      
      // Additional tables for version 3
      if (expectedVersion >= 3) {
        requiredTables.addAll(['shared_locations', 'emergency_contacts']);
      }
      
      // Check if all required tables exist
      for (final table in requiredTables) {
        if (!tableNames.contains(table)) {
          print('Missing table: $table');
          return false;
        }
      }
      
      print('Database schema validation passed for version $expectedVersion');
      return true;
    } catch (e) {
      print('Error validating database schema: $e');
      return false;
    }
  }
  
  /// Get current database version
  static Future<int> getCurrentVersion(Database db) async {
    try {
      final result = await db.rawQuery('PRAGMA user_version');
      return result.first['user_version'] as int;
    } catch (e) {
      print('Error getting database version: $e');
      return 0;
    }
  }
  
  /// Set database version
  static Future<void> setVersion(Database db, int version) async {
    try {
      await db.rawQuery('PRAGMA user_version = $version');
      print('Database version set to $version');
    } catch (e) {
      print('Error setting database version: $e');
      rethrow;
    }
  }
}
