import 'dart:math' as math;

/// Location types for Islamic pilgrimage
enum LocationType {
  mosque('mosque'),
  hotel('hotel'),
  restaurant('restaurant'),
  hospital('hospital'),
  shopping('shopping'),
  transport('transport'),
  historical('historical'),
  other('other');

  const LocationType(this.value);
  final String value;

  static LocationType fromString(String value) {
    return LocationType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => LocationType.other,
    );
  }
}

/// Local saved location model for SQLite database
class SavedLocationModel {
  final int? id;
  final String? firebaseId;
  final String userFirebaseUid;
  final String name;
  final double latitude;
  final double longitude;
  final LocationType type;
  final String? notes;
  final String? address;
  final bool isFavorite;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastSync;

  const SavedLocationModel({
    this.id,
    this.firebaseId,
    required this.userFirebaseUid,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.type,
    this.notes,
    this.address,
    this.isFavorite = false,
    required this.createdAt,
    required this.updatedAt,
    this.lastSync,
  });

  /// Create SavedLocationModel from database map
  factory SavedLocationModel.fromMap(Map<String, dynamic> map) {
    return SavedLocationModel(
      id: map['id'] as int?,
      firebaseId: map['firebase_id'] as String?,
      userFirebaseUid: map['user_firebase_uid'] as String,
      name: map['name'] as String,
      latitude: map['latitude'] as double,
      longitude: map['longitude'] as double,
      type: LocationType.fromString(map['type'] as String),
      notes: map['notes'] as String?,
      address: map['address'] as String?,
      isFavorite: (map['is_favorite'] as int? ?? 0) == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
      lastSync: map['last_sync'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['last_sync'] as int)
          : null,
    );
  }

  /// Convert SavedLocationModel to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'firebase_id': firebaseId,
      'user_firebase_uid': userFirebaseUid,
      'name': name,
      'latitude': latitude,
      'longitude': longitude,
      'type': type.value,
      'notes': notes,
      'address': address,
      'is_favorite': isFavorite ? 1 : 0,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'last_sync': lastSync?.millisecondsSinceEpoch,
    };
  }

  /// Create a copy with updated fields
  SavedLocationModel copyWith({
    int? id,
    String? firebaseId,
    String? userFirebaseUid,
    String? name,
    double? latitude,
    double? longitude,
    LocationType? type,
    String? notes,
    String? address,
    bool? isFavorite,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastSync,
  }) {
    return SavedLocationModel(
      id: id ?? this.id,
      firebaseId: firebaseId ?? this.firebaseId,
      userFirebaseUid: userFirebaseUid ?? this.userFirebaseUid,
      name: name ?? this.name,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      type: type ?? this.type,
      notes: notes ?? this.notes,
      address: address ?? this.address,
      isFavorite: isFavorite ?? this.isFavorite,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastSync: lastSync ?? this.lastSync,
    );
  }

  /// Calculate distance to another location in kilometers
  double distanceTo(double otherLatitude, double otherLongitude) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    double lat1Rad = latitude * (3.14159265359 / 180);
    double lat2Rad = otherLatitude * (3.14159265359 / 180);
    double deltaLatRad = (otherLatitude - latitude) * (3.14159265359 / 180);
    double deltaLonRad = (otherLongitude - longitude) * (3.14159265359 / 180);

    double a = math.sin(deltaLatRad / 2) * math.sin(deltaLatRad / 2) +
        math.cos(lat1Rad) * math.cos(lat2Rad) *
        math.sin(deltaLonRad / 2) * math.sin(deltaLonRad / 2);

    double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));

    return earthRadius * c;
  }

  /// Get location type display name
  String get typeDisplayName {
    switch (type) {
      case LocationType.mosque:
        return 'Mosque';
      case LocationType.hotel:
        return 'Hotel';
      case LocationType.restaurant:
        return 'Restaurant';
      case LocationType.hospital:
        return 'Hospital';
      case LocationType.shopping:
        return 'Shopping';
      case LocationType.transport:
        return 'Transport';
      case LocationType.historical:
        return 'Historical Site';
      case LocationType.other:
        return 'Other';
    }
  }

  /// Get location type icon name
  String get typeIconName {
    switch (type) {
      case LocationType.mosque:
        return 'mosque';
      case LocationType.hotel:
        return 'hotel';
      case LocationType.restaurant:
        return 'restaurant';
      case LocationType.hospital:
        return 'local_hospital';
      case LocationType.shopping:
        return 'shopping_cart';
      case LocationType.transport:
        return 'directions_bus';
      case LocationType.historical:
        return 'account_balance';
      case LocationType.other:
        return 'place';
    }
  }

  @override
  String toString() {
    return 'SavedLocationModel{id: $id, name: $name, type: ${type.value}, '
        'latitude: $latitude, longitude: $longitude, isFavorite: $isFavorite}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SavedLocationModel &&
        other.id == id &&
        other.firebaseId == firebaseId &&
        other.userFirebaseUid == userFirebaseUid &&
        other.name == name &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.type == type &&
        other.notes == notes &&
        other.address == address &&
        other.isFavorite == isFavorite &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.lastSync == lastSync;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      firebaseId,
      userFirebaseUid,
      name,
      latitude,
      longitude,
      type,
      notes,
      address,
      isFavorite,
      createdAt,
      updatedAt,
      lastSync,
    );
  }
}
