/// Local user model for SQLite database
class UserModel {
  final int? id;
  final String firebaseUid;
  final String? email;
  final String? displayName;
  final String? photoUrl;
  final bool isPremium;
  final String? subscriptionType;
  final DateTime? subscriptionExpiry;
  final String language;
  final bool prayerNotifications;
  final bool locationSharing;
  final bool highContrast;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastSync;

  const UserModel({
    this.id,
    required this.firebaseUid,
    this.email,
    this.displayName,
    this.photoUrl,
    this.isPremium = false,
    this.subscriptionType,
    this.subscriptionExpiry,
    this.language = 'en',
    this.prayerNotifications = true,
    this.locationSharing = false,
    this.highContrast = false,
    required this.createdAt,
    required this.updatedAt,
    this.lastSync,
  });

  /// Create UserModel from database map
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] as int?,
      firebaseUid: map['firebase_uid'] as String,
      email: map['email'] as String?,
      displayName: map['display_name'] as String?,
      photoUrl: map['photo_url'] as String?,
      isPremium: (map['is_premium'] as int) == 1,
      subscriptionType: map['subscription_type'] as String?,
      subscriptionExpiry: map['subscription_expiry'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['subscription_expiry'] as int)
          : null,
      language: map['language'] as String? ?? 'en',
      prayerNotifications: (map['prayer_notifications'] as int? ?? 1) == 1,
      locationSharing: (map['location_sharing'] as int? ?? 0) == 1,
      highContrast: (map['high_contrast'] as int? ?? 0) == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
      lastSync: map['last_sync'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['last_sync'] as int)
          : null,
    );
  }

  /// Convert UserModel to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'firebase_uid': firebaseUid,
      'email': email,
      'display_name': displayName,
      'photo_url': photoUrl,
      'is_premium': isPremium ? 1 : 0,
      'subscription_type': subscriptionType,
      'subscription_expiry': subscriptionExpiry?.millisecondsSinceEpoch,
      'language': language,
      'prayer_notifications': prayerNotifications ? 1 : 0,
      'location_sharing': locationSharing ? 1 : 0,
      'high_contrast': highContrast ? 1 : 0,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'last_sync': lastSync?.millisecondsSinceEpoch,
    };
  }

  /// Create a copy with updated fields
  UserModel copyWith({
    int? id,
    String? firebaseUid,
    String? email,
    String? displayName,
    String? photoUrl,
    bool? isPremium,
    String? subscriptionType,
    DateTime? subscriptionExpiry,
    String? language,
    bool? prayerNotifications,
    bool? locationSharing,
    bool? highContrast,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastSync,
  }) {
    return UserModel(
      id: id ?? this.id,
      firebaseUid: firebaseUid ?? this.firebaseUid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoUrl: photoUrl ?? this.photoUrl,
      isPremium: isPremium ?? this.isPremium,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      subscriptionExpiry: subscriptionExpiry ?? this.subscriptionExpiry,
      language: language ?? this.language,
      prayerNotifications: prayerNotifications ?? this.prayerNotifications,
      locationSharing: locationSharing ?? this.locationSharing,
      highContrast: highContrast ?? this.highContrast,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastSync: lastSync ?? this.lastSync,
    );
  }

  @override
  String toString() {
    return 'UserModel{id: $id, firebaseUid: $firebaseUid, email: $email, '
        'displayName: $displayName, isPremium: $isPremium, language: $language}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel &&
        other.id == id &&
        other.firebaseUid == firebaseUid &&
        other.email == email &&
        other.displayName == displayName &&
        other.photoUrl == photoUrl &&
        other.isPremium == isPremium &&
        other.subscriptionType == subscriptionType &&
        other.subscriptionExpiry == subscriptionExpiry &&
        other.language == language &&
        other.prayerNotifications == prayerNotifications &&
        other.locationSharing == locationSharing &&
        other.highContrast == highContrast &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.lastSync == lastSync;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      firebaseUid,
      email,
      displayName,
      photoUrl,
      isPremium,
      subscriptionType,
      subscriptionExpiry,
      language,
      prayerNotifications,
      locationSharing,
      highContrast,
      createdAt,
      updatedAt,
      lastSync,
    );
  }
}
