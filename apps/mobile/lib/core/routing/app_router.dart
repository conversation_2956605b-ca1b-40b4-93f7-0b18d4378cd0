import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/home/<USER>/pages/home_page.dart';
import '../../features/prayer_times/presentation/pages/prayer_times_page.dart';
import '../../features/knowledge_hub/presentation/pages/knowledge_hub_page.dart';
import '../../features/friday_sermon/presentation/pages/friday_sermon_page.dart';
import '../../features/historical_places/presentation/pages/historical_places_page.dart';
import '../../shared/widgets/islamic_themed/islamic_bottom_nav.dart';

/// Route paths for the application
class AppRoutes {
  static const String home = '/';
  static const String prayerTimes = '/prayer-times';
  static const String knowledgeHub = '/knowledge-hub';
  static const String fridaySermon = '/friday-sermon';
  static const String historicalPlaces = '/historical-places';
  
  // Sub-routes for future implementation
  static const String hajjGuide = '/knowledge-hub/hajj-guide';
  static const String umrahGuide = '/knowledge-hub/umrah-guide';
  static const String manualCounter = '/knowledge-hub/manual-counter';
  static const String gpsCounter = '/knowledge-hub/gps-counter'; // Premium
  static const String qiblaDirection = '/prayer-times/qibla';
  static const String hijriCalendar = '/prayer-times/hijri-calendar';
  static const String sermonPlayer = '/friday-sermon/player'; // Premium
  static const String placeDetails = '/historical-places/details';
}

/// Main shell route that provides the bottom navigation structure
class ShellRouteData {
  static final GlobalKey<NavigatorState> _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> _shellNavigatorKey = GlobalKey<NavigatorState>();
  
  static GlobalKey<NavigatorState> get rootNavigatorKey => _rootNavigatorKey;
  static GlobalKey<NavigatorState> get shellNavigatorKey => _shellNavigatorKey;
}

/// App router configuration with go_router
class AppRouter {
  static final GoRouter _router = GoRouter(
    navigatorKey: ShellRouteData.rootNavigatorKey,
    initialLocation: AppRoutes.home,
    debugLogDiagnostics: true,
    routes: [
      ShellRoute(
        navigatorKey: ShellRouteData.shellNavigatorKey,
        builder: (context, state, child) {
          return MainShell(child: child);
        },
        routes: [
          GoRoute(
            path: AppRoutes.home,
            name: 'home',
            builder: (context, state) => const HomePage(),
          ),
          GoRoute(
            path: AppRoutes.prayerTimes,
            name: 'prayer-times',
            builder: (context, state) => const PrayerTimesPage(),
            routes: [
              GoRoute(
                path: 'qibla',
                name: 'qibla-direction',
                builder: (context, state) => const QiblaPlaceholderPage(),
              ),
              GoRoute(
                path: 'hijri-calendar',
                name: 'hijri-calendar',
                builder: (context, state) => const HijriCalendarPlaceholderPage(),
              ),
            ],
          ),
          GoRoute(
            path: AppRoutes.knowledgeHub,
            name: 'knowledge-hub',
            builder: (context, state) => const KnowledgeHubPage(),
            routes: [
              GoRoute(
                path: 'hajj-guide',
                name: 'hajj-guide',
                builder: (context, state) => const HajjGuidePlaceholderPage(),
              ),
              GoRoute(
                path: 'umrah-guide',
                name: 'umrah-guide',
                builder: (context, state) => const UmrahGuidePlaceholderPage(),
              ),
              GoRoute(
                path: 'manual-counter',
                name: 'manual-counter',
                builder: (context, state) => const ManualCounterPlaceholderPage(),
              ),
              GoRoute(
                path: 'gps-counter',
                name: 'gps-counter',
                builder: (context, state) => const GpsCounterPlaceholderPage(),
                redirect: (context, state) {
                  // Route guard for premium feature
                  // TODO: Check premium subscription status
                  // For now, allow access for development
                  return null;
                },
              ),
            ],
          ),
          GoRoute(
            path: AppRoutes.fridaySermon,
            name: 'friday-sermon',
            builder: (context, state) => const FridaySermonPage(),
            redirect: (context, state) {
              // Route guard for premium feature
              // TODO: Check premium subscription status
              // For now, allow access for development
              return null;
            },
            routes: [
              GoRoute(
                path: 'player',
                name: 'sermon-player',
                builder: (context, state) => const SermonPlayerPlaceholderPage(),
                redirect: (context, state) {
                  // Route guard for premium feature
                  return null;
                },
              ),
            ],
          ),
          GoRoute(
            path: AppRoutes.historicalPlaces,
            name: 'historical-places',
            builder: (context, state) => const HistoricalPlacesPage(),
            routes: [
              GoRoute(
                path: 'details/:placeId',
                name: 'place-details',
                builder: (context, state) {
                  final placeId = state.pathParameters['placeId'] ?? '';
                  return PlaceDetailsPlaceholderPage(placeId: placeId);
                },
              ),
            ],
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => ErrorPage(error: state.error.toString()),
  );

  static GoRouter get router => _router;
  
  /// Navigate to a specific tab by index
  static void navigateToTab(BuildContext context, int tabIndex) {
    switch (tabIndex) {
      case 0:
        context.go(AppRoutes.home);
        break;
      case 1:
        context.go(AppRoutes.prayerTimes);
        break;
      case 2:
        context.go(AppRoutes.knowledgeHub);
        break;
      case 3:
        context.go(AppRoutes.fridaySermon);
        break;
      case 4:
        context.go(AppRoutes.historicalPlaces);
        break;
    }
  }
  
  /// Get current tab index from route
  static int getCurrentTabIndex(String location) {
    if (location.startsWith(AppRoutes.prayerTimes)) return 1;
    if (location.startsWith(AppRoutes.knowledgeHub)) return 2;
    if (location.startsWith(AppRoutes.fridaySermon)) return 3;
    if (location.startsWith(AppRoutes.historicalPlaces)) return 4;
    return 0; // Default to home
  }
}

/// Main shell widget that provides the bottom navigation structure
class MainShell extends StatelessWidget {
  final Widget child;
  
  const MainShell({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    final location = GoRouterState.of(context).uri.toString();
    final currentIndex = AppRouter.getCurrentTabIndex(location);
    
    return Scaffold(
      body: child,
      bottomNavigationBar: IslamicBottomNavigation(
        currentIndex: currentIndex,
        onTabChanged: (index) => AppRouter.navigateToTab(context, index),
      ),
    );
  }
}

/// Placeholder pages for sub-routes (to be implemented in future stories)

class QiblaPlaceholderPage extends StatelessWidget {
  const QiblaPlaceholderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Qibla Direction')),
      body: const Center(
        child: Text('Qibla Direction feature coming soon'),
      ),
    );
  }
}

class HijriCalendarPlaceholderPage extends StatelessWidget {
  const HijriCalendarPlaceholderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Hijri Calendar')),
      body: const Center(
        child: Text('Hijri Calendar feature coming soon'),
      ),
    );
  }
}

class HajjGuidePlaceholderPage extends StatelessWidget {
  const HajjGuidePlaceholderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Hajj Guide')),
      body: const Center(
        child: Text('Hajj Guide feature coming soon'),
      ),
    );
  }
}

class UmrahGuidePlaceholderPage extends StatelessWidget {
  const UmrahGuidePlaceholderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Umrah Guide')),
      body: const Center(
        child: Text('Umrah Guide feature coming soon'),
      ),
    );
  }
}

class ManualCounterPlaceholderPage extends StatelessWidget {
  const ManualCounterPlaceholderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Manual Counter')),
      body: const Center(
        child: Text('Manual Counter feature coming soon'),
      ),
    );
  }
}

class GpsCounterPlaceholderPage extends StatelessWidget {
  const GpsCounterPlaceholderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('GPS Counter - Premium')),
      body: const Center(
        child: Text('GPS Counter premium feature coming soon'),
      ),
    );
  }
}

class SermonPlayerPlaceholderPage extends StatelessWidget {
  const SermonPlayerPlaceholderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Sermon Player - Premium')),
      body: const Center(
        child: Text('Sermon Player premium feature coming soon'),
      ),
    );
  }
}

class PlaceDetailsPlaceholderPage extends StatelessWidget {
  final String placeId;

  const PlaceDetailsPlaceholderPage({super.key, required this.placeId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Place Details: $placeId')),
      body: Center(
        child: Text('Place details for $placeId coming soon'),
      ),
    );
  }
}

class ErrorPage extends StatelessWidget {
  final String error;

  const ErrorPage({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            const Text('Something went wrong'),
            const SizedBox(height: 8),
            Text(error, textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.home),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    );
  }
}
