import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter/foundation.dart';
import '../../firebase_options.dart';

/// Firebase service for initializing and managing Firebase services
class FirebaseService {
  static FirebaseService? _instance;
  static FirebaseService get instance => _instance ??= FirebaseService._();
  
  FirebaseService._();
  
  // Firebase service instances
  late FirebaseAuth _auth;
  late FirebaseFirestore _firestore;
  late FirebaseStorage _storage;
  late FirebaseAnalytics _analytics;
  late FirebaseCrashlytics _crashlytics;
  late GoogleSignIn _googleSignIn;
  
  // Getters for Firebase services
  FirebaseAuth get auth => _auth;
  FirebaseFirestore get firestore => _firestore;
  FirebaseStorage get storage => _storage;
  FirebaseAnalytics get analytics => _analytics;
  FirebaseCrashlytics get crashlytics => _crashlytics;
  GoogleSignIn get googleSignIn => _googleSignIn;
  
  /// Initialize Firebase and all services
  Future<void> initialize() async {
    try {
      // Initialize Firebase Core
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      
      // Initialize Firebase services
      _auth = FirebaseAuth.instance;
      _firestore = FirebaseFirestore.instance;
      _storage = FirebaseStorage.instance;
      _analytics = FirebaseAnalytics.instance;
      _crashlytics = FirebaseCrashlytics.instance;
      
      // Initialize Google Sign-In
      _googleSignIn = GoogleSignIn(
        scopes: ['email', 'profile'],
      );
      
      // Configure Firestore settings for offline persistence
      await _configureFirestore();
      
      // Configure Crashlytics
      await _configureCrashlytics();
      
      // Configure Analytics
      await _configureAnalytics();
      
      print('Firebase services initialized successfully');
    } catch (e) {
      print('Error initializing Firebase: $e');
      rethrow;
    }
  }
  
  /// Configure Firestore settings
  Future<void> _configureFirestore() async {
    try {
      // Enable offline persistence for Islamic data sensitivity
      // This ensures prayer times and other critical data work offline
      _firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );
      
      // Set up Firestore security rules (to be implemented on server)
      // Rules will enforce:
      // - User can only access their own data
      // - Premium features require valid subscription
      // - Location data is not tracked during prayer times
      
    } catch (e) {
      print('Error configuring Firestore: $e');
    }
  }
  
  /// Configure Crashlytics
  Future<void> _configureCrashlytics() async {
    try {
      // Enable Crashlytics collection
      await _crashlytics.setCrashlyticsCollectionEnabled(true);
      
      // Set user identifier for crash reports (privacy-compliant)
      final user = _auth.currentUser;
      if (user != null) {
        await _crashlytics.setUserIdentifier(user.uid);
      }
      
      // Set custom keys for Islamic app context
      await _crashlytics.setCustomKey('app_type', 'islamic_companion');
      await _crashlytics.setCustomKey('privacy_mode', 'high');
      
    } catch (e) {
      print('Error configuring Crashlytics: $e');
    }
  }
  
  /// Configure Analytics
  Future<void> _configureAnalytics() async {
    try {
      // Enable Analytics collection with privacy considerations
      await _analytics.setAnalyticsCollectionEnabled(true);
      
      // Set user properties (privacy-compliant)
      await _analytics.setUserProperty(
        name: 'app_type',
        value: 'islamic_companion',
      );
      
      // Log app initialization
      await _analytics.logEvent(
        name: 'app_initialized',
        parameters: {
          'platform': defaultTargetPlatform.name,
          'version': '1.0.0',
        },
      );
      
    } catch (e) {
      print('Error configuring Analytics: $e');
    }
  }
  
  /// Sign in with Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        // User cancelled the sign-in
        return null;
      }
      
      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      
      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );
      
      // Sign in to Firebase with the Google credential
      final userCredential = await _auth.signInWithCredential(credential);
      
      // Log successful sign-in
      await _analytics.logLogin(loginMethod: 'google');
      
      // Update Crashlytics user identifier
      if (userCredential.user != null) {
        await _crashlytics.setUserIdentifier(userCredential.user!.uid);
      }
      
      return userCredential;
      
    } catch (e) {
      print('Error signing in with Google: $e');
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }
  
  /// Sign out
  Future<void> signOut() async {
    try {
      await Future.wait([
        _auth.signOut(),
        _googleSignIn.signOut(),
      ]);
      
      // Log sign out
      await _analytics.logEvent(name: 'user_signed_out');
      
      // Clear Crashlytics user identifier
      await _crashlytics.setUserIdentifier('');
      
    } catch (e) {
      print('Error signing out: $e');
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }
  
  /// Get current user
  User? get currentUser => _auth.currentUser;
  
  /// Check if user is signed in
  bool get isSignedIn => _auth.currentUser != null;
  
  /// Listen to authentication state changes
  Stream<User?> get authStateChanges => _auth.authStateChanges();
  
  /// Create user document in Firestore
  Future<void> createUserDocument(User user) async {
    try {
      final userDoc = _firestore.collection(FirebaseConfig.usersCollection).doc(user.uid);
      
      // Check if user document already exists
      final docSnapshot = await userDoc.get();
      if (docSnapshot.exists) {
        return; // User document already exists
      }
      
      // Create new user document
      await userDoc.set({
        'uid': user.uid,
        'email': user.email,
        'displayName': user.displayName,
        'photoURL': user.photoURL,
        'createdAt': FieldValue.serverTimestamp(),
        'lastSignIn': FieldValue.serverTimestamp(),
        'isPremium': false,
        'subscriptionType': null,
        'subscriptionExpiry': null,
        'preferences': {
          'language': 'en',
          'prayerNotifications': true,
          'locationSharing': false, // Default to privacy-first
          'highContrast': false,
        },
      });
      
      // Log user creation
      await _analytics.logSignUp(signUpMethod: 'google');
      
    } catch (e) {
      print('Error creating user document: $e');
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }
  
  /// Update user last sign-in time
  Future<void> updateUserLastSignIn(String uid) async {
    try {
      await _firestore
          .collection(FirebaseConfig.usersCollection)
          .doc(uid)
          .update({
        'lastSignIn': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('Error updating user last sign-in: $e');
      await _crashlytics.recordError(e, null);
    }
  }
  
  /// Log custom event for Islamic app features
  Future<void> logIslamicEvent(String eventName, Map<String, dynamic> parameters) async {
    try {
      await _analytics.logEvent(
        name: eventName,
        parameters: parameters,
      );
    } catch (e) {
      print('Error logging Islamic event: $e');
    }
  }
}
