import 'package:flutter/material.dart';
import 'shared/themes/islamic_theme.dart';
import 'core/routing/app_router.dart';

void main() {
  runApp(const <PERSON>iarahApp());
}

class ZiarahApp extends StatelessWidget {
  const ZiarahApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'Ziarah - Islamic Pilgrimage Companion',
      theme: IslamicTheme.lightTheme,
      darkTheme: IslamicTheme.darkTheme,
      highContrastTheme: IslamicTheme.highContrastTheme,
      themeMode: ThemeMode.system,
      routerConfig: AppRouter.router,
    );
  }
}


