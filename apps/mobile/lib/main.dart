import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'shared/themes/islamic_theme.dart';
import 'core/routing/app_router.dart';
import 'core/services/firebase_service.dart';
import 'core/di/service_locator.dart';

void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations (portrait for Islamic app)
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Initialize Firebase services
  try {
    await FirebaseService.instance.initialize();
  } catch (e) {
    print('Failed to initialize Firebase: $e');
    // Continue without Firebase for development
  }

  // Initialize service locator (dependency injection)
  try {
    await ServiceLocatorConfig.initialize();
  } catch (e) {
    print('Failed to initialize service locator: $e');
    // This is critical, so we might want to show an error screen
  }

  runApp(const <PERSON><PERSON>hApp());
}

class ZiarahApp extends StatelessWidget {
  const <PERSON><PERSON>h<PERSON><PERSON>({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'Ziarah - Islamic Pilgrimage Companion',
      theme: IslamicTheme.lightTheme,
      darkTheme: IslamicTheme.darkTheme,
      highContrastTheme: IslamicTheme.highContrastTheme,
      themeMode: ThemeMode.system,
      routerConfig: AppRouter.router,
    );
  }
}


