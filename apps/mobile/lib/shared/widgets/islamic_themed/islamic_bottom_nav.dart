import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../themes/color_palette.dart';

/// Navigation events for bottom navigation
abstract class NavigationEvent {}

class NavigationTabChanged extends NavigationEvent {
  final int tabIndex;
  NavigationTabChanged(this.tabIndex);
}

/// Navigation states for bottom navigation
class NavigationState {
  final int currentIndex;
  
  const NavigationState({required this.currentIndex});
  
  NavigationState copyWith({int? currentIndex}) {
    return NavigationState(
      currentIndex: currentIndex ?? this.currentIndex,
    );
  }
}

/// BLoC for managing bottom navigation state
class NavigationBloc extends Bloc<NavigationEvent, NavigationState> {
  NavigationBloc() : super(const NavigationState(currentIndex: 0)) {
    on<NavigationTabChanged>((event, emit) {
      emit(state.copyWith(currentIndex: event.tabIndex));
    });
  }
}

/// Islamic-themed bottom navigation bar with 5 tabs
class IslamicBottomNavigation extends StatelessWidget {
  final Function(int) onTabChanged;
  final int currentIndex;
  
  const IslamicBottomNavigation({
    super.key,
    required this.onTabChanged,
    required this.currentIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).bottomNavigationBarTheme.backgroundColor,
        boxShadow: [
          BoxShadow(
            color: IslamicColorPalette.primaryBlue.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: onTabChanged,
        type: BottomNavigationBarType.fixed,
        backgroundColor: Colors.transparent,
        elevation: 0,
        selectedItemColor: IslamicColorPalette.primaryBlue,
        unselectedItemColor: IslamicColorPalette.textSecondary,
        selectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.home_outlined),
            activeIcon: const Icon(Icons.home),
            label: 'Home',
            tooltip: 'Home - Weather, News, and Quick Access',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.access_time_outlined),
            activeIcon: const Icon(Icons.access_time),
            label: 'Prayer Times',
            tooltip: 'Prayer Times - Salah schedule and Qibla direction',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.menu_book_outlined),
            activeIcon: const Icon(Icons.menu_book),
            label: 'Knowledge Hub',
            tooltip: 'Knowledge Hub - Hajj and Umrah guidance',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.play_circle_outline),
            activeIcon: const Icon(Icons.play_circle),
            label: 'Friday Sermon',
            tooltip: 'Friday Sermon - Audio sermons with captions',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.location_on_outlined),
            activeIcon: const Icon(Icons.location_on),
            label: 'Historical Places',
            tooltip: 'Historical Places - Islamic heritage sites',
          ),
        ],
      ),
    );
  }
}

/// Navigation wrapper that provides BLoC context
class NavigationWrapper extends StatelessWidget {
  final Widget child;
  
  const NavigationWrapper({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => NavigationBloc(),
      child: child,
    );
  }
}
