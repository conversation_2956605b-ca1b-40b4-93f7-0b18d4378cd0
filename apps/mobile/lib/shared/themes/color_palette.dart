import 'package:flutter/material.dart';

/// Islamic-appropriate color palette for Ziarah app
/// Following Material Design 3 color system with Islamic cultural considerations
class IslamicColorPalette {
  // Primary colors - Deep blues representing spirituality and peace
  static const Color primaryBlue = Color(0xFF1B4B8C); // Deep Islamic blue
  static const Color primaryBlueDark = Color(0xFF0F2A5A); // Darker shade for contrast
  static const Color primaryBlueLight = Color(0xFF4A7BC8); // Lighter shade for surfaces
  
  // Secondary colors - Gold representing Islamic art and architecture
  static const Color secondaryGold = Color(0xFFD4AF37); // Islamic gold
  static const Color secondaryGoldDark = Color(0xFFB8941F); // Darker gold
  static const Color secondaryGoldLight = Color(0xFFE6C866); // Lighter gold
  
  // Tertiary colors - Warm earth tones
  static const Color tertiaryBrown = Color(0xFF8B4513); // Warm brown
  static const Color tertiaryBrownDark = Color(0xFF654321); // Darker brown
  static const Color tertiaryBrownLight = Color(0xFFA0522D); // Lighter brown
  
  // Neutral colors - Clean whites and soft grays
  static const Color surfaceWhite = Color(0xFFFFFBF7); // Warm white
  static const Color surfaceGray = Color(0xFFF5F5F5); // Light gray
  static const Color surfaceGrayDark = Color(0xFFE0E0E0); // Medium gray
  
  // Error colors - Muted red for Islamic sensitivity
  static const Color errorRed = Color(0xFFB71C1C); // Deep red
  static const Color errorRedLight = Color(0xFFE57373); // Light red
  
  // Success colors - Green representing nature and growth
  static const Color successGreen = Color(0xFF2E7D32); // Deep green
  static const Color successGreenLight = Color(0xFF66BB6A); // Light green
  
  // Warning colors - Amber for attention
  static const Color warningAmber = Color(0xFFFF8F00); // Amber
  static const Color warningAmberLight = Color(0xFFFFB74D); // Light amber
  
  // Text colors
  static const Color textPrimary = Color(0xFF1A1A1A); // Almost black
  static const Color textSecondary = Color(0xFF666666); // Medium gray
  static const Color textTertiary = Color(0xFF999999); // Light gray
  static const Color textOnPrimary = Color(0xFFFFFFFF); // White on primary
  static const Color textOnSecondary = Color(0xFF1A1A1A); // Dark on secondary
  
  // Prayer time specific colors
  static const Color fajrBlue = Color(0xFF2C3E50); // Pre-dawn blue
  static const Color sunriseOrange = Color(0xFFE67E22); // Sunrise orange
  static const Color dhuhrYellow = Color(0xFFF39C12); // Midday yellow
  static const Color asrAmber = Color(0xFFD68910); // Afternoon amber
  static const Color maghribPurple = Color(0xFF8E44AD); // Sunset purple
  static const Color ishaIndigo = Color(0xFF34495E); // Night indigo
  
  // High contrast colors for accessibility (WCAG AA compliance)
  static const Color highContrastPrimary = Color(0xFF000080); // Navy blue
  static const Color highContrastSecondary = Color(0xFFFFD700); // Bright gold
  static const Color highContrastBackground = Color(0xFFFFFFFF); // Pure white
  static const Color highContrastSurface = Color(0xFFF8F9FA); // Off-white
  static const Color highContrastText = Color(0xFF000000); // Pure black
  
  // Gradient definitions for Islamic patterns
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryBlue, primaryBlueDark],
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondaryGold, secondaryGoldDark],
  );
  
  static const LinearGradient sunsetGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [maghribPurple, sunriseOrange, secondaryGold],
  );
  
  // Color scheme for Material Design 3
  static ColorScheme get lightColorScheme => ColorScheme.fromSeed(
    seedColor: primaryBlue,
    brightness: Brightness.light,
    primary: primaryBlue,
    onPrimary: textOnPrimary,
    secondary: secondaryGold,
    onSecondary: textOnSecondary,
    tertiary: tertiaryBrown,
    surface: surfaceWhite,
    onSurface: textPrimary,
    error: errorRed,
    onError: textOnPrimary,
  );
  
  static ColorScheme get darkColorScheme => ColorScheme.fromSeed(
    seedColor: primaryBlue,
    brightness: Brightness.dark,
    primary: primaryBlueLight,
    onPrimary: textPrimary,
    secondary: secondaryGoldLight,
    onSecondary: textPrimary,
    tertiary: tertiaryBrownLight,
    surface: Color(0xFF1A1A1A),
    onSurface: textOnPrimary,
    error: errorRedLight,
    onError: textPrimary,
  );
  
  // High contrast color scheme for accessibility
  static ColorScheme get highContrastColorScheme => ColorScheme.fromSeed(
    seedColor: highContrastPrimary,
    brightness: Brightness.light,
    primary: highContrastPrimary,
    onPrimary: highContrastBackground,
    secondary: highContrastSecondary,
    onSecondary: highContrastText,
    surface: highContrastBackground,
    onSurface: highContrastText,
    error: Color(0xFFCC0000),
    onError: highContrastBackground,
  );
}
