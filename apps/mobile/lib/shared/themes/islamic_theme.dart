import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'color_palette.dart';
import 'typography.dart';

/// Islamic-themed Material Design 3 configuration for Ziarah app
class IslamicTheme {
  // Private constructor to prevent instantiation
  IslamicTheme._();
  
  /// Light theme configuration
  static ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    colorScheme: IslamicColorPalette.lightColorScheme,
    textTheme: IslamicTypography.lightTextTheme,
    
    // App Bar Theme
    appBarTheme: AppBarTheme(
      backgroundColor: IslamicColorPalette.primaryBlue,
      foregroundColor: IslamicColorPalette.textOnPrimary,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: IslamicTypography.latinTitleLarge.copyWith(
        color: IslamicColorPalette.textOnPrimary,
        fontWeight: FontWeight.w600,
      ),
      systemOverlayStyle: SystemUiOverlayStyle.light,
    ),
    
    // Bottom Navigation Bar Theme
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: IslamicColorPalette.surfaceWhite,
      selectedItemColor: IslamicColorPalette.primaryBlue,
      unselectedItemColor: IslamicColorPalette.textSecondary,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      selectedLabelStyle: IslamicTypography.latinBodyMedium.copyWith(
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: IslamicTypography.latinBodyMedium,
    ),
    
    // Card Theme - using default for now
    // cardTheme: CardTheme(
    //   color: IslamicColorPalette.surfaceWhite,
    //   elevation: 2,
    //   shadowColor: IslamicColorPalette.primaryBlue.withOpacity(0.1),
    //   shape: RoundedRectangleBorder(
    //     borderRadius: BorderRadius.circular(12),
    //   ),
    // ),
    
    // Elevated Button Theme
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: IslamicColorPalette.primaryBlue,
        foregroundColor: IslamicColorPalette.textOnPrimary,
        elevation: 2,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: IslamicTypography.latinBodyMedium.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    ),
    
    // Text Button Theme
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: IslamicColorPalette.primaryBlue,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: IslamicTypography.latinBodyMedium.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    ),
    
    // Outlined Button Theme
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: IslamicColorPalette.primaryBlue,
        side: const BorderSide(
          color: IslamicColorPalette.primaryBlue,
          width: 1.5,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: IslamicTypography.latinBodyMedium.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    ),
    
    // Input Decoration Theme
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: IslamicColorPalette.surfaceGray,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(
          color: IslamicColorPalette.surfaceGrayDark,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(
          color: IslamicColorPalette.surfaceGrayDark,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(
          color: IslamicColorPalette.primaryBlue,
          width: 2,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(
          color: IslamicColorPalette.errorRed,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      labelStyle: IslamicTypography.latinBodyMedium.copyWith(
        color: IslamicColorPalette.textSecondary,
      ),
      hintStyle: IslamicTypography.latinBodyMedium.copyWith(
        color: IslamicColorPalette.textTertiary,
      ),
    ),
    
    // Floating Action Button Theme
    floatingActionButtonTheme: FloatingActionButtonThemeData(
      backgroundColor: IslamicColorPalette.secondaryGold,
      foregroundColor: IslamicColorPalette.textOnSecondary,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    ),
    
    // Divider Theme
    dividerTheme: const DividerThemeData(
      color: IslamicColorPalette.surfaceGrayDark,
      thickness: 1,
      space: 1,
    ),
    
    // Icon Theme
    iconTheme: const IconThemeData(
      color: IslamicColorPalette.textSecondary,
      size: 24,
    ),
    
    // Primary Icon Theme
    primaryIconTheme: const IconThemeData(
      color: IslamicColorPalette.textOnPrimary,
      size: 24,
    ),
  );
  
  /// Dark theme configuration
  static ThemeData get darkTheme => ThemeData(
    useMaterial3: true,
    colorScheme: IslamicColorPalette.darkColorScheme,
    textTheme: IslamicTypography.darkTextTheme,
    
    // App Bar Theme
    appBarTheme: AppBarTheme(
      backgroundColor: IslamicColorPalette.primaryBlueDark,
      foregroundColor: IslamicColorPalette.textOnPrimary,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: IslamicTypography.latinTitleLarge.copyWith(
        color: IslamicColorPalette.textOnPrimary,
        fontWeight: FontWeight.w600,
      ),
      systemOverlayStyle: SystemUiOverlayStyle.light,
    ),
    
    // Bottom Navigation Bar Theme
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: const Color(0xFF2A2A2A),
      selectedItemColor: IslamicColorPalette.primaryBlueLight,
      unselectedItemColor: IslamicColorPalette.textTertiary,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      selectedLabelStyle: IslamicTypography.latinBodyMedium.copyWith(
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: IslamicTypography.latinBodyMedium,
    ),
    
    // Card Theme - using default for now
    // cardTheme: CardTheme(
    //   color: const Color(0xFF2A2A2A),
    //   elevation: 4,
    //   shadowColor: Colors.black.withOpacity(0.3),
    //   shape: RoundedRectangleBorder(
    //     borderRadius: BorderRadius.circular(12),
    //   ),
    // ),
  );
  
  /// High contrast theme for accessibility (WCAG AA compliance)
  static ThemeData get highContrastTheme => ThemeData(
    useMaterial3: true,
    colorScheme: IslamicColorPalette.highContrastColorScheme,
    textTheme: IslamicTypography.highContrastTextTheme,
    
    // App Bar Theme with high contrast
    appBarTheme: AppBarTheme(
      backgroundColor: IslamicColorPalette.highContrastPrimary,
      foregroundColor: IslamicColorPalette.highContrastBackground,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: IslamicTypography.highContrastBody.copyWith(
        fontSize: IslamicTypography.titleLarge,
        color: IslamicColorPalette.highContrastBackground,
        fontWeight: FontWeight.w700,
      ),
      systemOverlayStyle: SystemUiOverlayStyle.light,
    ),
    
    // Bottom Navigation Bar Theme with high contrast
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: IslamicColorPalette.highContrastBackground,
      selectedItemColor: IslamicColorPalette.highContrastPrimary,
      unselectedItemColor: IslamicColorPalette.highContrastText,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      selectedLabelStyle: IslamicTypography.highContrastBody.copyWith(
        fontSize: IslamicTypography.bodyMedium,
        fontWeight: FontWeight.w700,
      ),
      unselectedLabelStyle: IslamicTypography.highContrastBody.copyWith(
        fontSize: IslamicTypography.bodyMedium,
        fontWeight: FontWeight.w600,
      ),
    ),
    
    // Enhanced contrast for buttons
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: IslamicColorPalette.highContrastPrimary,
        foregroundColor: IslamicColorPalette.highContrastBackground,
        elevation: 4,
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: const BorderSide(
            color: IslamicColorPalette.highContrastText,
            width: 2,
          ),
        ),
        textStyle: IslamicTypography.highContrastBody.copyWith(
          fontSize: IslamicTypography.bodyMedium,
          fontWeight: FontWeight.w700,
        ),
      ),
    ),
  );
}
