import 'package:flutter/material.dart';

/// Typography configuration for Ziarah app
/// Supporting multiple scripts: Arabic, Latin, and Southeast Asian
class IslamicTypography {
  // Font families for different scripts
  static const String arabicFontFamily = 'Amiri'; // Traditional Arabic font
  static const String latinFontFamily = 'Inter'; // Modern Latin font
  static const String seAsianFontFamily = 'Noto Sans'; // Southeast Asian support
  
  // Base font sizes following Material Design 3
  static const double displayLarge = 57.0;
  static const double displayMedium = 45.0;
  static const double displaySmall = 36.0;
  static const double headlineLarge = 32.0;
  static const double headlineMedium = 28.0;
  static const double headlineSmall = 24.0;
  static const double titleLarge = 22.0;
  static const double titleMedium = 16.0;
  static const double titleSmall = 14.0;
  static const double labelLarge = 14.0;
  static const double labelMedium = 12.0;
  static const double labelSmall = 11.0;
  static const double bodyLarge = 16.0;
  static const double bodyMedium = 14.0;
  static const double bodySmall = 12.0;
  
  // Arabic text styles with proper RTL support
  static TextStyle get arabicDisplayLarge => TextStyle(
    fontFamily: arabicFontFamily,
    fontSize: displayLarge,
    fontWeight: FontWeight.w400,
    letterSpacing: -0.25,
    height: 1.12,
  );
  
  static TextStyle get arabicHeadlineLarge => TextStyle(
    fontFamily: arabicFontFamily,
    fontSize: headlineLarge,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.25,
  );
  
  static TextStyle get arabicTitleLarge => TextStyle(
    fontFamily: arabicFontFamily,
    fontSize: titleLarge,
    fontWeight: FontWeight.w500,
    letterSpacing: 0,
    height: 1.27,
  );
  
  static TextStyle get arabicBodyLarge => TextStyle(
    fontFamily: arabicFontFamily,
    fontSize: bodyLarge,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
    height: 1.5,
  );
  
  static TextStyle get arabicBodyMedium => TextStyle(
    fontFamily: arabicFontFamily,
    fontSize: bodyMedium,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
    height: 1.43,
  );
  
  // Latin text styles
  static TextStyle get latinDisplayLarge => TextStyle(
    fontFamily: latinFontFamily,
    fontSize: displayLarge,
    fontWeight: FontWeight.w400,
    letterSpacing: -0.25,
    height: 1.12,
  );
  
  static TextStyle get latinHeadlineLarge => TextStyle(
    fontFamily: latinFontFamily,
    fontSize: headlineLarge,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.25,
  );
  
  static TextStyle get latinTitleLarge => TextStyle(
    fontFamily: latinFontFamily,
    fontSize: titleLarge,
    fontWeight: FontWeight.w500,
    letterSpacing: 0,
    height: 1.27,
  );

  static TextStyle get latinTitleMedium => TextStyle(
    fontFamily: latinFontFamily,
    fontSize: titleMedium,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.15,
    height: 1.5,
  );

  static TextStyle get latinBodyLarge => TextStyle(
    fontFamily: latinFontFamily,
    fontSize: bodyLarge,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
    height: 1.5,
  );

  static TextStyle get latinBodyMedium => TextStyle(
    fontFamily: latinFontFamily,
    fontSize: bodyMedium,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
    height: 1.43,
  );

  static TextStyle get latinBodySmall => TextStyle(
    fontFamily: latinFontFamily,
    fontSize: bodySmall,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    height: 1.33,
  );
  
  // Prayer time specific typography
  static TextStyle get prayerTimeDisplay => TextStyle(
    fontFamily: latinFontFamily,
    fontSize: 32.0,
    fontWeight: FontWeight.w600,
    letterSpacing: -0.5,
    height: 1.2,
  );
  
  static TextStyle get prayerNameArabic => TextStyle(
    fontFamily: arabicFontFamily,
    fontSize: 18.0,
    fontWeight: FontWeight.w500,
    letterSpacing: 0,
    height: 1.4,
  );
  
  static TextStyle get prayerNameLatin => TextStyle(
    fontFamily: latinFontFamily,
    fontSize: 16.0,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.15,
    height: 1.4,
  );
  
  // High contrast typography for accessibility
  static TextStyle get highContrastDisplay => TextStyle(
    fontFamily: latinFontFamily,
    fontSize: displayLarge,
    fontWeight: FontWeight.w700, // Bolder for better contrast
    letterSpacing: -0.25,
    height: 1.12,
  );
  
  static TextStyle get highContrastBody => TextStyle(
    fontFamily: latinFontFamily,
    fontSize: bodyLarge + 2, // Slightly larger for readability
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
    height: 1.6, // Increased line height
  );
  
  // Material Design 3 Typography Theme
  static TextTheme get lightTextTheme => TextTheme(
    displayLarge: latinDisplayLarge,
    displayMedium: latinDisplayLarge.copyWith(fontSize: displayMedium),
    displaySmall: latinDisplayLarge.copyWith(fontSize: displaySmall),
    headlineLarge: latinHeadlineLarge,
    headlineMedium: latinHeadlineLarge.copyWith(fontSize: headlineMedium),
    headlineSmall: latinHeadlineLarge.copyWith(fontSize: headlineSmall),
    titleLarge: latinTitleLarge,
    titleMedium: latinTitleLarge.copyWith(fontSize: titleMedium),
    titleSmall: latinTitleLarge.copyWith(fontSize: titleSmall),
    bodyLarge: latinBodyLarge,
    bodyMedium: latinBodyMedium,
    bodySmall: latinBodyMedium.copyWith(fontSize: bodySmall),
    labelLarge: latinBodyMedium.copyWith(
      fontSize: labelLarge,
      fontWeight: FontWeight.w500,
    ),
    labelMedium: latinBodyMedium.copyWith(
      fontSize: labelMedium,
      fontWeight: FontWeight.w500,
    ),
    labelSmall: latinBodyMedium.copyWith(
      fontSize: labelSmall,
      fontWeight: FontWeight.w500,
    ),
  );
  
  static TextTheme get darkTextTheme => lightTextTheme;
  
  // High contrast text theme
  static TextTheme get highContrastTextTheme => TextTheme(
    displayLarge: highContrastDisplay,
    displayMedium: highContrastDisplay.copyWith(fontSize: displayMedium),
    displaySmall: highContrastDisplay.copyWith(fontSize: displaySmall),
    headlineLarge: highContrastDisplay.copyWith(fontSize: headlineLarge),
    headlineMedium: highContrastDisplay.copyWith(fontSize: headlineMedium),
    headlineSmall: highContrastDisplay.copyWith(fontSize: headlineSmall),
    titleLarge: highContrastBody.copyWith(fontSize: titleLarge),
    titleMedium: highContrastBody.copyWith(fontSize: titleMedium),
    titleSmall: highContrastBody.copyWith(fontSize: titleSmall),
    bodyLarge: highContrastBody,
    bodyMedium: highContrastBody.copyWith(fontSize: bodyMedium),
    bodySmall: highContrastBody.copyWith(fontSize: bodySmall),
    labelLarge: highContrastBody.copyWith(
      fontSize: labelLarge,
      fontWeight: FontWeight.w600,
    ),
    labelMedium: highContrastBody.copyWith(
      fontSize: labelMedium,
      fontWeight: FontWeight.w600,
    ),
    labelSmall: highContrastBody.copyWith(
      fontSize: labelSmall,
      fontWeight: FontWeight.w600,
    ),
  );
  
  // Utility method to get appropriate text style based on script
  static TextStyle getScriptAwareStyle(String text, TextStyle baseStyle) {
    // Simple heuristic to detect Arabic text
    final arabicRegex = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]');
    
    if (arabicRegex.hasMatch(text)) {
      return baseStyle.copyWith(fontFamily: arabicFontFamily);
    } else {
      return baseStyle.copyWith(fontFamily: latinFontFamily);
    }
  }
}
