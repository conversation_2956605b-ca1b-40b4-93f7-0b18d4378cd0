# Firebase Setup Instructions for Ziarah Islamic Companion App

This document provides step-by-step instructions for setting up Firebase for the Ziarah Islamic pilgrimage companion app.

## Prerequisites

1. Google account with access to Firebase Console
2. Flutter development environment set up
3. Android development environment configured

## Firebase Project Setup

### 1. Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name: `ziarah-islamic-companion`
4. Enable Google Analytics (recommended for user insights)
5. Choose or create a Google Analytics account
6. Click "Create project"

### 2. Register Android App

1. In Firebase Console, click "Add app" and select Android
2. Enter Android package name: `com.ziarah.mobile`
3. Enter app nickname: `Ziarah Mobile`
4. Enter SHA-1 certificate fingerprint (get from Android Studio or keystore)
5. Click "Register app"

### 3. Download Configuration File

1. Download `google-services.json`
2. Replace the placeholder file at `apps/mobile/android/app/google-services.json`

### 4. Update Firebase Options

1. Install FlutterFire CLI: `dart pub global activate flutterfire_cli`
2. Run: `flutterfire configure --project=ziarah-islamic-companion`
3. This will update `lib/firebase_options.dart` with real configuration

## Firebase Services Configuration

### 1. Authentication

1. In Firebase Console, go to Authentication > Sign-in method
2. Enable Google sign-in provider
3. Add your app's SHA-1 fingerprint
4. Configure OAuth consent screen in Google Cloud Console

### 2. Firestore Database

1. Go to Firestore Database
2. Click "Create database"
3. Choose "Start in test mode" (will be secured later)
4. Select a location close to your users (e.g., asia-southeast1)

#### Firestore Collections Structure:
```
users/
├── {userId}/
│   ├── uid: string
│   ├── email: string
│   ├── displayName: string
│   ├── photoURL: string
│   ├── createdAt: timestamp
│   ├── lastSignIn: timestamp
│   ├── isPremium: boolean
│   ├── subscriptionType: string
│   ├── subscriptionExpiry: timestamp
│   └── preferences: map

saved_locations/
├── {locationId}/
│   ├── userId: string
│   ├── name: string
│   ├── latitude: number
│   ├── longitude: number
│   ├── type: string (mosque, hotel, restaurant, etc.)
│   ├── notes: string
│   └── createdAt: timestamp

sermons/ (Premium feature)
├── {sermonId}/
│   ├── title: string
│   ├── speaker: string
│   ├── audioUrl: string
│   ├── captionsUrl: string
│   ├── duration: number
│   ├── language: string
│   └── uploadedAt: timestamp

historical_places/
├── {placeId}/
│   ├── name: string
│   ├── description: string
│   ├── latitude: number
│   ├── longitude: number
│   ├── images: array
│   ├── hadithReferences: array (Premium)
│   └── significance: string

family_groups/
├── {groupId}/
│   ├── name: string
│   ├── members: array
│   ├── createdBy: string
│   ├── createdAt: timestamp
│   └── sharedLocations: array
```

### 3. Storage

1. Go to Storage
2. Click "Get started"
3. Choose "Start in test mode"
4. Select same location as Firestore

#### Storage Structure:
```
user_profiles/
├── {userId}/
│   └── profile.jpg

place_images/
├── {placeId}/
│   ├── main.jpg
│   ├── gallery/
│   │   ├── 1.jpg
│   │   └── 2.jpg

sermon_audio/ (Premium)
├── {sermonId}/
│   ├── audio.mp3
│   └── captions.vtt
```

### 4. Analytics

1. Analytics is automatically enabled if configured during project creation
2. Custom events are logged for Islamic app features:
   - Prayer time notifications
   - Qibla direction usage
   - Knowledge hub interactions
   - Premium feature usage

### 5. Crashlytics

1. Go to Crashlytics
2. Click "Enable Crashlytics"
3. Follow setup instructions (already implemented in code)

## Security Rules

### Firestore Security Rules

Replace the default rules with:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Saved locations are private to each user
    match /saved_locations/{locationId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
    
    // Sermons are premium content
    match /sermons/{sermonId} {
      allow read: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isPremium == true;
    }
    
    // Historical places are publicly readable
    match /historical_places/{placeId} {
      allow read: if true;
      allow write: if false; // Only admin can write
    }
    
    // Family groups
    match /family_groups/{groupId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.members;
    }
  }
}
```

### Storage Security Rules

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // User profile images
    match /user_profiles/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Place images are publicly readable
    match /place_images/{allPaths=**} {
      allow read: if true;
      allow write: if false; // Only admin can write
    }
    
    // Sermon audio is premium content
    match /sermon_audio/{allPaths=**} {
      allow read: if request.auth != null && 
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.isPremium == true;
    }
  }
}
```

## Environment Variables

Create a `.env` file in the project root (not committed to version control):

```
FIREBASE_PROJECT_ID=ziarah-islamic-companion
FIREBASE_API_KEY=your_actual_api_key_here
GOOGLE_SIGN_IN_CLIENT_ID=your_google_client_id_here
```

## Testing Firebase Integration

1. Run the app: `flutter run`
2. Test Google Sign-In functionality
3. Verify Firestore data creation
4. Check Analytics events in Firebase Console
5. Test offline functionality

## Privacy and Islamic Considerations

1. **Location Privacy**: Location data is not tracked during prayer times
2. **Data Minimization**: Only essential data is collected
3. **User Consent**: Clear consent for data collection
4. **Offline First**: Critical Islamic data (prayer times) works offline
5. **Family Sharing**: Optional family group features with privacy controls

## Troubleshooting

### Common Issues:

1. **Google Sign-In fails**: Check SHA-1 fingerprint configuration
2. **Firestore permission denied**: Verify security rules
3. **Analytics not working**: Check if Analytics is enabled
4. **Crashlytics not reporting**: Ensure proper initialization

### Debug Commands:

```bash
# Check Firebase configuration
flutterfire configure

# Get SHA-1 fingerprint
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android

# Test Firestore connection
flutter run --debug
```

## Production Deployment

1. Generate release keystore
2. Add release SHA-1 to Firebase
3. Update security rules for production
4. Enable App Check for additional security
5. Set up monitoring and alerts

## Support

For Firebase-specific issues:
- [Firebase Documentation](https://firebase.google.com/docs)
- [FlutterFire Documentation](https://firebase.flutter.dev/)

For app-specific issues:
- Check the main project README
- Review the architecture documentation
