# Create Flutter project structure following unified-project-structure.md

# Feature directories
$features = @("home", "prayer_times", "knowledge_hub", "friday_sermon", "historical_places")
$layers = @("presentation", "domain", "data")
$presentation_dirs = @("pages", "widgets", "bloc")
$domain_dirs = @("entities", "repositories", "usecases")
$data_dirs = @("models", "datasources", "repositories")

foreach ($feature in $features) {
    foreach ($layer in $layers) {
        $base_path = "apps/mobile/lib/features/$feature/$layer"
        
        if ($layer -eq "presentation") {
            foreach ($dir in $presentation_dirs) {
                New-Item -ItemType Directory -Path "$base_path/$dir" -Force | Out-Null
            }
        }
        elseif ($layer -eq "domain") {
            foreach ($dir in $domain_dirs) {
                New-Item -ItemType Directory -Path "$base_path/$dir" -Force | Out-Null
            }
        }
        elseif ($layer -eq "data") {
            foreach ($dir in $data_dirs) {
                New-Item -ItemType Directory -Path "$base_path/$dir" -Force | Out-Null
            }
        }
    }
}

# Shared directories
New-Item -ItemType Directory -Path "apps/mobile/lib/shared/widgets/islamic_themed" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/lib/shared/widgets/premium" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/lib/shared/widgets/common" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/lib/shared/themes" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/lib/shared/constants" -Force | Out-Null

# Core directories
New-Item -ItemType Directory -Path "apps/mobile/lib/core/islamic" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/lib/core/location" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/lib/core/premium" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/lib/core/database" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/lib/core/network" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/lib/core/error" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/lib/core/utils" -Force | Out-Null

# Assets directories
New-Item -ItemType Directory -Path "apps/mobile/assets/images/islamic" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/assets/images/icons" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/assets/images/historical" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/assets/data" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/assets/audio/azan" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/assets/audio/duas" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/assets/fonts/arabic" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/assets/fonts/latin" -Force | Out-Null

# Test directories
New-Item -ItemType Directory -Path "apps/mobile/test/unit/features" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/test/unit/core" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/test/integration/islamic" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/test/integration/premium" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/test/widget/islamic_widgets" -Force | Out-Null
New-Item -ItemType Directory -Path "apps/mobile/test/widget/premium_widgets" -Force | Out-Null

Write-Host "Flutter project structure created successfully!"
